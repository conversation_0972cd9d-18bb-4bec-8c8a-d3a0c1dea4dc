.page-container {
  @apply w-full max-w-7xl mx-auto py-2;
}

.content-container {
  @apply bg-white rounded-lg shadow-sm !p-0;
}

.page-header {
  @apply flex justify-between items-center mb-4 pb-3 border-b border-gray-200;
}

.page-title {
  @apply text-2xl font-semibold text-[var(--gray-800)];
}

.back-button {
  @apply p-2 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors;
}

.i18n-management-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 1.5rem;
  /* background-color: #f8fafc; */
  min-height: 100vh;
}

.header-section {
  margin-bottom: 1.5rem;
}

.stats-card {
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  padding: 1rem;
  text-align: center;
  min-width: 100px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.language-selection {
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  padding: 1rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.language-selection .flex {
  align-items: flex-end;
}

.language-selection .btn {
  height: 42px; /* Match select height */
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
  min-width: auto;
}

.language-selection .flex.items-end {
  gap: 0.5rem;
}

.filters-section {
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  padding: 1rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.form-select {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  background-color: white;
  color: #374151;
  height: 42px;
}

.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  background-color: white;
  color: #374151;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  background-color: white;
  color: #374151;
  resize: none;
}

.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border: 1px solid transparent;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.2s;
  cursor: pointer;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn-primary {
  color: white;
  background-color: #3b82f6;
}

.btn-primary:hover {
  background-color: #2563eb;
}

.btn-primary:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

.btn-outline {
  color: #374151;
  background-color: white;
  border-color: #d1d5db;
}

.btn-outline:hover {
  background-color: #f9fafb;
}

.btn-outline:disabled {
  color: #9ca3af;
  cursor: not-allowed;
}

.btn-outline:disabled:hover {
  background-color: white;
}

.loading-state {
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  padding: 2rem;
}

.translations-table {
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.table-container {
  overflow-x: auto;
}

.table-container table {
  min-width: 100%;
  border-collapse: collapse;
}

.table-container th {
  background-color: #f9fafb;
  text-align: left;
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
}

.table-container td {
  white-space: nowrap;
  font-size: 0.875rem;
  color: #111827;
  padding: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
}

.empty-state {
  background-color: #f9fafb;
  padding: 3rem;
  text-align: center;
}

.footer-actions {
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  padding: 1.5rem;
}

/* Custom scrollbar for textarea */
.form-textarea::-webkit-scrollbar {
  width: 6px;
}

.form-textarea::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.form-textarea::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.form-textarea::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation for modified rows */
.bg-yellow-50 {
  transition: background-color 0.2s ease-in-out;
}

/* Flag icons spacing */
.fi {
  margin-right: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .i18n-management-container {
    padding: 1rem;
  }

  .header-section .flex {
    flex-direction: column;
    gap: 1rem;
  }

  .language-selection .flex {
    flex-direction: column;
    align-items: stretch !important;
    gap: 1rem;
  }

  .language-selection .btn {
    height: auto;
    padding: 0.75rem 1rem;
  }

  .filters-section .flex {
    flex-direction: column;
    gap: 0.75rem;
  }

  .footer-actions .flex {
    flex-direction: column;
    gap: 0.75rem;
  }

  .table-container {
    font-size: 0.875rem;
  }

  .stats-card {
    min-width: 80px;
    padding: 0.5rem 0.75rem;
  }
}

/* Utility classes */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.items-end {
  align-items: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-x-3 > * + * {
  margin-left: 0.75rem;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}

.font-medium {
  font-weight: 500;
}

.text-gray-600 {
  color: #4b5563;
}

.text-gray-500 {
  color: #6b7280;
}

.text-blue-600 {
  color: #2563eb;
}

.bg-yellow-50 {
  background-color: #fffbeb;
  transition: background-color 0.2s ease-in-out;
}

.w-full {
  width: 100%;
}

.h-20 {
  height: 5rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.mr-1 {
  margin-right: 0.25rem;
}

.ml-4 {
  margin-left: 1rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.pl-10 {
  padding-left: 2.5rem;
}

.w-48 {
  width: 12rem;
}

.w-20 {
  width: 5rem;
}

.w-1\/3 {
  width: 33.333333%;
}

.flex-1 {
  flex: 1 1 0%;
}

.min-h-\[60px\] {
  min-height: 60px;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.font-semibold {
  font-weight: 600;
}

.font-mono {
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

.text-gray-900 {
  color: #111827;
}

.text-gray-800 {
  color: #1f2937;
}

.text-gray-700 {
  color: #374151;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-gray-300 {
  color: #d1d5db;
}

.text-blue-500 {
  color: #3b82f6;
}

.text-orange-500 {
  color: #f97316;
}

.text-orange-600 {
  color: #ea580c;
}

.text-orange-800 {
  color: #9a3412;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.bg-gray-100 {
  background-color: #f3f4f6;
}

.bg-orange-50 {
  background-color: #fff7ed;
}

.bg-orange-100 {
  background-color: #ffedd5;
}

.border-gray-200 {
  border-color: #e5e7eb;
}

.border-gray-100 {
  border-color: #f3f4f6;
}

.border-orange-300 {
  border-color: #fdba74;
}

.border-b {
  border-bottom-width: 1px;
}

.border-t {
  border-top-width: 1px;
}

.hover\:bg-gray-50:hover {
  background-color: #f9fafb;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.inline-flex {
  display: inline-flex;
}

.rounded-full {
  border-radius: 9999px;
}

.resize-y {
  resize: vertical;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.left-3 {
  left: 0.75rem;
}

.top-1\/2 {
  top: 50%;
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

/* Language Dropdown Styles */
.language-dropdown-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: white;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
  height: 42px;
}

.language-dropdown-button:hover {
  border-color: #9ca3af;
}

.language-dropdown-button:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.language-dropdown-button:disabled {
  background-color: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

.language-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 50;
  margin-top: 0.25rem;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  max-height: 200px;
  overflow-y: auto;
}

.language-dropdown-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 0.875rem;
  color: #374151;
}

.language-dropdown-item:hover {
  background-color: #f9fafb;
}

.language-dropdown-item.selected {
  background-color: #eff6ff;
  color: #1d4ed8;
}

.language-dropdown-empty {
  padding: 0.75rem;
  text-align: center;
  color: #6b7280;
  font-size: 0.875rem;
  font-style: italic;
}

.flag-icon {
  width: 20px;
  height: 15px;
  margin-right: 0.5rem;
  border-radius: 2px;
  flex-shrink: 0;
}

.rotate-180 {
  transform: rotate(180deg);
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.duration-200 {
  transition-duration: 200ms;
}

/* Tab Navigation Styles */
.tab-navigation {
  margin-bottom: 2rem;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  border-bottom: 2px solid transparent;
  background: none;
  border-top: none;
  border-left: none;
  border-right: none;
  cursor: pointer;
  transition: all 0.2s;
}

.tab-button:hover {
  color: #374151;
  border-bottom-color: #d1d5db;
}

.tab-button.active {
  color: #2563eb;
  border-bottom-color: #2563eb;
}

.tab-content {
  min-height: 400px;
}

/* Language Management Styles */
.languages-tab {
  padding: 1rem 0;
}

.section-header {
  margin-bottom: 1rem;
}

.language-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 0.75rem;
  max-height: 400px;
  overflow-y: auto;
  padding: 0.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  background-color: #f9fafb;
}

.language-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
}

.language-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.language-card.selected {
  border-color: #10b981;
  background-color: #ecfdf5;
}

.language-card.custom {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.language-card.custom.selected {
  border-color: #10b981;
  background-color: #ecfdf5;
}

.language-card.is-default {
  border-color: #3b82f6;
  background-color: #eff6ff;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.language-card.inactive {
  opacity: 0.5;
}

.language-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.language-details {
  display: flex;
  flex-direction: column;
}

.language-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #111827;
}

.language-code {
  font-size: 0.75rem;
  color: #6b7280;
}

.language-description {
  font-size: 0.75rem;
  color: #9ca3af;
  margin-top: 0.25rem;
}

.default-indicator {
  margin-top: 0.25rem;
}

.language-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-icon {
  padding: 0.25rem;
  color: #6b7280;
  background: none;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: color 0.2s;
}

.btn-icon:hover {
  color: #374151;
}

.btn-icon.text-red-600 {
  color: #dc2626;
}

.btn-icon.text-red-600:hover {
  color: #b91c1c;
}

/* Custom Language List */
.custom-language-list {
  max-height: 400px;
  overflow-y: auto;
  padding: 0.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  background-color: #f9fafb;
}

.custom-language-list .language-card {
  margin-bottom: 0.75rem;
}

.custom-language-list .language-card:last-child {
  margin-bottom: 0;
}

/* Language Settings Section */
.language-settings-section {
  margin-top: 2rem;
}

.selected-languages-summary {
  margin-bottom: 1rem;
}

.selected-languages-summary .flag-icon {
  width: 1rem;
  height: 1rem;
  border-radius: 0.125rem;
}

.default-language-selection {
  margin-bottom: 1rem;
}

.default-language-selection button {
  transition: all 0.2s ease-in-out;
}

.default-language-selection button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.settings-actions {
  margin-top: 1rem;
}

.settings-actions .btn {
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.settings-actions .btn:hover {
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-container {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.close-button {
  padding: 0.25rem;
  color: #6b7280;
  background: none;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: color 0.2s;
}

.close-button:hover {
  color: #374151;
}

.modal-content {
  padding: 1.5rem;
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

/* Form Styles */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-help {
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #6b7280;
}

.form-checkbox {
  width: 1rem;
  height: 1rem;
  color: #3b82f6;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
}

.form-checkbox:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Button Variants */
.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
}

.btn-secondary {
  color: #374151;
  background-color: white;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background-color: #f9fafb;
}

.btn-secondary:disabled {
  color: #9ca3af;
  cursor: not-allowed;
}

/* Grid Layout */
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.gap-6 {
  gap: 1.5rem;
}

.max-w-2xl {
  max-width: 42rem;
}

/* Responsive Design */
@media (min-width: 1024px) {
  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (max-width: 768px) {
  .tab-navigation nav {
    display: flex;
    flex-direction: column;
    gap: 0;
  }

  .tab-button {
    border-bottom: none;
    border-left: 4px solid transparent;
    padding-left: 1rem;
    justify-content: flex-start;
  }

  .tab-button.active {
    border-bottom: none;
    border-left-color: #2563eb;
  }

  .language-grid {
    grid-template-columns: 1fr;
  }

  .modal-container {
    margin: 1rem;
    max-width: calc(100% - 2rem);
  }

  .selected-language-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .selected-language-item .language-actions {
    align-self: stretch;
    justify-content: flex-end;
  }
}
