import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconsModule } from '../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { brandColors } from '../../../icons/icons.font-awesome-brands';
import { IntegrationsService } from '../../../core/services/integrations.service';
import { IntegrationPosition, IntegrationRes } from '../../../model/response/integration-res.model';

@Component({
  selector: 'app-floating-contact-buttons',
  standalone: true,
  imports: [CommonModule, IconsModule, TranslateModule],
  templateUrl: './floating-contact-buttons.component.html',
  styleUrls: ['./floating-contact-buttons.component.css']
})
export class FloatingContactButtonsComponent implements OnInit {
  brandColors = brandColors;

  // Integration data from API
  leftIntegrations: IntegrationRes[] = [];
  rightIntegrations: IntegrationRes[] = [];
  loading: boolean = false;

  constructor(
    private integrationsService: IntegrationsService
  ) {}

  ngOnInit(): void {
    this.loadIntegrations();
  }

  loadIntegrations(): void {
    this.loading = true;
    this.integrationsService.getForUser().subscribe({
      next: (integrations) => {
        // Filter integrations by position
        this.leftIntegrations = integrations.filter(
          integration => integration.active && integration.position === IntegrationPosition.LEFT
        );
        this.rightIntegrations = integrations.filter(
          integration => integration.active && integration.position === IntegrationPosition.RIGHT
        );
        this.loading = false;
      },
      error: (error: any) => {
        console.error('Error loading integrations:', error);
        this.loading = false;
      }
    });
  }

  getIntegrationIcon(integration: IntegrationRes): any {
    // Check if integration has a custom icon
    if (integration.icon && this.isImageUrl(integration.icon)) {
      return null; // Will be handled by template to show image
    }

    // Check if it's a FontAwesome icon
    if (integration.icon && !this.isImageUrl(integration.icon)) {
      return ['fab', integration.icon];
    }

    // Default icons for known types
    switch (integration.key.toLowerCase()) {
      case 'telegram':
        return ['fab', 'telegram-plane'];
      case 'whatsapp':
        return ['fab', 'whatsapp'];
      default:
        return ['fab', integration.key.toLowerCase()];
    }
  }

  // Check if a value is an image URL
  isImageUrl(value: string): boolean {
    if (!value) return false;

    const valueStr = value.toString();

    // Check if it's a URL (contains http/https or starts with /)
    const isUrl = valueStr.startsWith('http') || valueStr.startsWith('https') || valueStr.startsWith('/');

    // Check if it has image extension
    const hasImageExtension = /\.(png|jpg|jpeg|gif|svg|webp)(\?.*)?$/i.test(valueStr);

    // Check if it's a CDN URL (more comprehensive patterns)
    const isCdnUrl = valueStr.includes('/cdn/') ||
                     valueStr.includes('/api/image/') ||
                     valueStr.includes('/uploads/') ||
                     valueStr.includes('/images/') ||
                     valueStr.includes('/assets/') ||
                     !!valueStr.match(/\/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}\.(png|jpg|jpeg|gif|svg|webp)/i);

    return isUrl && (hasImageExtension || isCdnUrl);
  }

  getIntegrationColor(key: string): string {
    const brandKey = key.toLowerCase();
    return brandColors[brandKey] || '#6c757d'; // Default color if not found
  }

  openIntegrationLink(integration: IntegrationRes): void {
    if (!integration.value) return;

    let url = '';

    switch (integration.key.toLowerCase()) {
      case 'telegram':
        // Handle Telegram link
        url = `https://t.me/${integration.value.replace('@', '')}`;
        break;
      case 'whatsapp':
        // Handle WhatsApp link
        const cleanNumber = integration.value.replace(/[\s+]/g, '');
        url = `https://wa.me/${cleanNumber}`;
        break;
      default:
        // For custom integrations and other types, use the value directly if it's a URL
        if (integration.value.startsWith('http://') || integration.value.startsWith('https://')) {
          url = integration.value;
        } else {
          console.warn('Invalid URL for integration:', integration.key, integration.value);
          return;
        }
    }

    // Open the URL in a new tab
    if (url) {
      window.open(url, '_blank');
    }
  }
}
