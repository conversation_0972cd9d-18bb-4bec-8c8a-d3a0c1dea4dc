# Database Setup for Language Management

## PostgreSQL Migration

### Option 1: Automatic Migration (Recommended)
The application will automatically create the `tenant_custom_languages` table when you start the Spring Boot application with `ddl-auto: update` configuration.

### Option 2: Manual Migration
If you prefer to run the migration manually, execute the SQL script:

```sql
-- Connect to your PostgreSQL database and run:
\i smm-system/sql/create_tenant_custom_languages_table.sql
```

Or copy and paste the content from:
- `smm-system/sql/create_tenant_custom_languages_table.sql`

### Option 3: Flyway Migration
If your project uses Flyway, the migration script is available at:
- `smm-system/src/main/resources/db/migration/V1__Create_tenant_custom_languages_table.sql`

## Table Structure

The `tenant_custom_languages` table includes:
- `id` (BIGSERIAL PRIMARY KEY)
- `tenant_id` (VARCHAR(50)) - Links to tenant
- `language_code` (VARCHAR(20)) - Unique language identifier per tenant
- `language_name` (VARCHAR(100)) - Display name
- `flag_class` (VARCHAR(20)) - CSS class for flag icon
- `description` (VARCHAR(500)) - Optional description
- `is_active` (BOOLEAN) - Whether language is active
- `created_at` (TIMESTAMP) - Creation timestamp
- `updated_at` (TIMESTAMP) - Last update timestamp (auto-updated)

## Indexes
- `idx_tenant_custom_languages_tenant_id` - For tenant-based queries
- `idx_tenant_custom_languages_language_code` - For language code lookups
- `idx_tenant_custom_languages_is_active` - For active language filtering

## Constraints
- `unique_tenant_language` - Prevents duplicate language codes per tenant

## Triggers
- `update_tenant_custom_languages_updated_at` - Automatically updates `updated_at` on row changes

## Verification

After running the migration, verify the table was created:

```sql
-- Check if table exists
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name = 'tenant_custom_languages';

-- Check table structure
\d tenant_custom_languages;

-- Test insert (optional)
INSERT INTO tenant_custom_languages (tenant_id, language_code, language_name, flag_class, description) 
VALUES ('test', 'vi2', 'Việt Nam 2', 'fi fi-vn', 'Test custom language');

-- Clean up test data
DELETE FROM tenant_custom_languages WHERE tenant_id = 'test';
```
