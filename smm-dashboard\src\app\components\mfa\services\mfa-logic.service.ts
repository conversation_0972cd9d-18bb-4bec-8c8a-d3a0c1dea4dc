import { Injectable, AfterViewInit } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { BehaviorSubject, Observable, catchError, throwError } from 'rxjs';

// Services
import { AuthService } from '../../../core/services/auth.service';
import { ToastService } from '../../../core/services/toast.service';
import { MfaStateService } from '../../../core/services/mfa-state.service';
import { NotifyType } from '../../../constant/notify-type';

export interface MfaComponentState {
  mfaForm: FormGroup;
  isLoading: boolean;
  mfaError: string;
  isResending: boolean;
  resendCooldown: number;
  canResend: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class MfaLogicService {
  private stateSubject = new BehaviorSubject<MfaComponentState>({
    mfaForm: new FormGroup({
      code: new FormControl('', [Validators.required, Validators.minLength(6), Validators.maxLength(6)])
    }),
    isLoading: false,
    mfaError: '',
    isResending: false,
    resendCooldown: 0,
    canResend: true
  });

  public state$ = this.stateSubject.asObservable();
  private resendTimer: any;

  constructor(
    private authService: AuthService,
    private router: Router,
    private toastService: ToastService,
    private mfaStateService: MfaStateService
  ) {}

  public initializeDigitInputs(): void {
    // This will be called after view init to set up digit inputs
    setTimeout(() => {
      const firstInput = document.querySelector('.mfa-digit-input') as HTMLInputElement;
      if (firstInput) {
        firstInput.focus();
      }
    }, 100);
  }

  public onDigitInput(event: Event, index: number): void {
    const input = event.target as HTMLInputElement;
    const value = input.value;

    // Only allow digits
    if (!/^\d$/.test(value)) {
      input.value = '';
      return;
    }

    // Move to next input if value is entered
    if (value && index < 5) {
      const nextInput = document.querySelectorAll('.mfa-digit-input')[index + 1] as HTMLInputElement;
      if (nextInput) {
        nextInput.focus();
      }
    }

    this.updateFormValue();
  }

  public onDigitKeyDown(event: KeyboardEvent, index: number): void {
    const input = event.target as HTMLInputElement;

    // Handle backspace
    if (event.key === 'Backspace') {
      if (!input.value && index > 0) {
        const prevInput = document.querySelectorAll('.mfa-digit-input')[index - 1] as HTMLInputElement;
        if (prevInput) {
          prevInput.focus();
          prevInput.select();
        }
      }
    }
    // Handle arrow keys
    else if (event.key === 'ArrowLeft' && index > 0) {
      const prevInput = document.querySelectorAll('.mfa-digit-input')[index - 1] as HTMLInputElement;
      if (prevInput) {
        prevInput.focus();
        prevInput.select();
      }
    }
    else if (event.key === 'ArrowRight' && index < 5) {
      const nextInput = document.querySelectorAll('.mfa-digit-input')[index + 1] as HTMLInputElement;
      if (nextInput) {
        nextInput.focus();
        nextInput.select();
      }
    }
  }

  public onFocus(event: FocusEvent): void {
    const input = event.target as HTMLInputElement;
    if (input) {
      setTimeout(() => {
        input.select();
      }, 0);
    }
  }

  public onPaste(event: ClipboardEvent): void {
    event.preventDefault();

    const pastedData = event.clipboardData?.getData('text') || '';

    // Only allow digits and limit to 6 characters
    const digits = pastedData.replace(/\D/g, '').substring(0, 6);

    if (digits.length === 0) {
      return;
    }

    // Clear all inputs first
    const inputs = document.querySelectorAll('.mfa-digit-input') as NodeListOf<HTMLInputElement>;
    inputs.forEach(input => {
      input.value = '';
    });

    // Fill inputs with pasted digits
    for (let i = 0; i < digits.length && i < 6; i++) {
      if (inputs[i]) {
        inputs[i].value = digits[i];
      }
    }

    // Focus on the next empty input or the last filled input
    const nextIndex = Math.min(digits.length, 5);
    if (inputs[nextIndex]) {
      inputs[nextIndex].focus();
    }

    // Update form value
    this.updateFormValue();
  }

  private updateFormValue(): void {
    const inputs = document.querySelectorAll('.mfa-digit-input') as NodeListOf<HTMLInputElement>;
    let combinedValue = '';

    inputs.forEach(input => {
      combinedValue += input.value || '';
    });

    const currentState = this.stateSubject.value;
    currentState.mfaForm.get('code')?.setValue(combinedValue);
    currentState.mfaForm.get('code')?.markAsDirty();

    // If we have 6 digits, mark as touched to trigger validation
    if (combinedValue.length === 6) {
      currentState.mfaForm.get('code')?.markAsTouched();
    }

    this.stateSubject.next(currentState);
  }

  public onSubmit(): void {
    const currentState = this.stateSubject.value;
    
    if (currentState.mfaForm.valid) {
      this.updateState({ isLoading: true, mfaError: '' });

      const mfaCode = currentState.mfaForm.get('code')?.value;
      const mfaInfo = this.mfaStateService.getMfaInfo();

      if (!mfaInfo) {
        this.updateState({ 
          mfaError: 'MFA session expired. Please login again.',
          isLoading: false 
        });
        this.router.navigate(['/auth/login']);
        return;
      }

      this.authService.verifyMfa(mfaInfo.username, mfaInfo.password, mfaInfo.loginFirstFactor, mfaCode)
        .pipe(
          catchError(err => {
            console.error('MFA verification error:', err);
            const errorMessage = err.message || 'Invalid verification code. Please try again.';
            this.updateState({
              mfaError: errorMessage,
              isLoading: false
            });
            this.toastService.showToast(errorMessage, NotifyType.ERROR);

            // Clear the input fields on error
            this.clearInputFields();

            return throwError(() => err);
          })
        )
        .subscribe({
          next: () => {
            console.log('MFA verification successful');
            this.toastService.showToast('Login successful!', NotifyType.SUCCESS);

            // Clear MFA info
            this.mfaStateService.clearMfaInfo();

            // Navigate to return URL
            this.router.navigate([mfaInfo.returnUrl || '/dashboard/new']).then(() => {
              window.location.reload();
            });
          },
          error: (error) => {
            console.error('MFA verification subscription error:', error);
            // Ensure loading is turned off even if error occurs in subscription
            this.updateState({ isLoading: false });
          },
          complete: () => {
            this.updateState({ isLoading: false });
          }
        });
    } else {
      // Mark form as touched to show validation errors
      currentState.mfaForm.markAllAsTouched();
      this.stateSubject.next(currentState);
    }
  }

  public resendCode(): void {
    if (!this.stateSubject.value.canResend) return;

    this.updateState({ isResending: true });

    // Simulate resend API call
    setTimeout(() => {
      this.updateState({ 
        isResending: false,
        canResend: false,
        resendCooldown: 30
      });
      
      this.toastService.showToast('Verification code resent!', NotifyType.SUCCESS);
      this.startResendCooldown();
    }, 1000);
  }

  private startResendCooldown(): void {
    this.resendTimer = setInterval(() => {
      const currentState = this.stateSubject.value;
      const newCooldown = currentState.resendCooldown - 1;
      
      if (newCooldown <= 0) {
        this.updateState({ 
          resendCooldown: 0,
          canResend: true
        });
        clearInterval(this.resendTimer);
      } else {
        this.updateState({ resendCooldown: newCooldown });
      }
    }, 1000);
  }

  public goBack(): void {
    this.mfaStateService.clearMfaInfo();
    this.router.navigate(['/auth/login']);
  }

  private updateState(partialState: Partial<MfaComponentState>): void {
    const currentState = this.stateSubject.value;
    this.stateSubject.next({
      ...currentState,
      ...partialState
    });
  }

  public cleanup(): void {
    if (this.resendTimer) {
      clearInterval(this.resendTimer);
    }
  }

  // Getters for template access
  public get currentState(): MfaComponentState {
    return this.stateSubject.value;
  }

  public get mfaForm(): FormGroup {
    return this.stateSubject.value.mfaForm;
  }

  public get isLoading(): boolean {
    return this.stateSubject.value.isLoading;
  }

  public get mfaError(): string {
    return this.stateSubject.value.mfaError;
  }

  public get isResending(): boolean {
    return this.stateSubject.value.isResending;
  }

  public get resendCooldown(): number {
    return this.stateSubject.value.resendCooldown;
  }

  public get canResend(): boolean {
    return this.stateSubject.value.canResend;
  }

  public get code(): FormControl {
    return this.stateSubject.value.mfaForm.get('code') as FormControl;
  }

  // Clear input fields method
  private clearInputFields(): void {
    const inputs = document.querySelectorAll('.mfa-digit-input') as NodeListOf<HTMLInputElement>;
    inputs.forEach(input => {
      input.value = '';
    });

    this.stateSubject.value.mfaForm.get('code')?.setValue('');
    this.stateSubject.value.mfaForm.get('code')?.markAsUntouched();
    this.stateSubject.value.mfaForm.get('code')?.markAsPristine();

    // Focus on first input
    setTimeout(() => {
      const firstInput = document.querySelector('.mfa-digit-input') as HTMLInputElement;
      if (firstInput) {
        firstInput.focus();
      }
    }, 100);
  }

  // Reset form method
  public resetForm(): void {
    this.clearInputFields();

    this.updateState({
      mfaError: '',
      isLoading: false
    });

    this.stateSubject.value.mfaForm.reset();
  }

  // Initialize method
  public initialize(): void {
    this.resetForm();
    this.initializeDigitInputs();
  }
}
