-- Add default_language column to tenant table if it doesn't exist
DO $$
BEGIN
    -- Check if the column exists
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'tenant' 
        AND column_name = 'default_language'
    ) THEN
        -- Add the column
        ALTER TABLE tenant ADD COLUMN default_language VARCHAR(4) DEFAULT 'vi';
        
        -- Update existing records to have default language 'vi'
        UPDATE tenant SET default_language = 'vi' WHERE default_language IS NULL;
        
        -- Add comment
        COMMENT ON COLUMN tenant.default_language IS 'Default language for tenant (vi, en)';
    END IF;
END $$;
