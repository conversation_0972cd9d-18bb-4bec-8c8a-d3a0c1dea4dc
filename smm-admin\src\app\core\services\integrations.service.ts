import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ConfigService } from './config.service';
import { ToastService } from './toast.service';
import { IntegrationPosition, IntegrationRes } from '../../model/response/integration-res.model';
import { IntegrationReq } from '../../model/request/integration-req.model';

// Legacy interface for backward compatibility
export interface IntegrationConfig {
  id?: number;
  type: string;
  username?: string;
  position?: string;
  token?: string;
  enabled: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class IntegrationsService {
  constructor(
    private http: HttpClient,
    private configService: ConfigService,
    private toastService: ToastService
  ) {}

  /**
   * Get all integrations - loads all integrations from API and adds default ones if missing
   */
  getIntegrations(): Observable<IntegrationConfig[]> {
    // Default integrations that should always be available
    const defaultIntegrations = ['telegram', 'whatsapp'];

    // Call API to get all integrations
    return this.http.get<IntegrationRes[]>(`${this.configService.apiUrl}/integrations`)
      .pipe(
        map(response => {
          // Convert all API integrations to UI format
          const allIntegrations: IntegrationConfig[] = response.map(integration =>
            this.mapToIntegrationConfig(integration)
          );

          // Add default integrations if they don't exist in API response
          defaultIntegrations.forEach(defaultType => {
            const exists = allIntegrations.some(integration =>
              integration.type.toLowerCase() === defaultType.toLowerCase()
            );

            if (!exists) {
              // Add default integration as not connected
              allIntegrations.push({
                id: undefined,
                type: defaultType,
                username: '',
                position: 'left',
                enabled: false,
                token: ''
              });
            }
          });

          // Sort integrations: default ones first, then custom ones
          return allIntegrations.sort((a, b) => {
            const aIsDefault = defaultIntegrations.includes(a.type.toLowerCase());
            const bIsDefault = defaultIntegrations.includes(b.type.toLowerCase());

            if (aIsDefault && !bIsDefault) return -1;
            if (!aIsDefault && bIsDefault) return 1;
            return a.type.localeCompare(b.type);
          });
        }),
        catchError(error => {
          console.error('Error fetching integrations:', error);
          // Return default integrations if API fails
          return of(defaultIntegrations.map(type => ({
            id: undefined,
            type,
            username: '',
            position: 'left',
            enabled: false,
            token: ''
          })));
        })
      );
  }

  /**
   * Connect to integration (update integration configuration)
   */
  connectIntegration(id: number, req: IntegrationReq): Observable<IntegrationConfig> {
    return this.http.put<any>(
      `${this.configService.apiUrl}/integrations/${id}/connect`,
      req
    ).pipe(
      map(() => {
        // Return a successful result
        return {
          id: id,
          type: '',
          username: req.value,
          position: req.position.toLowerCase(),
          enabled: true
        };
      }),
      catchError(error => {
        console.error('Error connecting to integration:', error);
        this.toastService.showError('Failed to connect to integration');
        return of({
          id: id,
          type: '',
          username: req.value,
          position: req.position.toLowerCase(),
          enabled: false
        });
      })
    );
  }

  /**
   * Create a new integration
   */
  createIntegration(req: IntegrationReq): Observable<IntegrationConfig> {
    return this.http.post<IntegrationRes>(
      `${this.configService.apiUrl}/integrations`,
      req
    ).pipe(
      map(response => this.mapToIntegrationConfig(response)),
      catchError(error => {
        console.error('Error creating integration:', error);
        this.toastService.showError('Failed to create integration');
        throw error;
      })
    );
  }

  /**
   * Disconnect from integration
   */
  disconnectIntegration(id: number): Observable<boolean> {
    return this.http.put<any>(
      `${this.configService.apiUrl}/integrations/${id}/disconnect`,
      {}
    ).pipe(
      map(() => true),
      catchError(error => {
        console.error('Error disconnecting from integration:', error);
        this.toastService.showError('Failed to disconnect from integration');
        return of(false);
      })
    );
  }

  /**
   * Map API response to UI model
   */
  private mapToIntegrationConfig(integration: IntegrationRes): IntegrationConfig {
    return {
      id: integration.id,
      type: integration.key,
      username: integration.value,
      position: integration.position?.toLowerCase(),
      enabled: integration.active, // Use active field from API
      token: integration.icon || '' // Map icon to token field for backward compatibility
    };
  }

  /**
   * Get integration ID from key (temporary solution)
   */
  private getIntegrationIdFromKey(key: string): number {
    switch (key.toLowerCase()) {
      case 'telegram':
        return 1;
      case 'whatsapp':
        return 2;
      default:
        return 0;
    }
  }

  /**
   * Get integrations for user (lite version)
   * This endpoint is used to display integration buttons to users
   */
  getForUser(): Observable<IntegrationRes[]> {
    return this.http.get<IntegrationRes[]>(`${this.configService.apiUrl}/integrations/lite`)
      .pipe(
        catchError(error => {
          console.error('Error fetching user integrations:', error);
          return of([]);
        })
      );
  }

  /**
   * Mock integrations data for development
   */

}
