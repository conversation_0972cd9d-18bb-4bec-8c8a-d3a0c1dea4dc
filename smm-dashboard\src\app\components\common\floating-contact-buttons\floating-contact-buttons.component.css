.floating-contact-container {
  position: fixed;
  bottom: 20px;
  width: 100%;
  z-index: 999;
  pointer-events: none;
}

.floating-buttons-left {
  position: absolute;
  left: 20px;
  bottom: 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
  pointer-events: auto;
}

.floating-buttons-right {
  position: absolute;
  right: 20px;
  bottom: 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
  pointer-events: auto;
}

/* No desktop override - let buttons stay on their respective sides */

.floating-contact-button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.floating-contact-button:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.contact-icon {
  font-size: 24px;
}

.contact-icon-image {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

/* Loading indicator styles */
.loading-indicator {
  position: fixed;
  bottom: 20px;
  right: 20px;
  pointer-events: auto;
}

.spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary, #0ea5e9);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Mobile responsive styles */
@media (max-width: 768px) {
  .floating-buttons-left,
  .floating-buttons-right {
    bottom: 10px;
  }

  .floating-buttons-left {
    left: 10px;
  }

  .floating-buttons-right {
    right: 10px;
  }

  .floating-contact-button {
    width: 45px;
    height: 45px;
  }

  .contact-icon {
    font-size: 20px;
  }

  .contact-icon-image {
    width: 20px;
    height: 20px;
  }

  .loading-indicator {
    bottom: 10px;
    right: 10px;
  }

  .spinner {
    width: 25px;
    height: 25px;
  }
}
