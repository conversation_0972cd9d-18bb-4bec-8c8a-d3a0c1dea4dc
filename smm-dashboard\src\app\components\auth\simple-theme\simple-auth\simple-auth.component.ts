import { Component, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { RouterModule } from '@angular/router';

// Base component
import { BaseAuthComponent } from '../../base-auth.component';

// Services
import { AuthLogicService } from '../../services/auth-logic.service';

@Component({
  selector: 'app-simple-auth',
  standalone: true,
  imports: [FormsModule, CommonModule, TranslateModule, ReactiveFormsModule, RouterModule],
  templateUrl: './simple-auth.component.html',
  styleUrl: './simple-auth.component.css'
})
export class SimpleAuthComponent extends BaseAuthComponent implements OnInit {
  constructor(authLogicService: AuthLogicService) {
    super(authLogicService);
  }
}
