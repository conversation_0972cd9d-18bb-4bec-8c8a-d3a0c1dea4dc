import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { BehaviorSubject, Subscription } from 'rxjs';

// Services
import { AuthService } from '../../../../core/services/auth.service';
import { UserService } from '../../../../core/services/user.service';
import { CurrencyService } from '../../../../core/services/currency.service';
import { ThemeService, LayoutTheme } from '../../../../core/services/theme.service';

// Models
import { UserRes } from '../../../../model/response/user-res.model';

export interface AvatarState {
  // UI state
  isOpen: boolean;
  currentHeaderStyle: string;
  
  // User data
  user: UserRes | undefined;
  formattedBalance: string;
  
  // Theme management
  currentTheme: LayoutTheme;
}

@Injectable({
  providedIn: 'root'
})
export class AvatarLogicService {
  private subscriptions: Subscription[] = [];

  // State management
  private _state$ = new BehaviorSubject<AvatarState>({
    isOpen: false,
    currentHeaderStyle: 'standard',
    user: undefined,
    formattedBalance: '111',
    currentTheme: LayoutTheme.DEFAULT
  });

  // Public state observable
  public readonly state$ = this._state$.asObservable();

  // Current state getter
  private get currentState(): AvatarState {
    return this._state$.value;
  }

  constructor(
    private router: Router,
    private authService: AuthService,
    private userService: UserService,
    private currencyService: CurrencyService,
    private themeService: ThemeService
  ) {
    this.initialize();
  }

  // Public getters for template access
  get isOpen(): boolean {
    return this.currentState.isOpen;
  }

  get currentHeaderStyle(): string {
    return this.currentState.currentHeaderStyle;
  }

  get user(): UserRes | undefined {
    return this.currentState.user;
  }

  get formattedBalance(): string {
    return this.currentState.formattedBalance;
  }

  get currentTheme(): LayoutTheme {
    return this.currentState.currentTheme;
  }

  // Initialize service
  private initialize(): void {
    this.subscribeToUserChanges();
    this.subscribeToHeaderStyleChanges();
    this.subscribeToThemeChanges();
    this.triggerUserDataLoad();
  }

  // Update state helper
  private updateState(updates: Partial<AvatarState>): void {
    const currentState = this._state$.value;
    this._state$.next({ ...currentState, ...updates });
  }

  // Subscribe to user information changes
  private subscribeToUserChanges(): void {
    const userSubscription = this.userService.user$.subscribe(user => {
      const formattedBalance = user ? this.formatBalance(user.balance) : '111';
      this.updateState({ 
        user,
        formattedBalance
      });
    });
    this.subscriptions.push(userSubscription);
  }

  // Subscribe to header style changes
  private subscribeToHeaderStyleChanges(): void {
    const headerStyleSubscription = this.themeService.headerStyle$.subscribe(style => {
      this.updateState({ currentHeaderStyle: style });
    });
    this.subscriptions.push(headerStyleSubscription);
  }

  // Subscribe to theme changes
  private subscribeToThemeChanges(): void {
    const themeSubscription = this.themeService.currentLayoutTheme$.subscribe((theme: LayoutTheme) => {
      this.updateState({ currentTheme: theme });
    });
    this.subscriptions.push(themeSubscription);
  }

  // Trigger user data loading
  private triggerUserDataLoad(): void {
    this.userService.get$.next();
  }

  // Format balance using currency service
  private formatBalance(balance: number): string {
    return this.currencyService.formatPrice(balance);
  }

  // Public methods for component interaction
  toggleMenu(): void {
    this.updateState({ isOpen: !this.currentState.isOpen });
  }

  closeMenu(): void {
    this.updateState({ isOpen: false });
  }

  goToSettings(): void {
    this.router.navigate(['dashboard/settings']);
    this.closeMenu();
  }

  goToSecuritySettings(): void {
    this.router.navigate(['dashboard/settings'], { queryParams: { tab: 'security' } });
    this.closeMenu();
  }

  logout(): void {
    this.authService.logout();
    this.closeMenu();
  }

  getAvatarPath(): string {
    const user = this.currentState.user;
    if (user?.avatar && user.avatar.trim() !== '') {
      // If avatar is already a full URL, return it as is
      if (user.avatar.startsWith('http://') || user.avatar.startsWith('https://')) {
        return user.avatar;
      }
      // If it's a relative path, construct the full URL
      
      return `assets/images/${user.avatar}.png`;
    }
    return 'assets/images/avatar.png';
  }

  // Cleanup
  destroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.subscriptions = [];
  }
}
