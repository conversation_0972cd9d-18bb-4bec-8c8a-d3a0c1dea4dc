.interface-switcher {
  position: relative;
}

.switcher-button {
  min-width: 140px;
  justify-content: space-between;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.switcher-button.active {
  @apply bg-gray-50 border-gray-300;
}

.dropdown-menu {
  animation: slideDown 0.2s ease-out;
  transform-origin: top;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.theme-option {
  position: relative;
}

.theme-option.selected {
  @apply bg-blue-50;
}

.theme-option.selected::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #3b82f6;
}

.theme-option:hover {
  @apply bg-gray-50;
}

.theme-option.selected:hover {
  @apply bg-blue-50;
}

.theme-icon {
  transition: all 0.2s ease;
}

.theme-name {
  font-weight: 500;
}

.theme-description {
  line-height: 1.3;
}

.selected-indicator {
  animation: checkmark 0.3s ease-in-out;
}

@keyframes checkmark {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .dropdown-menu {
    width: 280px;
    right: -8px;
  }
  
  .switcher-button {
    min-width: 120px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .dropdown-menu {
    @apply bg-gray-800 border-gray-700;
  }
  
  .dropdown-header {
    @apply border-gray-700;
  }
  
  .dropdown-footer {
    @apply bg-gray-700 border-gray-600;
  }
  
  .theme-option:hover {
    @apply bg-gray-700;
  }
  
  .theme-option.selected {
    @apply bg-blue-900;
  }
}
