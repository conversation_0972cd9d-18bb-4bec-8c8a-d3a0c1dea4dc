-- Create table for tenant-specific currency settings
CREATE TABLE IF NOT EXISTS tenant_currency_settings (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(36) NOT NULL,
    currency_code VARCHAR(5) NOT NULL,
    sync_enabled BOOLEAN DEFAULT true,
    payment_sync_enabled BOOLEAN DEFAULT false,
    custom_rate DECIMAL(20, 8) NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, currency_code),
    FOREIGN KEY (currency_code) REFERENCES currency(code) ON DELETE CASCADE
);

-- Remove individual sync settings from currency table since they're now tenant-specific
ALTER TABLE currency DROP COLUMN IF EXISTS sync_enabled;
ALTER TABLE currency DROP COLUMN IF EXISTS last_sync;

-- Remove global sync settings from tenant table since they're now per-currency
ALTER TABLE tenant DROP COLUMN IF EXISTS currency_sync_enabled;
ALTER TABLE tenant DROP COLUMN IF EXISTS sync_with_payment;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_tenant_currency_settings_tenant_id ON tenant_currency_settings(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_currency_settings_currency_code ON tenant_currency_settings(currency_code);
