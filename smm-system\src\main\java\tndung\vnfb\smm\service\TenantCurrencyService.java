package tndung.vnfb.smm.service;

import tndung.vnfb.smm.dto.request.TenantCurrencyReq;
import tndung.vnfb.smm.dto.response.TenantCurrencyRes;
import tndung.vnfb.smm.entity.Currency;
import tndung.vnfb.smm.entity.TenantCurrencySettings;

import java.math.BigDecimal;
import java.util.List;

public interface TenantCurrencyService {

    TenantCurrencyRes getTenantCurrencies();

    TenantCurrencyRes updateTenantCurrencies(TenantCurrencyReq req);

    List<Currency> getAvailableCurrenciesForCurrentTenant();

    void triggerCurrencySync();

    void updateCurrencySyncSettings(String currencyCode, boolean syncEnabled, boolean paymentSyncEnabled);

    TenantCurrencySettings getTenantCurrencySettings(String tenantId, String currencyCode);

    BigDecimal getEffectiveRate(String tenantId, String currencyCode);
}
