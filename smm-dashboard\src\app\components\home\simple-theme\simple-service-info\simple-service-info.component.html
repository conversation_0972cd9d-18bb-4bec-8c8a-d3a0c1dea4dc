<div class="simple-service-info-container">
  
  <!-- Service Selected State -->
  <div *ngIf="selectedService" class="service-selected">
    
    <!-- Service Header -->
    <div class="service-header">
      <h3 class="service-name">{{ selectedService.name }}</h3>
      <div class="service-id">#{{ selectedService.id }}</div>

       <app-simple-favorite-services></app-simple-favorite-services>

    </div>

    <!-- Service Details Grid -->
    <div class="service-details-grid">
      
      <!-- Example URL -->
      <div class="detail-item">
        <div class="detail-label">
          <fa-icon [icon]='["fas", "link"]' class="detail-icon"></fa-icon>
          {{ 'simple_theme.example_url' | translate }}
        </div>
        <div class="detail-value">
          <a [href]="exampleUrl" target="_blank" class="example-link">
            {{ exampleUrl }}
          </a>
        </div>
      </div>

      <!-- Start Time -->
      <div class="detail-item">
        <div class="detail-label">
          <fa-icon [icon]='["fas", "clock"]' class="detail-icon"></fa-icon>
          {{ 'start_time' | translate }}
        </div>
        <div class="detail-value">{{ startTime }}</div>
      </div>

      <!-- Speed -->
      <div class="detail-item">
        <div class="detail-label">
          <fa-icon [icon]='["fas", "tachometer-alt"]' class="detail-icon"></fa-icon>
          {{ 'speed' | translate }}
        </div>
        <div class="detail-value">{{ speed }}</div>
      </div>

      <!-- Average Time -->
      <div class="detail-item">
        <div class="detail-label">
          <fa-icon [icon]='["fas", "stopwatch"]' class="detail-icon"></fa-icon>
          {{ 'average_time' | translate }}
        </div>
        <div class="detail-value">{{ avgTime }}</div>
      </div>

      <!-- Warranty -->
      <div class="detail-item">
        <div class="detail-label">
          <fa-icon [icon]='["fas", "shield-alt"]' class="detail-icon"></fa-icon>
          {{ 'simple_theme.warranty' | translate }}
        </div>
        <div class="detail-value">{{ warranty }}</div>
      </div>

      <!-- Min/Max -->
      <div class="detail-item">
        <div class="detail-label">
          <fa-icon [icon]='["fas", "sort-numeric-up"]' class="detail-icon"></fa-icon>
          {{ 'min_max' | translate }}
        </div>
        <div class="detail-value">
          {{ selectedService.min | number }} - {{ selectedService.max | number }}
        </div>
      </div>

    </div>

    <!-- Description -->
    <div class="service-description">
      <div class="description-label">
        <fa-icon [icon]='["fas", "info-circle"]' class="detail-icon"></fa-icon>
        {{ 'description' | translate }}
      </div>
      <p class="description-text">{{ description }}</p>
    </div>

  </div>

  <!-- No Service Selected State -->
  <div *ngIf="!selectedService" class="no-service-selected">
    <div class="empty-state">
      <fa-icon [icon]='["fas", "info-circle"]' class="empty-icon"></fa-icon>
      <h3 class="empty-title">{{ 'simple_theme.no_service_selected' | translate }}</h3>
      <p class="empty-message">{{ 'simple_theme.select_service_to_view_info' | translate }}</p>
    </div>
  </div>

</div>
