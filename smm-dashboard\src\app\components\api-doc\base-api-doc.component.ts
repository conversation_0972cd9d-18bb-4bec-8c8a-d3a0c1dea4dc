import { <PERSON>mpo<PERSON>, <PERSON>Ini<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { ApiDocLogicService, ApiService } from './api-doc.service';

@Component({
  template: ''
})
export abstract class BaseApiDocComponent implements OnInit, OnDestroy {
  constructor(protected apiDocLogic: ApiDocLogicService) {}

  ngOnInit(): void {
    this.apiDocLogic.initialize();
  }

  ngOnDestroy(): void {
    this.apiDocLogic.ngOnDestroy();
  }

  // Expose service properties
  get apiDetails() { return this.apiDocLogic.apiDetails; }
  get isLoadingApiKey(): boolean { return this.apiDocLogic.isLoadingApiKey; }
  get isGeneratingApiKey(): boolean { return this.apiDocLogic.isGeneratingApiKey; }
  get parameters() { return this.apiDocLogic.parameters; }
  get codeExample(): string { return this.apiDocLogic.codeExample; }
  get services(): string[] { return this.apiDocLogic.services; }
  get selectedService(): string { return this.apiDocLogic.selectedService; }
  get serviceData(): { [key: string]: ApiService } { return this.apiDocLogic.serviceData; }

  // Expose service methods
  loadApiKey(): void {
    this.apiDocLogic.loadApiKey();
  }

  generateApiKey(): void {
    this.apiDocLogic.generateApiKey();
  }

  selectService(serviceId: string): void {
    this.apiDocLogic.selectService(serviceId);
  }
}
