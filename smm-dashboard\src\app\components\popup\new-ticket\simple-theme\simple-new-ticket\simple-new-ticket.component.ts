import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { LiteDropdownComponent } from "../../../../common/lite-dropdown/lite-dropdown.component";
import { TranslateModule } from '@ngx-translate/core';
import { BaseNewTicketComponent } from '../../base-new-ticket.component';
import { NewTicketLogicService } from '../../new-ticket.service';

@Component({
  selector: 'app-simple-new-ticket',
  standalone: true,
  imports: [FormsModule, CommonModule, LiteDropdownComponent, TranslateModule],
  templateUrl: './simple-new-ticket.component.html',
  styleUrls: ['./simple-new-ticket.component.css'],
  providers: [NewTicketLogicService]
})
export class SimpleNewTicketComponent extends BaseNewTicketComponent implements OnInit {
  constructor(newTicketLogic: NewTicketLogicService) {
    super(newTicketLogic);
  }

  override ngOnInit(): void {
    super.ngOnInit();
  }
}
