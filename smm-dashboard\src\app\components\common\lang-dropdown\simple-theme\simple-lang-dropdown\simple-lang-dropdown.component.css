/* Simple Lang Dropdown Theme Styles */
.simple-lang-dropdown {
  position: relative;
  display: inline-block;
}

/* Language Selector Button */
.lang-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 16px;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
  min-width: 140px;
}

.lang-selector:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
}

.selected-language {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.flag-container {
  position: relative;
}

.flag-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  display: block;
  background-size: cover;
  background-position: center;
}

.language-info {
  display: flex;
  flex-direction: column;
  color: white;
}

.language-name {
  font-size: 13px;
  font-weight: 600;
  line-height: 1.2;
}

.language-code {
  font-size: 11px;
  opacity: 0.8;
  line-height: 1.2;
}

.dropdown-arrow {
  width: 18px;
  height: 18px;
  color: rgba(255, 255, 255, 0.8);
  transition: transform 0.3s ease;
}

.dropdown-arrow.rotated {
  transform: rotate(180deg);
}

/* Dropdown Menu */
.lang-dropdown-menu {
  position: absolute;
  top: calc(100% + 12px);
  right: 0;
  width: 280px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.05);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 9999;
  overflow: hidden;
}

.lang-dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-content {
  padding: 20px;
}

/* Dropdown Header */
.dropdown-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.header-text {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

/* Language Options */
.language-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.language-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.language-option:hover {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  transform: translateX(4px);
}

.language-option.selected {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
}

.language-option.selected .option-info {
  color: white;
}

.option-flag {
  flex-shrink: 0;
}

.language-option .flag-icon {
  width: 28px;
  height: 28px;
  border: 2px solid rgba(0, 0, 0, 0.1);
}

.language-option.selected .flag-icon {
  border-color: rgba(255, 255, 255, 0.3);
}

.option-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  color: #333;
}

.option-name {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.2;
}

.option-code {
  font-size: 12px;
  opacity: 0.7;
  line-height: 1.2;
}

.option-check {
  width: 20px;
  height: 20px;
  color: white;
  flex-shrink: 0;
}

/* Overlay */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 9998;
  backdrop-filter: blur(2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .lang-selector {
    padding: 8px 12px;
    min-width: 120px;
  }
  
  .language-info {
    display: none;
  }
  
  .flag-icon {
    width: 20px;
    height: 20px;
  }
  
  .lang-dropdown-menu {
    width: 240px;
    right: -20px;
  }
  
  .language-option .flag-icon {
    width: 24px;
    height: 24px;
  }
}
