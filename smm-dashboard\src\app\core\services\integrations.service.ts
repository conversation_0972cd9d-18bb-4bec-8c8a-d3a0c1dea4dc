import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ConfigService } from './config.service';
import { ToastService } from './toast.service';
import { IntegrationPosition, IntegrationRes } from '../../model/response/integration-res.model';
import { IntegrationReq } from '../../model/request/integration-req.model';

// Legacy interface for backward compatibility
export interface IntegrationConfig {
  id?: number;
  type: string;
  username?: string;
  position?: string;
  token?: string;
  enabled: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class IntegrationsService {
  constructor(
    private http: HttpClient,
    private configService: ConfigService,
    private toastService: ToastService
  ) {}

  /**
   * Get all integrations
   */
  getIntegrations(): Observable<IntegrationConfig[]> {
    return this.http.get<IntegrationRes[] >(`${this.configService.apiUrl}/integrations`)
      .pipe(
        map(response => {
          // Map API response to the format expected by the UI
          return response.map(integration => this.mapToIntegrationConfig(integration));
        }),
        catchError(error => {
          console.error('Error fetching integrations:', error);
          // Return mock data for now
          return of([]);
        })
      );
  }

  /**
   * Connect to integration (update integration configuration)
   */
  connectIntegration(id: number, req: IntegrationReq): Observable<IntegrationConfig> {
    return this.http.put<any>(
      `${this.configService.apiUrl}/integrations/${id}/connect`,
      req
    ).pipe(
      map(() => {
        // Return a successful result
        return {
          id: id,
          type: '',
          username: req.value,
          position: req.position.toLowerCase(),
          enabled: true
        };
      }),
      catchError(error => {
        console.error('Error connecting to integration:', error);
        this.toastService.showError('Failed to connect to integration');
        return of({
          id: id,
          type: '',
          username: req.value,
          position: req.position.toLowerCase(),
          enabled: false
        });
      })
    );
  }

  /**
   * Disconnect from integration
   */
  disconnectIntegration(id: number): Observable<boolean> {
    return this.http.put<any>(
      `${this.configService.apiUrl}/integrations/${id}/disconnect`,
      {}
    ).pipe(
      map(() => true),
      catchError(error => {
        console.error('Error disconnecting from integration:', error);
        this.toastService.showError('Failed to disconnect from integration');
        return of(false);
      })
    );
  }

  /**
   * Map API response to UI model
   */
  private mapToIntegrationConfig(integration: IntegrationRes): IntegrationConfig {
    return {
      id: integration.id,
      type: integration.key,
      username: integration.value,
      position: integration.position?.toLowerCase(),
      enabled: integration.active, // Use active field from API
      token: integration.icon || '' // Map icon to token field for backward compatibility
    };
  }

  /**
   * Get integration ID from key (temporary solution)
   */
  private getIntegrationIdFromKey(key: string): number {
    switch (key.toLowerCase()) {
      case 'telegram':
        return 1;
      case 'whatsapp':
        return 2;
      default:
        return 0;
    }
  }

  /**
   * Get integrations for user (lite version)
   * This endpoint is used to display integration buttons to users
   */
  getForUser(): Observable<IntegrationRes[]> {
    return this.http.get<IntegrationRes[]>(`${this.configService.apiUrl}/integrations/lite`)
      .pipe(
        catchError(error => {
          console.error('Error fetching user integrations:', error);
          return of([]);
        })
      );
  }

  /**
   * Mock integrations data for development
   */

}
