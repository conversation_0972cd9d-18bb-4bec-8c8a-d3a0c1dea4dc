import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Observable } from 'rxjs';
import { CashFlowLogicService, CashFlowState } from './services/cash-flow-logic.service';
import { MyTransactionRes } from '../../model/response/my-transaction.model';

@Component({
  template: '', // Will be overridden by child components
})
export abstract class BaseCashFlowComponent implements OnInit, OnDestroy {
  // Cash flow logic state for all themes
  cashFlowState$: Observable<CashFlowState>;

  constructor(protected cashFlowLogicService: CashFlowLogicService) {
    this.cashFlowState$ = this.cashFlowLogicService.state$;
  }

  ngOnInit(): void {
    // CashFlowLogicService handles all initialization
  }

  ngOnDestroy(): void {
    // CashFlowLogicService is singleton, no cleanup needed for individual components
  }

  // Delegate methods to CashFlowLogicService for template compatibility
  onSearch(): void {
    this.cashFlowLogicService.onSearch();
  }

  onPageChange(page: number): void {
    this.cashFlowLogicService.onPageChange(page);
  }

  onPageSizeChange(size: number): void {
    this.cashFlowLogicService.onPageSizeChange(size);
  }

  onTransactionTypeSelected(type: string): void {
    this.cashFlowLogicService.onTransactionTypeSelected(type);
  }

  onStatusSelected(status: string): void {
    this.cashFlowLogicService.onStatusSelected(status);
  }

  onDateRangeChanged(dateRange: { startDate: Date | null; endDate: Date | null }): void {
    this.cashFlowLogicService.updateDateRange(dateRange);
  }

  toggleAllTransactions(): void {
    this.cashFlowLogicService.toggleAllTransactions();
  }

  toggleTransaction(transactionId: number): void {
    this.cashFlowLogicService.toggleTransaction(transactionId);
  }

  isSelected(transactionId: number): boolean {
    return this.cashFlowLogicService.isSelected(transactionId);
  }

  getChangeClass(amount: number): string {
    return this.cashFlowLogicService.getChangeClass(amount);
  }

  getBalanceComponents(transaction: MyTransactionRes): { previous: number, change: number, current: number } {
    return this.cashFlowLogicService.getBalanceComponents(transaction);
  }

  formatNumber(value: number): string {
    return this.cashFlowLogicService.formatNumber(value);
  }

  resetFilters(): void {
    this.cashFlowLogicService.resetFilters();
  }
}
