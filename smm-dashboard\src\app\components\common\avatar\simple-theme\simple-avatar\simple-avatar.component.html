<!-- Simple Avatar Theme - Clean Modern Design -->
<div class="simple-avatar-container" *ngIf="avatarState$ | async as avatarState">
  
  <!-- Avatar Button -->
  <div class="avatar-button" (click)="toggleMenu()">
    <div class="avatar-image">
      <img [src]="getAvatarPath()" alt="Profile" class="user-avatar">
    </div>
    <div class="user-details">
      <span class="user-name">{{ avatarState.user?.user_name }}</span>
      <span class="user-role">{{ 'avatar.agent' | translate }}</span>
    </div>
    <div class="dropdown-icon">
      <svg viewBox="0 0 24 24" fill="currentColor">
        <path d="M7 10l5 5 5-5z"/>
      </svg>
    </div>
  </div>

  <!-- Dropdown Menu -->
  <div class="avatar-menu" [class.show]="avatarState.isOpen">
    <div class="menu-content">
      
      <!-- User Profile Section -->
      <div class="profile-section">
        <div class="profile-avatar">
          <img [src]="getAvatarPath()" alt="Profile" class="profile-image">
        </div>
        <div class="profile-info">
          <div class="profile-name">{{ avatarState.user?.user_name }}</div>
          <div class="profile-role">{{ 'avatar.agent' | translate }}</div>
        </div>
      </div>

      <!-- Divider -->
      <div class="menu-divider"></div>

      <!-- Menu Items -->
      <div class="menu-items">
        <div class="menu-item" (click)="goToSettings()">
          <div class="menu-icon">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z"/>
            </svg>
          </div>
          <span class="menu-text">{{ 'avatar.account_info' | translate }}</span>
        </div>

        <div class="menu-item" (click)="goToSecuritySettings()">
          <div class="menu-icon">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11.5C15.4,11.5 16,12.4 16,13V16C16,17.4 15.4,18 14.8,18H9.2C8.6,18 8,17.4 8,16V13C8,12.4 8.6,11.5 9.2,11.5V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.5,8.7 10.5,10V11.5H13.5V10C13.5,8.7 12.8,8.2 12,8.2Z"/>
            </svg>
          </div>
          <span class="menu-text">{{ 'avatar.change_password' | translate }}</span>
        </div>
      </div>

      <!-- Divider -->
      <div class="menu-divider"></div>

      <!-- Balance Section -->
      <div class="balance-section">
        <div class="balance-item">
          <div class="balance-value">{{ avatarState.formattedBalance }}</div>
          <div class="balance-label">{{ 'avatar.balance' | translate }}</div>
        </div>
        <div class="balance-item">
          <div class="balance-value">{{ avatarState.user?.total_order }}</div>
          <div class="balance-label">{{ 'avatar.total_orders' | translate }}</div>
        </div>
      </div>

      <!-- Divider -->
      <div class="menu-divider"></div>

      <!-- Logout -->
      <div class="logout-section">
        <div class="menu-item logout-item" (click)="logout()">
          <div class="menu-icon logout-icon">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M16 17v-3H9v-4h7V7l5 5-5 5M14 2a2 2 0 0 1 2 2v2h-2V4H4v16h10v-2h2v2a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h10Z"/>
            </svg>
          </div>
          <span class="menu-text">{{ 'avatar.logout' | translate }}</span>
        </div>
      </div>

    </div>
  </div>
</div>

<!-- Overlay -->
<div class="overlay" *ngIf="(avatarState$ | async)?.isOpen" (click)="closeMenu()"></div>
