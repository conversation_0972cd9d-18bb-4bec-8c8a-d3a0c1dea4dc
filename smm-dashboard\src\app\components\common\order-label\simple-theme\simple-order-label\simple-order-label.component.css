/* Simple Theme Order Label Styles */
.simple-order-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 1rem;
  color: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.simple-order-card:hover {
  transform: translateY(-2px);
}

.simple-order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.simple-order-id {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.simple-id-label {
  font-size: 0.75rem;
  opacity: 0.8;
  font-weight: 500;
}

.simple-id-value {
  font-size: 0.9rem;
  font-weight: 600;
}

.simple-time {
  font-size: 0.75rem;
  opacity: 0.7;
}

.simple-copy-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  color: white;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.simple-copy-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.simple-order-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.simple-service-tag {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  width: fit-content;
}

.simple-description {
  font-size: 0.85rem;
  line-height: 1.4;
  opacity: 0.9;
}

.simple-price {
  font-size: 0.9rem;
  font-weight: 600;
  color: #ffd700;
}

.simple-order-link {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.simple-link-label {
  font-size: 0.75rem;
  opacity: 0.8;
  font-weight: 500;
}

.simple-link-value {
  font-size: 0.8rem;
  word-break: break-all;
  opacity: 0.9;
}
