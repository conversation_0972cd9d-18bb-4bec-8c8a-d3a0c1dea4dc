import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../../icons/icons.module';
import { BaseFavoriteServicesComponent } from '../../favorite-services/base-favorite-services.component';
import { FavoritesService } from '../../../../core/services/favorites.service';
import { ServiceSelectionService } from '../../../../core/services/service-selection.service';

@Component({
  selector: 'app-simple-favorite-services',
  standalone: true,
  imports: [CommonModule, TranslateModule, IconsModule],
  templateUrl: './simple-favorite-services.component.html',
  styleUrls: ['./simple-favorite-services.component.css']
})
export class SimpleFavoriteServicesComponent extends BaseFavoriteServicesComponent {

  constructor(
    favoritesService: FavoritesService,
    serviceSelectionService: ServiceSelectionService
  ) {
    super(favoritesService, serviceSelectionService);
  }

  onCheckboxChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.onToggleFavorite(target.checked);
  }
}
