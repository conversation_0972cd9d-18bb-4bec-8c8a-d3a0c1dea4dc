import { <PERSON><PERSON>nent, <PERSON><PERSON>nit, <PERSON><PERSON><PERSON>roy, AfterViewInit, Input, Output, EventEmitter, HostListener, inject } from '@angular/core';
import { LiteDropdownLogicService } from './lite-dropdown.service';

@Component({
  template: ''
})
export abstract class LiteDropdownBaseComponent implements OnInit, OnDestroy, AfterViewInit {
  protected dropdownLogic = inject(LiteDropdownLogicService);

  @Input() set options(value: string[]) {
    this.dropdownLogic.setOptions(value);
  }
  
  @Input() set placeholder(value: string) {
    this.dropdownLogic.setPlaceholder(value);
  }
  
  @Input() set iconSize(value: number) {
    this.dropdownLogic.setIconSize(value);
  }
  
  @Input() set selectedOption(value: string | undefined | null) {
    this.dropdownLogic.setSelectedOption(value);
  }

  @Input() set customClassButton(value: string) {
    this.dropdownLogic.setCustomClassButton(value);
  }
  
  @Input() set customClassDropdown(value: string) {
    this.dropdownLogic.setCustomClassDropdown(value);
  }

  @Output() selected = new EventEmitter<string>();

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    this.dropdownLogic.onDocumentClick(event);
  }

  @HostListener('window:resize')
  onWindowResize() {
    this.dropdownLogic.onWindowResize();
  }

  @HostListener('window:scroll', ['$event'])
  onWindowScroll() {
    this.dropdownLogic.onWindowScroll();
  }

  ngOnInit(): void {
    // Base initialization is handled by the service
  }

  ngOnDestroy(): void {
    // Cleanup is handled by the service
  }

  ngAfterViewInit(): void {
    // Add a small delay to ensure the component is fully rendered
    setTimeout(() => {
      this.dropdownLogic.onWindowResize();
    }, 0);
  }

  // Expose service properties
  get dropdownOptions(): string[] { return this.dropdownLogic.options; }
  get dropdownPlaceholder(): string { return this.dropdownLogic.placeholder; }
  get dropdownIconSize(): number { return this.dropdownLogic.iconSize; }
  get dropdownSelectedOption(): string | undefined | null { return this.dropdownLogic.selectedOption; }
  get dropdownCustomClassButton(): string { return this.dropdownLogic.customClassButton; }
  get dropdownCustomClassDropdown(): string { return this.dropdownLogic.customClassDropdown; }
  get isOpen(): boolean { return this.dropdownLogic.isOpen; }

  // Expose service methods
  toggleDropdown(event?: MouseEvent): void {
    this.dropdownLogic.toggleDropdown(event);
  }

  selectOption(option: string, event?: MouseEvent): void {
    this.dropdownLogic.selectOption(option, event, (selectedOption) => {
      this.selected.emit(selectedOption);
    });
  }
}
