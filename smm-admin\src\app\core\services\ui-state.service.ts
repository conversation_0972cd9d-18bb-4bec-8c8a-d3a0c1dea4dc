import { Injectable } from '@angular/core';
import { SuperGeneralSvRes } from '../../model/response/super-general-sv.model';
import { ExtendedCategoryRes } from '../../model/extended/extended-category.model';
import { SuperCategoryRes } from '../../model/response/super-category.model';
import { SpecialPriceRes } from '../../model/response/special-price-res.model';
import { ToastService } from './toast.service';
import { LoadingService } from './loading.service';

@Injectable({
  providedIn: 'root'
})
export class UIStateService {
  // Modal states
  showModal = false;
  showAddServiceV2Modal = false;
  showAddPlatformLightModal = false;
  showPlatformManagementModal = false;
  showCategorySelectionModal = false;
  showNewCategoryModal = false;
  showNewPricesModal = false;
  showNewSpecialPricesModal = false;
  showSpecialPricesUserModal = false;
  showSpecialPricesServiceModal = false;
  showImportServicesModal = false;
  showNewServiceModal = false;
  showCreatePromoCodeModal = false;

  // Service type for new service modal
  selectedServiceType: string = 'Provider';

  // Menu states
  showBulkActionMenu = false;
  showServiceActionMenu = false;
  showCategoryActionMenu = false;
  showCategoryPlatformDropdown = false;

  // Selected items for actions
  selectedServiceForAction: SuperGeneralSvRes | null = null;
  selectedCategoryForAction: ExtendedCategoryRes | null = null;
  selectedCategoryForPlatform: ExtendedCategoryRes | null = null;
  selectedCategoryForMove: SuperCategoryRes | null = null;
  categoryToEdit: any | null = null;
  specialPriceToEdit: SpecialPriceRes | null = null;

  // Menu positions
  menuPosition = { top: 0, left: 0 };
  categoryMenuPosition = { top: 0, left: 0 };
  categoryPlatformMenuPosition = { top: 0, left: 0 };

  // Loading states
  loading = false;
  loadingBulkOperation = false;
  bulkOperationMessage = '';

  constructor(
    private toastService: ToastService,
    private loadingService: LoadingService
  ) {}

  /**
   * Opens the bulk action menu
   * @param event The mouse event
   */
  openBulkActionMenu(event: MouseEvent): void {
    event.stopPropagation();

    // Close all other menus first
    this.closeServiceActionMenu();
    this.closeCategoryActionMenu();

    // Toggle menu visibility
    this.showBulkActionMenu = !this.showBulkActionMenu;
  }

  /**
   * Closes the bulk action menu
   */
  closeBulkActionMenu(): void {
    this.showBulkActionMenu = false;
  }

  /**
   * Opens the service action menu
   * @param event The mouse event
   * @param service The service to act on
   */
  openServiceActionMenu(event: MouseEvent, service: SuperGeneralSvRes): void {
    event.stopPropagation();

    // Close all other menus first
    this.closeBulkActionMenu();
    this.closeCategoryActionMenu();

    // Toggle menu visibility
    if (this.showServiceActionMenu && this.selectedServiceForAction?.id === service.id) {
      this.closeServiceActionMenu();
      return;
    }

    this.showServiceActionMenu = true;
    this.selectedServiceForAction = service;
  }

  /**
   * Closes the service action menu
   */
  closeServiceActionMenu(): void {
    this.showServiceActionMenu = false;

    // Only clear selectedServiceForAction if we're not in edit mode
    if (!this.showNewServiceModal) {
      this.selectedServiceForAction = null;
    } else {
      console.log('closeServiceActionMenu - keeping selectedServiceForAction for edit mode');
    }
  }

  /**
   * Opens the category action menu
   * @param event The mouse event
   * @param category The category to act on
   */
  openCategoryActionMenu(event: MouseEvent, category: ExtendedCategoryRes): void {
    event.stopPropagation();

    // Close all other menus first
    this.closeBulkActionMenu();
    this.closeServiceActionMenu();

    // Toggle menu visibility
    if (this.showCategoryActionMenu && this.selectedCategoryForAction?.id === category.id) {
      this.closeCategoryActionMenu();
      return;
    }

    this.showCategoryActionMenu = true;
    this.selectedCategoryForAction = category;
  }

  /**
   * Closes the category action menu
   */
  closeCategoryActionMenu(): void {
    this.showCategoryActionMenu = false;
    this.selectedCategoryForAction = null;
  }

  /**
   * Toggles the category platform dropdown
   * @param event The mouse event
   * @param category The category to show platforms for
   * @param categoryPlatformSelections The category platform selections map
   */
  toggleCategoryPlatformDropdown(
    event: MouseEvent,
    category: ExtendedCategoryRes,
    categoryPlatformSelections: Map<number, Set<number>>,
    allPlatforms: any[]
  ): void {
    event.stopPropagation();

    // Close dropdown if clicking on the same category
    if (this.showCategoryPlatformDropdown && this.selectedCategoryForPlatform?.id === category.id) {
      this.showCategoryPlatformDropdown = false;
      this.selectedCategoryForPlatform = null;
      return;
    }

    // Set the selected category and show the dropdown
    this.showCategoryPlatformDropdown = true;
    this.selectedCategoryForPlatform = category;

    // Always refresh platform selections for this category to ensure current platform is selected
    const selectedPlatforms = new Set<number>();

    // Find the platform that this category belongs to and pre-select it
    // Try multiple matching strategies to ensure we find the correct platform
    allPlatforms.forEach(platform => {
      // Match by exact name
      if (platform.name === category.platformName) {
        selectedPlatforms.add(platform.id);
      }
      // Match by trimmed name (in case of whitespace differences)
      else if (platform.name?.trim() === category.platformName?.trim()) {
        selectedPlatforms.add(platform.id);
      }
      // Match by case-insensitive name
      else if (platform.name?.toLowerCase() === category.platformName?.toLowerCase()) {
        selectedPlatforms.add(platform.id);
      }
    });

    // Always update the selections to ensure current platform is checked
    categoryPlatformSelections.set(category.id, selectedPlatforms);
  }

  /**
   * Closes the category platform dropdown
   */
  closeCategoryPlatformDropdown(): void {
    this.showCategoryPlatformDropdown = false;
    this.selectedCategoryForPlatform = null;
  }

  /**
   * Opens the modal
   */
  openModal(): void {
    this.showModal = true;
  }

  /**
   * Closes the modal
   */
  closeModal(): void {
    this.showModal = false;
  }

  /**
   * Opens the add service V2 modal
   */
  openAddServiceV2Modal(): void {
    this.showAddServiceV2Modal = true;
  }

  /**
   * Closes the add service V2 modal
   */
  closeAddServiceV2Modal(): void {
    this.showAddServiceV2Modal = false;
  }

  /**
   * Opens the add platform light modal
   */
  openAddPlatformLightModal(): void {
    this.showAddPlatformLightModal = true;
  }

  /**
   * Closes the add platform light modal
   */
  closeAddPlatformLightModal(): void {
    this.showAddPlatformLightModal = false;
  }

  /**
   * Opens the platform management modal
   */
  openPlatformManagementModal(): void {
    this.showPlatformManagementModal = true;
  }

  /**
   * Closes the platform management modal
   */
  closePlatformManagementModal(): void {
    this.showPlatformManagementModal = false;
  }

  /**
   * Opens the category selection modal
   */
  openCategorySelectionModal(): void {
    this.showCategorySelectionModal = true;
  }

  /**
   * Closes the category selection modal
   */
  closeCategorySelectionModal(): void {
    this.showCategorySelectionModal = false;
    this.selectedCategoryForMove = null;
    this.selectedServiceForAction = null;
  }

  /**
   * Opens the new category modal
   */
  openNewCategoryModal(): void {
    this.showNewCategoryModal = true;
  }

  /**
   * Closes the new category modal
   */
  closeNewCategoryModal(): void {
    this.showNewCategoryModal = false;
    this.categoryToEdit = null;
  }

  /**
   * Opens the new prices modal
   */
  openNewPricesModal(): void {
    this.showNewPricesModal = true;
  }

  /**
   * Closes the new prices modal
   */
  closeNewPricesModal(): void {
    this.showNewPricesModal = false;
  }

  /**
   * Opens the new special prices modal
   */
  openNewSpecialPricesModal(): void {
    this.showNewSpecialPricesModal = true;
    // Reset edit mode
    this.specialPriceToEdit = null;
  }

  /**
   * Opens the new special prices modal in edit mode
   */
  openNewSpecialPricesModalInEditMode(): void {
    this.showNewSpecialPricesModal = true;
  }

  /**
   * Closes the new special prices modal
   */
  closeNewSpecialPricesModal(): void {
    this.showNewSpecialPricesModal = false;
    this.specialPriceToEdit = null;
  }

  /**
   * Opens the import services modal
   */
  openImportServicesModal(): void {
    this.showImportServicesModal = true;
  }

  /**
   * Closes the import services modal
   */
  closeImportServicesModal(): void {
    this.showImportServicesModal = false;
  }

  /**
   * Opens the new service modal
   * @param serviceType Optional service type to open the modal with
   */
  openNewServiceModal(serviceType?: string): void {
    this.showNewServiceModal = true;
    // Store the service type to be used by the new service component
    this.selectedServiceType = serviceType || 'Provider';
    // Clear the selected service for action to ensure we're not in edit mode
    this.selectedServiceForAction = null;
  }

  /**
   * Opens the new service modal in edit mode
   * @param service The service to edit
   * @param serviceType The service type
   */
  openNewServiceModalForEdit(service: SuperGeneralSvRes, serviceType: string): void {
    this.showNewServiceModal = true;
    // Store the service type to be used by the new service component
    this.selectedServiceType = serviceType;
    // Store the service to edit
    this.selectedServiceForAction = service;

    console.log('Opening new service modal for edit with service:', service);
  }

  /**
   * Closes the new service modal
   */
  closeNewServiceModal(): void {
    this.showNewServiceModal = false;
    // Don't clear the selected service for action here
    // It will be needed when the modal is reopened
    console.log('closeNewServiceModal - keeping selectedServiceForAction:', this.selectedServiceForAction);
  }

  /**
   * Opens the special prices user modal
   */
  openSpecialPricesUserModal(): void {
    this.showSpecialPricesUserModal = true;
  }

  /**
   * Closes the special prices user modal
   */
  closeSpecialPricesUserModal(): void {
    this.showSpecialPricesUserModal = false;
  }

  /**
   * Opens the special prices service modal
   */
  openSpecialPricesServiceModal(): void {
    this.showSpecialPricesServiceModal = true;
  }

  /**
   * Closes the special prices service modal
   */
  closeSpecialPricesServiceModal(): void {
    this.showSpecialPricesServiceModal = false;
    this.selectedServiceForAction = null;
  }

  /**
   * Sets the loading state
   * @param isLoading Whether the component is loading
   * @param message Optional message to display with the loading indicator
   */
  setLoading(isLoading: boolean, message: string = ''): void {
    this.loading = isLoading;
    if (isLoading) {
      this.loadingService.show(message);
    } else {
      this.loadingService.hide();
    }
  }

  /**
   * Sets the bulk operation loading state
   * @param isLoading Whether the bulk operation is loading
   * @param message The message to display
   */
  setBulkOperationLoading(isLoading: boolean, message: string = ''): void {
    this.loadingBulkOperation = isLoading;
    this.bulkOperationMessage = message;

    if (isLoading) {
      this.loadingService.show(message);
    } else {
      this.loadingService.hide();
    }
  }

  /**
   * Opens the create promo code modal
   */
  openCreatePromoCodeModal(): void {
    this.showCreatePromoCodeModal = true;
  }

  /**
   * Closes the create promo code modal
   */
  closeCreatePromoCodeModal(): void {
    this.showCreatePromoCodeModal = false;
  }

  /**
   * Shows a toast notification
   * @param message The message to display
   * @param type The type of notification (success, warning, error)
   */
  showToast(message: string, type: string): void {
    this.toastService.showToast(message, type);
  }
}
