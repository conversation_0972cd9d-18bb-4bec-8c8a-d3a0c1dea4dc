import { Currency } from "./currency-res.model";

export interface TenantCurrencySettings {
  id?: number;
  currency_code: string;
  sync_enabled: boolean;
  payment_sync_enabled: boolean;
  custom_rate?: number;
  created_at?: string;
  updated_at?: string;
}

export interface TenantCurrencyRes {
  available_currencies: string[];
  all_currencies: Currency[];
  currency_settings: TenantCurrencySettings[];
  last_currency_sync?: string;
}
