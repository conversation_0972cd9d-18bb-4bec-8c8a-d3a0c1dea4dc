package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.constant.enums.Role;
import tndung.vnfb.smm.dto.request.UserReq;
import tndung.vnfb.smm.dto.response.ApiKeyUserRes;
import tndung.vnfb.smm.dto.response.GUserRes;
import tndung.vnfb.smm.dto.response.GUserSuperRes;
import tndung.vnfb.smm.entity.GUser;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class GUserMapperImpl implements GUserMapper {

    @Autowired
    private CurrencyMapper currencyMapper;

    @Override
    public ApiKeyUserRes toApiKey(GUser user) {
        if ( user == null ) {
            return null;
        }

        ApiKeyUserRes apiKeyUserRes = new ApiKeyUserRes();

        apiKeyUserRes.setAvatar( user.getAvatar() );
        apiKeyUserRes.setBalance( user.getBalance() );
        apiKeyUserRes.setCreatedAt( user.getCreatedAt() );
        apiKeyUserRes.setCustomDiscount( user.getCustomDiscount() );
        apiKeyUserRes.setCustomReferralRate( user.getCustomReferralRate() );
        apiKeyUserRes.setEmail( user.getEmail() );
        apiKeyUserRes.setId( user.getId() );
        apiKeyUserRes.setLastLoginAt( user.getLastLoginAt() );
        apiKeyUserRes.setMfaEnabled( user.getMfaEnabled() );
        apiKeyUserRes.setPhone( user.getPhone() );
        apiKeyUserRes.setPreferredCurrency( currencyMapper.toRes( user.getPreferredCurrency() ) );
        apiKeyUserRes.setStatus( user.getStatus() );
        apiKeyUserRes.setTotalOrder( user.getTotalOrder() );
        apiKeyUserRes.setUserName( user.getUserName() );
        apiKeyUserRes.setApiKey( user.getApiKey() );

        return apiKeyUserRes;
    }

    @Override
    public GUserRes toRes(GUser user) {
        if ( user == null ) {
            return null;
        }

        GUserRes gUserRes = new GUserRes();

        gUserRes.setAvatar( user.getAvatar() );
        gUserRes.setBalance( user.getBalance() );
        gUserRes.setCreatedAt( user.getCreatedAt() );
        gUserRes.setCustomDiscount( user.getCustomDiscount() );
        gUserRes.setCustomReferralRate( user.getCustomReferralRate() );
        gUserRes.setEmail( user.getEmail() );
        gUserRes.setId( user.getId() );
        gUserRes.setLastLoginAt( user.getLastLoginAt() );
        gUserRes.setMfaEnabled( user.getMfaEnabled() );
        gUserRes.setPhone( user.getPhone() );
        gUserRes.setPreferredCurrency( currencyMapper.toRes( user.getPreferredCurrency() ) );
        gUserRes.setStatus( user.getStatus() );
        gUserRes.setTotalOrder( user.getTotalOrder() );
        gUserRes.setUserName( user.getUserName() );

        return gUserRes;
    }

    @Override
    public List<GUserRes> toRes(List<GUser> users) {
        if ( users == null ) {
            return null;
        }

        List<GUserRes> list = new ArrayList<GUserRes>( users.size() );
        for ( GUser gUser : users ) {
            list.add( toRes( gUser ) );
        }

        return list;
    }

    @Override
    public GUserSuperRes toSuperRes(GUser user) {
        if ( user == null ) {
            return null;
        }

        GUserSuperRes gUserSuperRes = new GUserSuperRes();

        gUserSuperRes.setAvatar( user.getAvatar() );
        gUserSuperRes.setBalance( user.getBalance() );
        gUserSuperRes.setCreatedAt( user.getCreatedAt() );
        gUserSuperRes.setCustomDiscount( user.getCustomDiscount() );
        gUserSuperRes.setCustomReferralRate( user.getCustomReferralRate() );
        gUserSuperRes.setEmail( user.getEmail() );
        gUserSuperRes.setId( user.getId() );
        gUserSuperRes.setLastLoginAt( user.getLastLoginAt() );
        gUserSuperRes.setMfaEnabled( user.getMfaEnabled() );
        gUserSuperRes.setPhone( user.getPhone() );
        gUserSuperRes.setPreferredCurrency( currencyMapper.toRes( user.getPreferredCurrency() ) );
        gUserSuperRes.setTotalOrder( user.getTotalOrder() );
        gUserSuperRes.setUserName( user.getUserName() );
        gUserSuperRes.setApiKey( user.getApiKey() );
        gUserSuperRes.setLanguage( user.getLanguage() );
        List<Role> list = user.getRoles();
        if ( list != null ) {
            gUserSuperRes.setRoles( new ArrayList<Role>( list ) );
        }
        gUserSuperRes.setSecretKey( user.getSecretKey() );
        gUserSuperRes.setStatus( user.getStatus() );
        gUserSuperRes.setTimeZone( user.getTimeZone() );

        return gUserSuperRes;
    }

    @Override
    public List<GUserSuperRes> toSuperRes(List<GUser> users) {
        if ( users == null ) {
            return null;
        }

        List<GUserSuperRes> list = new ArrayList<GUserSuperRes>( users.size() );
        for ( GUser gUser : users ) {
            list.add( toSuperRes( gUser ) );
        }

        return list;
    }

    @Override
    public GUser toEntity(UserReq req) {
        if ( req == null ) {
            return null;
        }

        GUser gUser = new GUser();

        gUser.setEmail( req.getEmail() );
        gUser.setPassword( req.getPassword() );
        gUser.setPhone( req.getPhone() );
        gUser.setUserName( req.getUserName() );

        return gUser;
    }
}
