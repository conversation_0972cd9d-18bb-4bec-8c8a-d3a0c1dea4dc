import { Injectable, OnDestroy, inject } from '@angular/core';
import { BehaviorSubject, Subscription } from 'rxjs';
import { Clipboard } from '@angular/cdk/clipboard';

import { CategoriesService } from '../../core/services/categories.service';
import { OrderService, OrderSearchReq } from '../../core/services/order.service';
import { ToastService } from '../../core/services/toast.service';
import { CurrencyService } from '../../core/services/currency.service';
import { SuperPlatformRes } from '../../model/response/super-platform.model';
import { IconBaseModel } from '../../model/base-model';
import { OrderRes } from '../../model/response/order-res.model';
import { SuperGeneralSvRes } from '../../model/response/super-general-sv.model';
import { STATUS_FILTERS, StatusFilter } from '../../shared/constants/status-filters';

@Injectable()
export class OrdersLogicService implements OnDestroy {
  // Data properties
  searchTerm: string = '';
  selectedService: string = 'All';
  selectedServiceId: number | null = null;
  dateRange: { startDate: Date | null, endDate: Date | null } = {
    startDate: null,
    endDate: null
  };
  showFilters: boolean = false;
  viewMode: 'table' | 'card' = 'table';

  statusFilters: StatusFilter[] = [...STATUS_FILTERS];
  services: SuperGeneralSvRes[] = [];
  categories: IconBaseModel[] = [];
  allServices: SuperGeneralSvRes[] = [];
  platforms: SuperPlatformRes[] = [];
  selectAll: boolean = false;

  orders: OrderRes[] = [];
  selectedOrders: number[] = [];

  // Bulk refill confirmation
  showBulkRefillConfirmation = false;
  isBulkRefilling = false;

  // Pagination
  pagination = {
    pageNumber: 0,
    pageSize: 10,
    totalElements: 0,
    totalPages: 0
  };
  isLoading: boolean = false;
  private subscriptions: Subscription[] = [];

  private categoriesService = inject(CategoriesService);
  private clipboard = inject(Clipboard);
  private orderService = inject(OrderService);
  private toastService = inject(ToastService);
  private currencyService = inject(CurrencyService);

  constructor() {
    this.setupSubscriptions();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  // Format price with currency conversion
  formatPrice(price: number): string {
    return this.currencyService.formatPrice(price);
  }

  setupSubscriptions() {
    // Subscribe to orders data
    this.subscriptions.push(
      this.orderService.orders$.subscribe(orders => {
        this.orders = orders;
        this.selectedOrders = [];
        this.selectAll = false;
      })
    );

    // Subscribe to pagination data
    this.subscriptions.push(
      this.orderService.pagination$.subscribe(pagination => {
        this.pagination = pagination;
      })
    );

    // Subscribe to loading state
    this.subscriptions.push(
      this.orderService.loading$.subscribe(loading => {
        this.isLoading = loading;
      })
    );
  }

  /**
   * Detect if the device is mobile and set the view mode accordingly
   */
  detectMobileDevice(): void {
    if (window.innerWidth < 768) {
      this.viewMode = 'card';
    } else {
      this.viewMode = 'table';
    }
  }

  /**
   * Load orders with current filters
   */
  loadOrders(page: number = 0): void {
    const filter: OrderSearchReq = {
      page: page,
      size: this.pagination.pageSize
    };

    if (this.searchTerm) {
      filter.keyword = this.searchTerm;
    }

    // Get active status filter
    const activeStatusFilter = this.statusFilters.find(f => f.active);
    if (activeStatusFilter && activeStatusFilter.value !== 'all') {
      filter.status = activeStatusFilter.value;
    }

    // Add date range filter if set
    if (this.dateRange) {
      if (this.dateRange.startDate) {
        const year = this.dateRange.startDate.getFullYear();
        const month = (this.dateRange.startDate.getMonth() + 1).toString().padStart(2, '0');
        const day = this.dateRange.startDate.getDate().toString().padStart(2, '0');
        filter.from = `${year}-${month}-${day}`;
      }

      if (this.dateRange.endDate) {
        const year = this.dateRange.endDate.getFullYear();
        const month = (this.dateRange.endDate.getMonth() + 1).toString().padStart(2, '0');
        const day = this.dateRange.endDate.getDate().toString().padStart(2, '0');
        filter.to = `${year}-${month}-${day}`;
      }
    }

    // Add service ID filter if selected
    if (this.selectedServiceId) {
      filter.serviceId = this.selectedServiceId;
    }

    this.orderService.search(filter);
  }

  /**
   * Load platforms and categories from the API
   */
  loadPlatforms(): void {
    this.categoriesService.getPlatforms().subscribe({
      next: (platforms) => {
        this.platforms = platforms
          .filter(platform => !platform.hide)
          .sort((a, b) => a.sort - b.sort);

        // Create categories array from platforms data
        this.categories = [
          {
            id: 'all',
            label: 'filter.all_categories',
            sort: 0,
            icon: ''
          }
        ];
        this.allServices = [];

        platforms.forEach(platform => {
          const sortedCategories = [...platform.categories]
            .filter(category => !category.hide)
            .sort((a, b) => a.sort - b.sort);

          sortedCategories.forEach(category => {
            this.categories.push({
              id: category.id.toString(),
              sort: category.sort,
              label: category.name,
              icon: platform.icon
            });
            this.allServices.push(...category.services);
          });
        });

        // Select first category by default if available
        if (this.categories.length > 0) {
          this.onCategorySelected(this.categories[0]);
        }
      },
      error: (error) => {
        console.error('Error loading platforms:', error);
      }
    });
  }

  /**
   * Handle category selection and update services list
   */
  onCategorySelected(category: IconBaseModel): void {
    // Handle "All Categories" option
    if (category.id === 'all') {
      this.services = [
        SuperGeneralSvRes.create({
          id: -1,
          name: 'filter.all_services',
          description: '',
          price: 0
        }),
        ...this.allServices
      ];
      return;
    }

    // Find the selected category in platforms data and get its services
    for (const platform of this.platforms) {
      const foundCategory = platform.categories.find(c => c.id.toString() === category.id);
      if (foundCategory) {
        this.services = [
          SuperGeneralSvRes.create({
            id: -1,
            name: 'filter.all_services',
            description: '',
            price: 0
          }),
          ...foundCategory.services
        ];
        break;
      }
    }
  }

  toggleFilter(filter: any): void {
    this.statusFilters.forEach(f => f.active = false);
    filter.active = true;
    this.loadOrders();
  }

  /**
   * Handle service selection
   */
  onServiceSelected(service: SuperGeneralSvRes): void {
    if (service.id === -1) {
      this.selectedServiceId = null;
    } else {
      this.selectedServiceId = service.id;
    }
    this.loadOrders();
  }

  toggleAllOrders(): void {
    if (this.selectAll) {
      this.selectedOrders = this.orders.map(order => order.id);
    } else {
      this.selectedOrders = [];
    }
  }

  toggleOrderSelection(orderId: number): void {
    const index = this.selectedOrders.indexOf(orderId);
    if (index === -1) {
      this.selectedOrders.push(orderId);
    } else {
      this.selectedOrders.splice(index, 1);
    }
    this.selectAll = this.selectedOrders.length === this.orders.length;
  }

  isOrderSelected(orderId: number): boolean {
    return this.selectedOrders.includes(orderId);
  }

  applyFilters(): void {
    this.loadOrders(0);
  }

  /**
   * Handle date range changes from the date picker
   */
  onDateRangeChanged(dateRange: { startDate: Date | null, endDate: Date | null }): void {
    this.dateRange = dateRange;
  }

  resetFilters(): void {
    if (this.categories.length > 0) {
      this.onCategorySelected(this.categories[0]);
    }
    this.dateRange = {
      startDate: null,
      endDate: null
    };

    // Reset status filters
    this.statusFilters.forEach(f => f.active = false);
    this.statusFilters[0].active = true;

    this.searchTerm = '';
    this.selectedServiceId = null;
    this.loadOrders();
  }

  onSearch(searchTerm: string): void {
    this.searchTerm = searchTerm;
    this.loadOrders();
  }

  toggleFilters(): void {
    this.showFilters = !this.showFilters;
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'PENDING':
        return 'bg-gray-100 text-gray-800';
      case 'PROCESSING':
        return 'bg-blue-100 text-blue-800';
      case 'IN_PROGRESS':
        return 'bg-blue-100 text-blue-800';
      case 'PARTIAL':
        return 'bg-yellow-100 text-yellow-800';
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      case 'CANCELED':
        return 'bg-red-100 text-red-800';
      case 'FAILED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  /**
   * Navigate to a specific page
   */
  goToPage(page: number): void {
    if (page < 0 || page >= this.pagination.totalPages) {
      return;
    }
    this.loadOrders(page);
  }

  /**
   * Change the page size and reload orders
   */
  changePageSize(): void {
    this.loadOrders(0);
  }

  /**
   * Get the range of pages to display in pagination
   */
  getPageRange(): number[] {
    const totalPages = this.pagination.totalPages;
    const currentPage = this.pagination.pageNumber;

    if (totalPages <= 7) {
      return Array.from({ length: totalPages }, (_, i) => i);
    }

    let startPage = Math.max(1, currentPage - 1);
    let endPage = Math.min(totalPages - 2, currentPage + 1);

    if (currentPage <= 2) {
      endPage = 3;
    } else if (currentPage >= totalPages - 3) {
      startPage = totalPages - 4;
    }

    return Array.from(
      { length: endPage - startPage + 1 },
      (_, i) => startPage + i
    );
  }

  // Copy methods
  copyID(): void {
    if (this.orders.length === 0) {
      console.log('No orders to copy');
      return;
    }

    const ordersToCopy = this.selectedOrders.length > 0
      ? this.orders.filter(order => this.selectedOrders.includes(order.id))
      : this.orders;

    const formattedOrders = ordersToCopy.map(order => this.formatOrderForCopy(order)).join('\n');

    navigator.clipboard.writeText(formattedOrders)
    .then(() => {
      console.log('Orders copied to clipboard');
      this.toastService.showSuccess('Orders copied to clipboard successfully');
    })
    .catch(err => {
      console.error('Failed to copy orders: ', err);
      this.toastService.showError('Failed to copy orders: ' + err);
    });
  }

  formatOrderForCopy(order: OrderRes): string {
    const formattedDate = new Date(order.created_at).toLocaleString('en-US', {
      month: 'numeric',
      day: 'numeric',
      year: '2-digit',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });

    const price = this.currencyService.formatPrice(order.actual_charge || order.charge);
    return `ID: ${order.id} | ${formattedDate}\n${order.service.id} - ${order.service.name} - ${price}\n${order.link}`;
  }

  copySingleOrder(order: OrderRes): void {
    const formattedOrder = this.formatOrderForCopy(order);

    navigator.clipboard.writeText(formattedOrder)
    .then(() => {
      console.log('Order copied to clipboard');
      this.toastService.showSuccess('Order copied to clipboard successfully');
    })
    .catch(err => {
      console.error('Failed to copy order: ', err);
      this.toastService.showError('Failed to copy order: ' + err);
    });
  }

  copyId(): void {
    const selectedOrderIds = this.selectedOrders.map(id => id.toString()).join('\n');
    if (selectedOrderIds) {
      this.clipboard.copy(selectedOrderIds);
      console.log('Order IDs copied to clipboard');
      this.toastService.showSuccess('Order IDs copied to clipboard successfully');
    }
  }

  reorder(order: OrderRes): void {
    if (order && order.service && order.service.id) {
      console.log('Reordering service with ID:', order.service.id);
      window.location.href = `/dashboard/new?serviceId=${order.service.id}`;
    }
  }

  isRefillAvailable(order: OrderRes): boolean {
    if (!order.service.refill) {
      return false;
    }

    const validStatuses = ['completed', 'partial'];
    if (!validStatuses.includes(order.status.toLowerCase())) {
      return false;
    }

    if (order.service.refill_days && order.service.refill_days > 0) {
      const orderCreatedAt = new Date(order.created_at);
      const refillDeadline = new Date(orderCreatedAt.getTime() + (order.service.refill_days * 24 * 60 * 60 * 1000));
      const now = new Date();

      if (now > refillDeadline) {
        return false;
      }
    }

    return true;
  }

  refillOrder(order: OrderRes): void {
    if (!this.isRefillAvailable(order)) {
      this.toastService.showError('Refill is not available for this order');
      return;
    }

    order.loading = true;

    this.orderService.refillOrder(order.id).subscribe({
      next: (updatedOrder) => {
        console.log('Order refilled successfully:', updatedOrder);
        this.toastService.showSuccess('Order refilled successfully');

        const index = this.orders.findIndex(o => o.id === order.id);
        if (index !== -1) {
          this.orders[index] = { ...updatedOrder, loading: false };
        }
      },
      error: (error) => {
        console.error('Error refilling order:', error);
        const errorMessage = error?.message || 'Failed to refill order';
        this.toastService.showError(errorMessage);
        order.loading = false;
      }
    });
  }

  bulkRefillOrders(): void {
    const eligibleOrders = this.orders.filter(order => this.isRefillAvailable(order));

    if (eligibleOrders.length === 0) {
      this.toastService.showError('No orders are eligible for refill');
      return;
    }

    this.showBulkRefillConfirmation = true;
  }

  closeBulkRefillConfirmation(): void {
    this.showBulkRefillConfirmation = false;
    this.isBulkRefilling = false;
  }

  confirmBulkRefill(): void {
    const eligibleOrders = this.orders.filter(order => this.isRefillAvailable(order));

    if (eligibleOrders.length === 0) {
      this.toastService.showError('No orders are eligible for refill');
      this.closeBulkRefillConfirmation();
      return;
    }

    this.isBulkRefilling = true;
    eligibleOrders.forEach(order => order.loading = true);

    let successCount = 0;
    let errorCount = 0;
    let completedCount = 0;

    eligibleOrders.forEach(order => {
      this.orderService.refillOrder(order.id).subscribe({
        next: (updatedOrder) => {
          console.log('Order refilled successfully:', updatedOrder);
          successCount++;

          const index = this.orders.findIndex(o => o.id === order.id);
          if (index !== -1) {
            this.orders[index] = { ...updatedOrder, loading: false };
          }

          completedCount++;
          this.checkBulkRefillCompletion(eligibleOrders.length, completedCount, successCount, errorCount);
        },
        error: (error) => {
          console.error('Error refilling order:', error);
          errorCount++;
          order.loading = false;

          completedCount++;
          this.checkBulkRefillCompletion(eligibleOrders.length, completedCount, successCount, errorCount);
        }
      });
    });
  }

  private checkBulkRefillCompletion(totalCount: number, completedCount: number, successCount: number, errorCount: number): void {
    if (completedCount === totalCount) {
      if (successCount > 0 && errorCount === 0) {
        this.toastService.showSuccess(`Successfully refilled ${successCount} order(s)`);
      } else if (successCount > 0 && errorCount > 0) {
        this.toastService.showWarning(`Refilled ${successCount} order(s), ${errorCount} failed`);
      } else {
        this.toastService.showError(`Failed to refill all ${errorCount} order(s)`);
      }
      this.closeBulkRefillConfirmation();
    }
  }

  cancelOrder(order: OrderRes): void {
    order.loading = true;

    this.orderService.cancelOrder(order.id).subscribe({
      next: (updatedOrder) => {
        console.log('Order cancelled successfully:', updatedOrder);
        this.toastService.showSuccess('Order cancelled successfully');

        const index = this.orders.findIndex(o => o.id === order.id);
        if (index !== -1) {
          this.orders[index] = { ...updatedOrder, loading: false };
        }
      },
      error: (error) => {
        console.error('Error cancelling order:', error);
        const errorMessage = error?.message || 'Failed to cancel order';
        this.toastService.showError(errorMessage);
        order.loading = false;
      }
    });
  }

  updateNote(order: OrderRes, note: string): void {
    this.orderService.updateNote(order.id, note).subscribe({
      next: (updatedOrder) => {
        console.log('Order note updated successfully:', updatedOrder);
        this.toastService.showSuccess('Note updated successfully');

        const index = this.orders.findIndex(o => o.id === order.id);
        if (index !== -1) {
          this.orders[index] = { ...updatedOrder };
        }
      },
      error: (error) => {
        console.error('Error updating order note:', error);
        const errorMessage = error?.message || 'Failed to update note';
        this.toastService.showError(errorMessage);
      }
    });
  }

  onNoteBlur(order: OrderRes, event: Event): void {
    const target = event.target as HTMLTextAreaElement;
    if (target && target.value !== (order.note || '')) {
      this.updateNote(order, target.value);
    }
  }

  isShowCancelButton(order: OrderRes): boolean {
    return order.service.cancel_button &&
           order.status.toLowerCase() !== 'canceled' &&
           order.status.toLowerCase() !== 'cancelled' &&
           order.status.toLowerCase() !== 'canceled_without_refund' &&
           order.tag !== 'CANCEL';
  }
}
