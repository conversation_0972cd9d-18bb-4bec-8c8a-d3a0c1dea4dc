import { Routes } from '@angular/router';
import { AdminLayoutComponent } from './components/admin/layout/admin-layout.component';
import { AdminDashboardComponent } from './components/admin/dashboard/admin-dashboard.component';
import { LandingPageComponent } from './components/landing-page/landing-page.component';
import { LayoutAuthComponent } from './components/layout-auth/layout-auth.component';
import { AuthComponent } from './components/auth/auth.component';
import { SignUpComponent } from './components/sign-up/sign-up.component';
import { MfaComponent } from './components/mfa/mfa.component';
import { ErrorLayoutComponent } from './components/error/error-layout.component';
import { NotFoundComponent } from './components/error/not-found.component';
import { UnauthorizedComponent } from './components/error/unauthorized.component';
import { ForbiddenComponent } from './components/error/forbidden.component';
import { ServerErrorComponent } from './components/error/server-error.component';
import { GeneralComponent } from './components/settings/general/general.component';
import { ProfileComponent } from './components/profile/profile.component';
import { ProvidersComponent } from './components/settings/providers/providers.component';
import { PromoCodesComponent } from './components/admin/promo-codes/promo-codes.component';
import { DesignComponent } from './components/settings/design/design.component';
import { IntegrationsComponent } from './components/settings/integrations/integrations.component';
import { interactionRoutes } from './components/settings/interaction/interaction.routes';
import { promotionsRoutes } from './components/settings/promotions/promotions.routes';
import { AdminOrdersComponent } from './components/admin/orders/admin-orders.component';
import { AdminServiceComponent } from './components/admin/service/admin-service.component';
import { AdminServicesV2Component } from './components/admin/service/admin-services-v2.component';
import { AdminSupportComponent } from './components/admin/support/admin-support.component';
import { AdminUsersComponent } from './components/admin/users/admin-users.component';
import { SettingsLayoutComponent } from './components/settings/layout/settings-layout.component';
import { authGuard } from './core/guards/auth.guard';
import { adminRoleGuard } from './core/guards/admin-role.guard';
import { authRedirectGuard } from './core/guards/auth-redirect.guard';
import { mfaGuard } from './core/guards/mfa.guard';

export const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    component: LandingPageComponent
  },
  {
    path: 'panel',
    component: AdminLayoutComponent,
    canActivate: [authGuard, adminRoleGuard],
    children: [
      { path: '', pathMatch: 'full', redirectTo: 'dashboard' },
      { path: 'dashboard', component: AdminDashboardComponent },
      { path: 'orders', component: AdminOrdersComponent },
      { path: 'users', component: AdminUsersComponent },

      { path: 'services', component: AdminServicesV2Component },
      // { path: 'services-v2', component: AdminServicesV2Component },
      { path: 'tickets', component: AdminSupportComponent },
      { path: 'statistics', component: AdminDashboardComponent },

      // Settings routes
      {
        path: 'settings',
        component: SettingsLayoutComponent,
        children: [
          { path: '', pathMatch: 'full', redirectTo: 'general' },
          { path: 'general', component: GeneralComponent },
          { path: 'providers', component: ProvidersComponent },
          { path: 'profile', component: ProfileComponent },
          { path: 'interaction', children: interactionRoutes },
          { path: 'promotions', children: promotionsRoutes },
          { path: 'promo-codes', component: PromoCodesComponent },
          { path: 'integrations', component: IntegrationsComponent },
          { path: 'design', component: DesignComponent },
          { path: 'i18n', loadComponent: () => import('./components/settings/i18n-management/i18n-management.component').then(m => m.I18nManagementComponent) },
          { path: 'currency', loadComponent: () => import('./components/settings/currency-settings-page/currency-settings-page.component').then(m => m.CurrencySettingsPageComponent) },
          { path: 'all-panels', loadComponent: () => import('./components/settings/panels-setting/panels-setting.component').then(m => m.PanelsSettingComponent) },
          { path: 'managers', loadComponent: () => import('./components/admin/managers/managers.component').then(m => m.ManagersComponent) },
        ]
      },
    ]
  },
  {
    path: 'auth',
    component: LayoutAuthComponent,
    children: [
      {
        path: 'login',
        component: AuthComponent,
        canActivate: [authRedirectGuard]
      },
      {
        path: 'register',
        component: SignUpComponent,
        canActivate: [authRedirectGuard]
      },
      {
        path: 'mfa',
        component: MfaComponent,
        canActivate: [mfaGuard]
      },
    ]
  },
  // Error routes
  {
    path: 'error',
    component: ErrorLayoutComponent,
    children: [
      { path: '404', component: NotFoundComponent },
      { path: '401', component: UnauthorizedComponent },
      { path: '403', component: ForbiddenComponent },
      { path: '502', component: ServerErrorComponent },
      { path: '503', component: ServerErrorComponent },
    ]
  },
  // Wildcard route for 404
  {
    path: '**',
    redirectTo: '/error/404'
  }
];
