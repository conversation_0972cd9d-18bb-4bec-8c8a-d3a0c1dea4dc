/* Simple Cash Flow Theme - Clean & Minimal Design */
.simple-cash-flow-container {
  padding: 1.5rem;
  background-color: #f8fafc;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Header Section */
.page-header {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 1rem;
}

.title-section {
  flex: 1;
  min-width: 200px;
}

.page-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.25rem 0;
}

.page-subtitle {
  color: #718096;
  font-size: 0.875rem;
  margin: 0;
}

.stats-section {
  display: flex;
  gap: 1rem;
}

.stat-item {
  background: #4299e1;
  color: white;
  padding: 1rem 1.25rem;
  border-radius: 8px;
  text-align: center;
  min-width: 100px;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-number {
  font-size: 1.25rem;
  font-weight: 600;
}

.stat-label {
  font-size: 0.75rem;
  opacity: 0.9;
}

/* Filter Section */
.filter-container {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.filter-form {
  width: 100%;
}

.filter-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-label {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.text-input {
  padding: 0.625rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
  background: white;
}

.text-input:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.date-picker-input {
  height: 38px;
}

.dropdown-select {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  transition: border-color 0.2s ease;
}

.dropdown-select:focus-within {
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.dropdown-btn {
  padding: 0.625rem 0.75rem;
  font-size: 0.875rem;
}

.filter-actions {
  display: flex;
  align-items: end;
  gap: 0.5rem;
}

.btn {
  padding: 0.625rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
  min-width: 80px;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #4b5563;
}

.btn-primary {
  background: #4299e1;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #3182ce;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Content Section */
.content-wrapper {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

/* Loading State */
.loading-container {
  text-align: center;
  padding: 3rem 2rem;
  color: #718096;
}

.loading-spinner {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #4299e1;
}

.loading-text {
  margin: 0;
  font-size: 0.875rem;
}

/* Selection Bar */
.selection-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  background: #f7fafc;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-weight: 500;
  color: #374151;
}

.selection-info {
  color: #4299e1;
  font-weight: 500;
  font-size: 0.875rem;
}

/* Custom Checkbox Styles */
.checkbox-wrapper input[type="checkbox"] {
  display: none;
}

.checkbox-custom {
  width: 18px;
  height: 18px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background: white;
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkbox-custom {
  background: #4299e1;
  border-color: #4299e1;
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkbox-custom::after {
  content: '✓';
  color: white;
  font-weight: bold;
  font-size: 11px;
}

.checkbox-label {
  font-size: 0.875rem;
}

/* Desktop Table */
.table-container.desktop-view {
  display: none;
  overflow-x: auto;
}

@media (min-width: 768px) {
  .table-container.desktop-view {
    display: block;
  }
  .cards-container.mobile-view {
    display: none;
  }
}

@media (max-width: 767px) {
  .table-container.desktop-view {
    display: none;
  }
  .cards-container.mobile-view {
    display: block;
  }
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background: #f7fafc;
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 500;
  color: #374151;
  border-bottom: 1px solid #e2e8f0;
  font-size: 0.875rem;
}

.data-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e2e8f0;
  vertical-align: middle;
  font-size: 0.875rem;
}

.table-row {
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background: #f7fafc;
}

.col-checkbox {
  width: 50px;
}

.col-number {
  width: 80px;
  font-weight: 500;
  color: #374151;
}

.col-amount,
.col-balance {
  font-weight: 500;
  white-space: nowrap;
}

.amount-value {
  font-weight: 600;
}

.balance-flow {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.flow-arrow {
  color: #9ca3af;
  font-size: 0.75rem;
}

.col-description {
  max-width: 200px;
}

.description-text {
  color: #6b7280;
}

/* Mobile Cards */
.cards-container.mobile-view {
  display: block;
  padding: 1rem;
}

.transaction-card {
  background: white;
  border-radius: 8px;
  margin-bottom: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.card-header {
  background: #f7fafc;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e2e8f0;
}

.card-number {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.card-amount {
  font-weight: 600;
  font-size: 1rem;
}

.card-content {
  padding: 1rem;
}

.card-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f7fafc;
}

.card-row:last-child {
  border-bottom: none;
}

.row-label {
  font-weight: 500;
  color: #6b7280;
  font-size: 0.875rem;
  min-width: 100px;
}

.row-value {
  text-align: right;
  flex: 1;
  font-size: 0.875rem;
}

.balance-flow.mobile {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: flex-end;
  font-weight: 500;
}

/* Color Classes */
.text-green {
  color: #10b981;
}

.text-red {
  color: #ef4444;
}

.text-yellow {
  color: #f59e0b;
}

.text-blue {
  color: #3b82f6;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: #6b7280;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
  color: #9ca3af;
}

.empty-title {
  font-size: 1.25rem;
  font-weight: 500;
  color: #374151;
  margin: 0 0 0.5rem 0;
}

.empty-description {
  margin: 0;
  font-size: 0.875rem;
}

/* Pagination */
.pagination-wrapper {
  background: white;
  border-radius: 12px;
  padding: 1rem 1.5rem;
  margin-top: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 1rem;
}

.pagination-info {
  color: #6b7280;
  font-size: 0.875rem;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.page-btn {
  width: 36px;
  height: 36px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
  font-weight: 500;
  font-size: 0.875rem;
}

.page-btn:hover:not(:disabled) {
  border-color: #4299e1;
  color: #4299e1;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-btn.active {
  background: #4299e1;
  border-color: #4299e1;
  color: white;
}

.page-btn.prev,
.page-btn.next {
  font-size: 0.75rem;
}

.page-numbers {
  display: flex;
  gap: 0.25rem;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  font-size: 0.875rem;
}

.selector-label {
  font-size: 0.875rem;
}

.page-size-select {
  padding: 0.375rem 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.page-size-select:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .simple-cash-flow-container {
    padding: 1rem;
  }

  .page-header,
  .filter-container,
  .content-wrapper,
  .pagination-wrapper {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
  }

  .filter-row {
    grid-template-columns: 1fr;
  }

  .pagination-wrapper {
    flex-direction: column;
    text-align: center;
  }

  .pagination-controls {
    order: -1;
  }
}
