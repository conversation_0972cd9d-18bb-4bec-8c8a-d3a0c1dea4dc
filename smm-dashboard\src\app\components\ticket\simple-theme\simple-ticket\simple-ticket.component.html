<div class="simple-ticket-container">
  
  <!-- Header Section -->
  <div class="ticket-header">
    <div class="header-content">
      <div class="header-icon">
        <fa-icon [icon]='["fas", "headset"]'></fa-icon>
      </div>
      <div class="header-text">
        <h1 class="header-title">{{ 'ticket.support' | translate }}</h1>
        <p class="header-subtitle">{{ 'ticket.manage_support_requests' | translate }}</p>
      </div>
      <button class="new-ticket-btn" (click)="openModal()">
        <fa-icon [icon]='["fas", "plus"]' class="btn-icon"></fa-icon>
        <span>{{ 'ticket.new_ticket' | translate }}</span>
      </button>
    </div>
  </div>

  <!-- Search Section -->
  <div class="search-section">
    <div class="search-container">
      <app-simple-search-box
        [placeholder]="'ticket.search' | translate"
        [buttonText]="'ticket.search' | translate"
        buttonIcon="search"
        buttonPosition="right"
        [showButtonText]="true"
        containerClass="simple-ticket-search-container"
        inputClass="simple-ticket-search-input"
        buttonClass="simple-ticket-search-button"
        (searchEvent)="onSearch($event)">
      </app-simple-search-box>
    </div>
  </div>

  <!-- Content Section -->
  <div class="content-section">
    
    <!-- Loading State -->
    <div *ngIf="(state$ | async)?.isLoading" class="loading-container">
      <app-loading [size]="'lg'"></app-loading>
    </div>

    <!-- Tickets Grid -->
    <div *ngIf="!(state$ | async)?.isLoading" class="tickets-grid">
      
      <!-- Empty State -->
      <div *ngIf="(state$ | async)?.tickets?.length === 0" class="empty-state">
        <div class="empty-icon">
          <fa-icon [icon]='["fas", "ticket-alt"]'></fa-icon>
        </div>
        <h3 class="empty-title">{{ 'ticket.no_tickets' | translate }}</h3>
        <p class="empty-subtitle">{{ 'ticket.create_first_ticket' | translate }}</p>
        <button class="empty-action-btn" (click)="openModal()">
          <fa-icon [icon]='["fas", "plus"]' class="mr-2"></fa-icon>
          {{ 'ticket.new_ticket' | translate }}
        </button>
      </div>

      <!-- Ticket Cards -->
      <div *ngFor="let ticket of (state$ | async)?.tickets; trackBy: trackByTicketId" 
           class="ticket-card" 
           (click)="navigateTo(ticket.id)">
        
        <!-- Card Header -->
        <div class="card-header">
          <div class="ticket-id">
            <span class="id-label">#{{ ticket.id }}</span>
          </div>
          <div class="ticket-status">
            <app-ticket-status [status]="ticket.status"></app-ticket-status>
          </div>
        </div>

        <!-- Card Content -->
        <div class="card-content">
          <h3 class="ticket-subject">{{ ticket.subject }}</h3>
          <p class="ticket-description">
            {{ ticket.description | slice:0:120 }}{{ ticket.description.length > 120 ? '...' : '' }}
          </p>
        </div>

        <!-- Card Footer -->
        <div class="card-footer">
          <div class="update-time">
            <fa-icon [icon]='["fas", "clock"]' class="time-icon"></fa-icon>
            <span>{{ ticket.updated_at | date: 'short' }}</span>
          </div>
          <div class="card-action">
            <fa-icon [icon]='["fas", "arrow-right"]' class="action-icon"></fa-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <ng-container *ngIf="state$ | async as state">
      <div *ngIf="state.pagination && state.pagination.totalPages > 1" class="pagination-section">
        <div class="pagination-container">
          <!-- Previous Button -->
          <button
            [disabled]="state.pagination.pageNumber === 0"
            [ngClass]="state.pagination.pageNumber === 0 ? 'pagination-btn-disabled' : 'pagination-btn'"
            (click)="changePage((state.pagination.pageNumber || 0) - 1)">
            <fa-icon [icon]='["fas", "chevron-left"]'></fa-icon>
          </button>

          <!-- Page Numbers -->
          <ng-container *ngFor="let page of Array(state.pagination.totalPages > 5 ? 5 : state.pagination.totalPages || 0); let i = index">
            <button
              *ngIf="i < 3 || i >= state.pagination.totalPages - 2 || i === state.pagination.pageNumber"
              [ngClass]="i === state.pagination.pageNumber ? 'pagination-btn-active' : 'pagination-btn'"
              (click)="changePage(i)">
              {{ i + 1 }}
            </button>
            <span *ngIf="i === 3 && state.pagination.totalPages > 5" class="pagination-dots">...</span>
          </ng-container>

          <!-- Next Button -->
          <button
            [disabled]="state.pagination.pageNumber === state.pagination.totalPages - 1"
            [ngClass]="state.pagination.pageNumber === state.pagination.totalPages - 1 ? 'pagination-btn-disabled' : 'pagination-btn'"
            (click)="changePage((state.pagination.pageNumber || 0) + 1)">
            <fa-icon [icon]='["fas", "chevron-right"]'></fa-icon>
          </button>
        </div>
      </div>
    </ng-container>
  </div>

  <!-- New Ticket Modal -->
  <app-new-ticket 
    *ngIf="(state$ | async)?.showModal" 
    (close)="closeModal()" 
    (ticketCreated)="onTicketCreated($event)">
  </app-new-ticket>

</div>
