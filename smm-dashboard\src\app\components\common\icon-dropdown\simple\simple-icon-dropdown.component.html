<div class="simple-icon-dropdown-container" [ngClass]="customClassDropdown">
  
  <!-- Button -->
  <button
    type="button"
    class="simple-dropdown-button"
    [ngClass]="customClassButton"
    (click)="toggleDropdown($event)">

    <div class="button-content">
      <!-- Selected icon -->
      <div class="icon-section">
        <app-social-icon 
          *ngIf="currentSelectedOption?.icon" 
          [icon]="currentSelectedOption?.icon || 'facebook'"
          class="selected-icon">
        </app-social-icon>
      </div>

      <!-- Selected label -->
      <div class="label-section">
        <span class="selected-label">
          {{ (currentSelectedOption?.label || currentPlaceholder) | translate }}
        </span>
      </div>

      <!-- Dropdown arrow -->
      <div class="arrow-section">
        <fa-icon 
          [icon]='["fas", "angle-down"]'
          class="dropdown-arrow"
          [class.rotated]="isOpen">
        </fa-icon>
      </div>
    </div>
  </button>

  <!-- Dropdown menu -->
  <div *ngIf="isOpen" class="simple-dropdown-menu" (click)="$event.stopPropagation()">
    <ul class="options-list">
      <li *ngFor="let option of currentOptions"
          (click)="selectOption(option, $event)"
          class="option-item"
          [class.selected]="option.id === currentSelectedOption?.id">

        <!-- Option icon -->
        <div class="option-icon">
          <app-social-icon 
            *ngIf="option?.icon" 
            [icon]="option?.icon || 'facebook'"
            class="icon">
          </app-social-icon>
        </div>

        <!-- Option label -->
        <div class="option-label">
          <span>{{ option.label | translate }}</span>
        </div>

        <!-- Selected indicator -->
        <div class="selected-indicator" *ngIf="option.id === currentSelectedOption?.id">
          <fa-icon [icon]='["fas", "check"]' class="check-icon"></fa-icon>
        </div>

      </li>
    </ul>
  </div>

</div>
