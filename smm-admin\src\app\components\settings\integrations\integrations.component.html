<div class="integrations-settings">
  <!-- Header -->
  <div class="settings-header">
    <h1 class="settings-title">{{ 'integrations.title' | translate }}</h1>
    <p class="settings-description">{{ 'integrations.description' | translate }}</p>
  </div>

  <!-- Integrations List -->
  <div class="settings-card">
    <div class="card-header">
      <h2 class="card-title">
        <fa-icon icon="plug" class="section-icon"></fa-icon>
        {{ 'integrations.available_integrations' | translate }}
      </h2>
      <button
        class="add-custom-btn"
        (click)="openCustomIntegrationPopup()">
        <fa-icon icon="plus" class="mr-2"></fa-icon>
        {{ 'integrations.add_custom' | translate }}
      </button>
    </div>

    <div class="card-content">
      <!-- Loading State -->
      <app-loading *ngIf="loading" [message]="'integrations.loading_integrations' | translate"></app-loading>

      <!-- Integrations List -->
      <div *ngIf="!loading" class="integrations-list">
        <!-- Integration Items -->
        <div *ngFor="let integration of integrations" class="integration-item">
          <div class="integration-info">
            <div class="integration-icon">
              <!-- Custom image icon -->
              <img *ngIf="integration.token && isImageUrl(integration.token)"
                   [src]="integration.token"
                   [alt]="integration.type"
                   class="custom-icon-image">
              <!-- FontAwesome icon -->
              <fa-icon *ngIf="!integration.token || !isImageUrl(integration.token)"
                       [icon]="getIntegrationIcon(integration)"
                       [styles]="{'font-weight': 'fab'}"
                       class="icon"></fa-icon>
            </div>
            <div class="integration-details">
              <h3 class="integration-name">{{ getIntegrationName(integration.type) }}</h3>
              <p class="integration-status" *ngIf="integration.enabled">
                <span class="status-badge connected">{{ 'integrations.connected' | translate }}</span>
                <span *ngIf="integration.username" class="ml-2 text-ellipsis overflow-hidden">
                  <!-- Format based on integration type -->
                  <ng-container *ngIf="integration.type.toLowerCase() === 'telegram' && !integration.username.startsWith('@')">
                    &#64;{{ integration.username }}
                  </ng-container>
                  <ng-container *ngIf="integration.type.toLowerCase() === 'telegram' && integration.username.startsWith('@')">
                    {{ integration.username }}
                  </ng-container>
                  <ng-container *ngIf="integration.type.toLowerCase() !== 'telegram'">
                    {{ integration.username }}
                  </ng-container>
                </span>
              </p>
              <p class="integration-status" *ngIf="!integration.enabled">
                <span class="status-badge disconnected">{{ 'integrations.not_connected' | translate }}</span>
              </p>
            </div>
          </div>
          <div class="integration-actions">
            <button
              *ngIf="integration.enabled"
              class="edit-button"
              (click)="openConfigPopup(integration)">
              {{ 'integrations.edit' | translate }}
            </button>
            <button
              class="connect-button"
              [ngClass]="{'disconnect-button': integration.enabled}"
              (click)="connectIntegration(integration.type)">
              {{ integration.enabled ? ('integrations.disconnect' | translate) : ('integrations.connect' | translate) }}
            </button>
          </div>
        </div>

        <!-- No Integrations Message -->
        <div *ngIf="integrations.length === 0" class="no-integrations">
          <p>{{ 'integrations.no_integrations' | translate }}</p>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Integration Config Popup -->
<app-integration-config
  *ngIf="showConfigPopup && selectedIntegration"
  [integration]="selectedIntegration"
  (close)="closeConfigPopup()"
  (save)="saveIntegrationConfig($event)">
</app-integration-config>

<!-- Custom Integration Popup -->
<app-custom-integration
  *ngIf="showCustomIntegrationPopup"
  (close)="closeCustomIntegrationPopup()"
  (integrationAdded)="onCustomIntegrationAdded($event)">
</app-custom-integration>

<!-- Disconnect Confirmation Modal -->
<app-disconnect-confirmation
  *ngIf="showDeleteConfirmation && integrationToDisconnect"
  [itemName]="getIntegrationName(integrationToDisconnect.type)"
  [isLoading]="isDisconnecting"
  (close)="closeDeleteConfirmation()"
  (confirm)="confirmDisconnectIntegration()">
</app-disconnect-confirmation>
