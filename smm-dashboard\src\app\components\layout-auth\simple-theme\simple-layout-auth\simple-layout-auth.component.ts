import { Compo<PERSON>, OnInit, On<PERSON><PERSON>roy, Inject, PLATFORM_ID, HostListener } from '@angular/core';
import { RouterModule, Router } from '@angular/router';
import { AuthUtilsService } from '../../../../core/services/auth-utils.service';
import { isPlatformBrowser, CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { IconsModule } from '../../../../icons/icons.module';
import { AppAssetsService } from '../../../../core/services/app-assets.service';
import { TenantSettingsService } from '../../../../core/services/tenant-settings.service';
import { Subscription } from 'rxjs';

// Base component
import { BaseLayoutAuthComponent } from '../../base-layout-auth.component';

// Services
import { LayoutAuthLogicService } from '../../services/layout-auth-logic.service';

@Component({
  selector: 'app-simple-layout-auth',
  standalone: true,
  imports: [CommonModule, RouterModule, TranslateModule, IconsModule],
  templateUrl: './simple-layout-auth.component.html',
  styleUrl: './simple-layout-auth.component.css'
})
export class SimpleLayoutAuthComponent extends BaseLayoutAuthComponent implements OnInit, OnDestroy {
  // Legacy properties for compatibility
  currentLanguage = 'en';
  showLanguageDropdown = false;
  allLanguages = [
    { flag: 'fi fi-vn', code: 'vi', name: 'Tiếng Việt' },
    { flag: 'fi fi-us', code: 'en', name: 'English' },
    { flag: 'fi fi-cn', code: 'cn', name: '中文' }
  ];
  languages: any[] = []; // Will be populated from tenant settings

  // Logo
  logoUrl: string = 'assets/images/logo.png';
  private subscription = new Subscription();

  constructor(
    layoutAuthLogicService: LayoutAuthLogicService,
    private authUtils: AuthUtilsService,
    private router: Router,
    private translateService: TranslateService,
    @Inject(PLATFORM_ID) private platformId: Object,
    private appAssetsService: AppAssetsService,
    private tenantSettingsService: TenantSettingsService
  ) {
    super(layoutAuthLogicService);
  }

  ngOnInit(): void {
    super.ngOnInit();
    
    // Check if user is already authenticated
    if (isPlatformBrowser(this.platformId)) {
      if (this.authUtils.isAuthenticated() && !this.authUtils.isTokenExpired()) {
        console.log('SimpleLayoutAuth Component - User is already authenticated, redirecting');
        const redirectUrl = localStorage.getItem('redirectAfterLogin') || '/';
        this.router.navigate([redirectUrl]);
      }

      // Get the browser language or use default
      const browserLang = this.translateService.getBrowserLang();
      const defaultLang = browserLang && ['en', 'vi'].includes(browserLang) ? browserLang : 'en';

      // Use the language from localStorage if available, otherwise use the default
      const savedLang = localStorage.getItem('language');
      this.currentLanguage = savedLang || defaultLang;

      // Set the current language
      this.translateService.use(this.currentLanguage);
    }

    // Load available languages from tenant settings
    this.loadAvailableLanguages();

    // Subscribe to logo URL changes
    this.subscription.add(
      this.appAssetsService.logoUrl$.subscribe(url => {
        this.logoUrl = url;
      })
    );
  }

  ngOnDestroy(): void {
    super.ngOnDestroy();
    this.subscription.unsubscribe();
  }

  private loadAvailableLanguages(): void {
    this.tenantSettingsService.getTenantAvailableLanguages().subscribe({
      next: (availableLanguages: string[]) => {
        // Filter languages based on what's available for this tenant
        this.languages = this.allLanguages.filter(lang => availableLanguages.includes(lang.code));

        // Check if current language is still available
        if (!availableLanguages.includes(this.currentLanguage) && this.languages.length > 0) {
          // Current language not available, switch to first available language
          console.log('SimpleLayoutAuth: Current language not available, switching to:', this.languages[0].code);
          this.changeLanguage(this.languages[0].code);
        }

        console.log('SimpleLayoutAuth: Available languages loaded:', this.languages);
      },
      error: (error) => {
        console.error('Error loading available languages:', error);
        // Fallback to all languages if API fails
        this.languages = this.allLanguages;
      }
    });
  }

  // Toggle language dropdown
  toggleLanguageDropdown(): void {
    this.showLanguageDropdown = !this.showLanguageDropdown;
  }

  // Change language - override base method to include legacy logic
  override changeLanguage(langCode: string): void {
    super.changeLanguage(langCode);
    this.currentLanguage = langCode;
    this.translateService.use(langCode);
    localStorage.setItem('language', langCode);
    this.showLanguageDropdown = false;
  }

  // Get flag class for a language code
  getFlagClass(langCode: string): string {
    const lang = this.allLanguages.find(l => l.code === langCode);
    return lang ? lang.flag : 'fi fi-us'; // Default to US flag
  }

  // Close language dropdown when clicking outside
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    if (!target.closest('.relative')) {
      this.showLanguageDropdown = false;
    }
  }
}
