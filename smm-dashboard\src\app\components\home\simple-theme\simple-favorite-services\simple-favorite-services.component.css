.simple-favorite-services-container {
  @apply flex flex-col gap-4 p-0;
}

/* Service Selected State */
.service-selected {
  @apply flex flex-col gap-4;
}

.service-header {
  @apply flex items-center justify-between pb-3 border-b;
  border-color: rgba(30, 41, 59, 0.1);
}

.service-info {
  @apply flex flex-col gap-1;
}

.service-name {
  @apply text-lg font-semibold m-0;
  color: #1e293b;
}

.service-id {
  @apply text-sm px-2 py-1 rounded;
  color: #64748b;
  background: rgba(30, 41, 59, 0.05);
}

/* Favorite Toggle Wrapper */
.favorite-toggle-wrapper {
  @apply flex-shrink-0;
}

.favorite-checkbox-label {
  @apply flex items-center gap-2 cursor-pointer select-none;
  @apply px-3 py-2 rounded-lg transition-all duration-200;
}

.favorite-checkbox-label:hover {
  background: rgba(30, 41, 59, 0.05);
}

.favorite-checkbox {
  @apply sr-only;
}

.heart-icon {
  @apply text-xl transition-colors duration-200;
  color: #94a3b8;
}

.heart-icon.active {
  @apply text-red-500;
}

.favorite-text {
  @apply text-sm font-medium;
  color: #1e293b;
}

/* Hover and focus states */
.favorite-checkbox-label:hover .heart-icon {
  @apply text-red-400;
}

.favorite-checkbox-label:hover .heart-icon.active {
  @apply text-red-600;
}

.favorite-checkbox:focus + .heart-icon {
  @apply ring-2 ring-red-200 ring-offset-2 rounded;
}

/* Disabled state */
.favorite-checkbox:disabled + .heart-icon {
  @apply opacity-50 cursor-not-allowed;
}

.favorite-checkbox:disabled ~ .favorite-text {
  @apply opacity-50 cursor-not-allowed;
}

/* No Service Selected State */
.no-service-selected {
  @apply flex items-center justify-center min-h-[200px];
}

.empty-state {
  @apply text-center;
}

.empty-icon {
  @apply text-4xl mb-3;
  color: #94a3b8;
}

.empty-title {
  @apply text-lg font-medium mb-2;
  color: #64748b;
}

.empty-message {
  @apply text-sm;
  color: #94a3b8;
}

/* Responsive Design */
@media (min-width: 768px) {
  .simple-favorite-services-container {
    @apply p-6;
  }
}
