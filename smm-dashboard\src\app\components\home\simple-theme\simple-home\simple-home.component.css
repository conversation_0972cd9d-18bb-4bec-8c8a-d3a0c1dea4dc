/* Simple Classic Theme Styles */
.simple-classic-layout {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  padding: 1rem;
}

/* Notification Banner */
.notification-banner {
  margin-bottom: 1.5rem;
}

/* Platform Selection */
.platforms-section {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(20px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.platforms-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(30, 41, 59, 0.1), transparent);
}

.platforms-section:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.platforms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.platform-card {
  background: rgba(255, 255, 255, 0.95);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 1.25rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.platform-card:hover {
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.platform-card.platform-selected {
  border-color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2), 0 0 0 3px rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.platform-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.platform-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.1), rgba(15, 23, 42, 0.1));
  border-radius: 12px;
  border: 1px solid rgba(30, 41, 59, 0.15);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.platform-details {
  flex: 1;
}

.platform-name {
  font-size: 1rem;
  font-weight: 500;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.platform-services {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
}

/* Main Content */
.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.content-wrapper {
  display: contents;
}

.content-primary,
.content-secondary {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Panel Styles */
.panel {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.4);
  overflow: hidden;
  backdrop-filter: blur(20px);
  transition: all 0.3s ease;
}

.panel:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.panel-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  padding: 1.25rem 1.75rem;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.03), rgba(15, 23, 42, 0.03));
  border-bottom: 1px solid rgba(30, 41, 59, 0.08);
  position: relative;
}

.panel-title::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(30, 41, 59, 0.1), transparent);
}

.panel-body {
  padding: 1.75rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .simple-classic-layout {
    padding: 0.5rem;
  }
  
  .main-content {
    grid-template-columns: 1fr;
  }
  
  .platforms-grid {
    grid-template-columns: 1fr;
  }
  
  .platform-card {
    padding: 0.75rem;
  }
  
  .panel-body {
    padding: 1rem;
  }
  
  .platforms-section,
  .panel {
    margin-bottom: 1rem;
  }
}
