/* Simple API Doc Theme - Clean & Minimal Design */
.simple-api-doc-container {
  padding: 1.5rem;
  background-color: #f8fafc;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Header */
.page-header {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  text-align: center;
}

.page-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

/* Content Layout */
.content-layout {
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Sidebar */
.sidebar {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  height: fit-content;
  position: sticky;
  top: 1.5rem;
}

.sidebar-content {
  width: 100%;
}

.sidebar-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 1rem 0;
}

.services-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.service-item {
  padding: 0.625rem 0.75rem;
  margin-bottom: 0.25rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
  font-weight: 500;
  font-size: 0.875rem;
}

.service-item:hover {
  background: #f7fafc;
  color: #4299e1;
}

.service-item.active {
  background: #4299e1;
  color: white;
}

/* Main Content */
.main-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.content-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 1rem 0;
}

/* API Details */
.details-grid {
  display: grid;
  gap: 1rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f7fafc;
  border-radius: 6px;
  gap: 1rem;
}

.detail-label {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
  min-width: 120px;
}

.detail-value {
  color: #1a202c;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  flex: 1;
  text-align: right;
}

.detail-value.method {
  background: #dbeafe;
  color: #1e40af;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 600;
  text-align: center;
  max-width: 80px;
}

.api-key-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  justify-content: flex-end;
}

.api-key {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  word-break: break-all;
  background: #f3f4f6;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.refresh-button {
  padding: 0.5rem;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
}

.refresh-button:hover:not(:disabled) {
  background: #3182ce;
}

.refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Parameters Table */
.table-wrapper {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.params-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.params-table th {
  background: #f7fafc;
  color: #374151;
  padding: 0.75rem;
  text-align: left;
  font-weight: 500;
  font-size: 0.875rem;
  border-bottom: 1px solid #e2e8f0;
}

.params-table td {
  padding: 0.75rem;
  border-bottom: 1px solid #e2e8f0;
  vertical-align: top;
  font-size: 0.875rem;
}

.param-name {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 500;
  color: #4299e1;
  background: #f0f9ff;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  display: inline-block;
}

.param-description {
  color: #6b7280;
  line-height: 1.5;
}

/* Code Block */
.code-wrapper {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.code-block {
  background: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  overflow-x: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .simple-api-doc-container {
    padding: 1rem;
  }

  .content-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .sidebar {
    position: static;
    order: 2;
  }

  .main-content {
    order: 1;
  }

  .content-card {
    padding: 1rem;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .detail-row {
    flex-direction: column;
    align-items: stretch;
    text-align: left;
  }

  .detail-value {
    text-align: left;
  }

  .api-key-row {
    justify-content: flex-start;
  }
}
