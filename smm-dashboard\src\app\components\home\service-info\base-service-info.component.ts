import { Component, <PERSON><PERSON>ni<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Subscription } from 'rxjs';
import { ServiceSelectionService } from '../../../core/services/service-selection.service';
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';

@Component({
  template: '', // Will be overridden by child components
})
export abstract class BaseServiceInfoComponent implements OnInit, OnDestroy {
  selectedService: SuperGeneralSvRes | undefined;
  private serviceSelectionSubscription: Subscription | undefined;

  constructor(protected serviceSelectionService: ServiceSelectionService) {}

  ngOnInit(): void {
    // Subscribe to service selection events
    this.serviceSelectionSubscription = this.serviceSelectionService.serviceSelected$.subscribe(service => {
      this.selectedService = service;
      console.log('ServiceInfoComponent received selected service:', service);
    });
  }

  ngOnDestroy(): void {
    // Clean up subscription to prevent memory leaks
    if (this.serviceSelectionSubscription) {
      this.serviceSelectionSubscription.unsubscribe();
    }
  }

  // Helper methods that can be used by child components
  get exampleUrl(): string {
    return this.selectedService?.sample_link || 'https://www.youtube.com/watch?v=iW6Z7xqZO5s';
  }

  get startTime(): string {
    return 'Ngay tức thì'; // Can be made dynamic based on service
  }

  get warranty(): string {
    return '365 ngày hoàn trả'; // Can be made dynamic based on service
  }

  get speed(): string {
    if (this.selectedService?.speed_per_day && this.selectedService.speed_per_day > 0) {
      return `${this.selectedService.speed_per_day} / 1 ngày`;
    }
    return 'Tăng 50k/ 1 ngày';
  }

  get avgTime(): string {
    if (this.selectedService?.average_time) {
      return `${this.selectedService.average_time} phút`;
    }
    return '7 phút';
  }

  get description(): string {
    return this.selectedService?.description || 'Lorem ipsum dolor sit amet consectetur. Etiam non id ultrices pellentesque nec a.';
  }
}
