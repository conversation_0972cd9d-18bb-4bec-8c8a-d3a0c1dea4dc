import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Observable } from 'rxjs';
import { ProfileLogicService, ProfileState } from './services/profile-logic.service';

@Component({
  template: '', // Will be overridden by child components
})
export abstract class BaseProfileComponent implements OnInit, OnDestroy {
  // Profile logic state for all themes
  profileState$: Observable<ProfileState>;

  constructor(protected profileLogicService: ProfileLogicService) {
    this.profileState$ = this.profileLogicService.state$;
  }

  ngOnInit(): void {
    // ProfileLogicService handles all initialization
  }

  ngOnDestroy(): void {
    // ProfileLogicService is singleton, no cleanup needed for individual components
  }

  // Delegate methods to ProfileLogicService for template compatibility
  setActiveTab(tab: 'account' | 'security' | 'settings' | 'history'): void {
    this.profileLogicService.setActiveTab(tab);
  }

  togglePasswordVisibility(field: 'current' | 'new' | 'confirm'): void {
    this.profileLogicService.togglePasswordVisibility(field);
  }

  getAvatarPath(): string {
    return this.profileLogicService.getAvatarPath();
  }

  // Form submission methods - to be implemented by child components
  abstract onProfileSubmit(): void;
  abstract onPasswordSubmit(): void;
  abstract onCurrencyChange(currency: string): void;
  abstract onLanguageChange(language: string): void;
  abstract loadLoginHistory(): void;
  abstract changePage(page: number): void;
  abstract toggle2FA(): void;
  abstract verifyMFA(): void;
  abstract disableMFA(): void;
}
