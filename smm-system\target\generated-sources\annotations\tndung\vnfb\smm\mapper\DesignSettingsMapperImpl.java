package tndung.vnfb.smm.mapper;

import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.dto.design.ColorSchemeDto;
import tndung.vnfb.smm.dto.design.DesignSettingsDto;
import tndung.vnfb.smm.dto.design.FaviconDto;
import tndung.vnfb.smm.dto.design.HeaderDto;
import tndung.vnfb.smm.dto.design.LandingSettingsDto;
import tndung.vnfb.smm.dto.design.LogoDto;
import tndung.vnfb.smm.dto.design.SidebarDto;
import tndung.vnfb.smm.entity.setting.ColorScheme;
import tndung.vnfb.smm.entity.setting.DesignSettings;
import tndung.vnfb.smm.entity.setting.Favicon;
import tndung.vnfb.smm.entity.setting.HeaderSettings;
import tndung.vnfb.smm.entity.setting.LandingSettings;
import tndung.vnfb.smm.entity.setting.Logo;
import tndung.vnfb.smm.entity.setting.SidebarSettings;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class DesignSettingsMapperImpl implements DesignSettingsMapper {

    @Override
    public DesignSettings toEntity(DesignSettingsDto dto) {
        if ( dto == null ) {
            return null;
        }

        DesignSettings designSettings = new DesignSettings();

        designSettings.setColorScheme( toEntity( dto.getColorScheme() ) );
        designSettings.setFavicon( toEntity( dto.getFavicon() ) );
        designSettings.setHeaderSettings( toEntity( dto.getHeaderSettings() ) );
        designSettings.setId( dto.getId() );
        designSettings.setLandingSettings( toEntity( dto.getLandingSettings() ) );
        designSettings.setLogo( toEntity( dto.getLogo() ) );
        designSettings.setSidebarSettings( toEntity( dto.getSidebarSettings() ) );

        return designSettings;
    }

    @Override
    public DesignSettingsDto toDto(DesignSettings entity) {
        if ( entity == null ) {
            return null;
        }

        DesignSettingsDto designSettingsDto = new DesignSettingsDto();

        designSettingsDto.setColorScheme( toDto( entity.getColorScheme() ) );
        designSettingsDto.setFavicon( toDto( entity.getFavicon() ) );
        designSettingsDto.setHeaderSettings( toDto( entity.getHeaderSettings() ) );
        designSettingsDto.setId( entity.getId() );
        designSettingsDto.setLandingSettings( toDto( entity.getLandingSettings() ) );
        designSettingsDto.setLogo( toDto( entity.getLogo() ) );
        designSettingsDto.setSidebarSettings( toDto( entity.getSidebarSettings() ) );

        return designSettingsDto;
    }

    @Override
    public Logo toEntity(LogoDto dto) {
        if ( dto == null ) {
            return null;
        }

        Logo logo = new Logo();

        logo.setFileType( dto.getFileType() );
        logo.setUrl( dto.getUrl() );

        return logo;
    }

    @Override
    public LogoDto toDto(Logo entity) {
        if ( entity == null ) {
            return null;
        }

        LogoDto logoDto = new LogoDto();

        logoDto.setFileType( entity.getFileType() );
        logoDto.setUrl( entity.getUrl() );

        return logoDto;
    }

    @Override
    public Favicon toEntity(FaviconDto dto) {
        if ( dto == null ) {
            return null;
        }

        Favicon favicon = new Favicon();

        favicon.setFileType( dto.getFileType() );
        favicon.setUrl( dto.getUrl() );

        return favicon;
    }

    @Override
    public FaviconDto toDto(Favicon entity) {
        if ( entity == null ) {
            return null;
        }

        FaviconDto faviconDto = new FaviconDto();

        faviconDto.setFileType( entity.getFileType() );
        faviconDto.setUrl( entity.getUrl() );

        return faviconDto;
    }

    @Override
    public ColorScheme toEntity(ColorSchemeDto dto) {
        if ( dto == null ) {
            return null;
        }

        ColorScheme colorScheme = new ColorScheme();

        colorScheme.setPrimary( dto.getPrimary() );
        colorScheme.setTextOnButtons( dto.getTextOnButtons() );

        return colorScheme;
    }

    @Override
    public ColorSchemeDto toDto(ColorScheme entity) {
        if ( entity == null ) {
            return null;
        }

        ColorSchemeDto colorSchemeDto = new ColorSchemeDto();

        colorSchemeDto.setPrimary( entity.getPrimary() );
        colorSchemeDto.setTextOnButtons( entity.getTextOnButtons() );

        return colorSchemeDto;
    }

    @Override
    public LandingSettings toEntity(LandingSettingsDto dto) {
        if ( dto == null ) {
            return null;
        }

        LandingSettings landingSettings = new LandingSettings();

        landingSettings.setType( dto.getType() );

        return landingSettings;
    }

    @Override
    public LandingSettingsDto toDto(LandingSettings entity) {
        if ( entity == null ) {
            return null;
        }

        LandingSettingsDto landingSettingsDto = new LandingSettingsDto();

        landingSettingsDto.setType( entity.getType() );

        return landingSettingsDto;
    }

    @Override
    public SidebarSettings toEntity(SidebarDto dto) {
        if ( dto == null ) {
            return null;
        }

        SidebarSettings sidebarSettings = new SidebarSettings();

        sidebarSettings.setType( dto.getType() );

        return sidebarSettings;
    }

    @Override
    public SidebarDto toDto(SidebarSettings entity) {
        if ( entity == null ) {
            return null;
        }

        SidebarDto sidebarDto = new SidebarDto();

        sidebarDto.setType( entity.getType() );

        return sidebarDto;
    }

    @Override
    public HeaderSettings toEntity(HeaderDto dto) {
        if ( dto == null ) {
            return null;
        }

        HeaderSettings headerSettings = new HeaderSettings();

        headerSettings.setType( dto.getType() );

        return headerSettings;
    }

    @Override
    public HeaderDto toDto(HeaderSettings entity) {
        if ( entity == null ) {
            return null;
        }

        HeaderDto headerDto = new HeaderDto();

        headerDto.setType( entity.getType() );

        return headerDto;
    }
}
