<div class="floating-contact-container">
  <!-- Loading indicator -->
  <div *ngIf="loading" class="loading-indicator">
    <div class="spinner"></div>
  </div>

  <!-- Left side buttons -->
  <div class="floating-buttons-left">
    <button
      *ngFor="let integration of leftIntegrations"
      class="floating-contact-button"
      [title]="'integrations.' + integration.key.toLowerCase() | translate"
      (click)="openIntegrationLink(integration)"
      [style.background-color]="getIntegrationColor(integration.key)">
      <!-- Custom image icon -->
      <img *ngIf="integration.icon && isImageUrl(integration.icon)"
           [src]="integration.icon"
           [alt]="integration.key"
           class="contact-icon-image">
      <!-- FontAwesome icon -->
      <fa-icon *ngIf="!integration.icon || !isImageUrl(integration.icon)"
        [icon]="getIntegrationIcon(integration)"
        class="contact-icon">
      </fa-icon>
    </button>
  </div>

  <!-- Right side buttons -->
  <div class="floating-buttons-right">
    <button
      *ngFor="let integration of rightIntegrations"
      class="floating-contact-button"
      [title]="'integrations.' + integration.key.toLowerCase() | translate"
      (click)="openIntegrationLink(integration)"
      [style.background-color]="getIntegrationColor(integration.key)">
      <!-- Custom image icon -->
      <img *ngIf="integration.icon && isImageUrl(integration.icon)"
           [src]="integration.icon"
           [alt]="integration.key"
           class="contact-icon-image">
      <!-- FontAwesome icon -->
      <fa-icon *ngIf="!integration.icon || !isImageUrl(integration.icon)"
        [icon]="getIntegrationIcon(integration)"
        class="contact-icon">
      </fa-icon>
    </button>
  </div>
</div>
