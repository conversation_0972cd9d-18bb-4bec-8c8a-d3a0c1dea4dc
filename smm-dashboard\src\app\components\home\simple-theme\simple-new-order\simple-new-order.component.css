.simple-new-order-container {
  @apply flex flex-col gap-4 p-0;
}

/* Action Buttons */
.action-buttons {
  @apply flex gap-2;
}

.action-btn {
  @apply px-4 py-2 rounded-lg border text-sm font-medium transition-colors;
  @apply border-slate-700/20 text-slate-800 bg-white/80;
}

.action-btn.active {
  @apply bg-slate-800 text-white border-slate-800;
}

.action-btn:hover:not(.active) {
  @apply bg-slate-800/5 border-slate-700/30;
}

/* Search Section */
.search-section {
  @apply relative;
}

.search-input-wrapper {
  @apply relative;
}

.search-icon {
  @apply absolute left-3 top-1/2 -translate-y-1/2 text-slate-500;
}

.search-input {
  @apply w-full pl-10 pr-4 py-3 border rounded-lg;
  @apply border-slate-700/20 bg-white/80 text-slate-800;
}

.search-input:focus {
  @apply outline-none border-slate-800;
  box-shadow: 0 0 0 3px rgba(30, 41, 59, 0.1);
}

/* Form Sections */
.form-section {
  @apply flex flex-col gap-2;
}

.section-title {
  @apply text-sm font-semibold text-slate-800;
}

.form-input {
  @apply w-full px-4 py-3 border rounded-lg;
  @apply border-slate-700/20 bg-white/80 text-slate-800;
}

.form-input:focus {
  @apply outline-none border-slate-800;
  box-shadow: 0 0 0 3px rgba(30, 41, 59, 0.1);
}

.form-input.error {
  @apply border-red-500 focus:ring-red-500 focus:border-red-500;
}

.form-input.success {
  @apply border-green-500 focus:ring-green-500 focus:border-green-500;
}

.form-input.disabled {
  @apply bg-gray-100 text-gray-500 cursor-not-allowed;
}

.form-textarea {
  @apply w-full px-4 py-3 border rounded-lg min-h-[120px] resize-y;
  @apply border-slate-700/20 bg-white/80 text-slate-800;
}

.form-textarea:focus {
  @apply outline-none border-slate-800;
  box-shadow: 0 0 0 3px rgba(30, 41, 59, 0.1);
}

.form-textarea.error {
  @apply border-red-500;
}

.form-textarea.error:focus {
  @apply border-red-500;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-help {
  @apply text-sm text-slate-500;
}

/* Voucher Input Group */
.voucher-input-group {
  @apply flex gap-2;
}

.voucher-input-group .form-input {
  @apply flex-1;
}

.voucher-btn {
  @apply px-4 py-2 rounded-lg text-sm font-medium transition-colors;
}

.voucher-btn.apply {
  @apply bg-slate-800 text-white hover:bg-slate-900;
}

.voucher-btn.apply:disabled {
  @apply bg-slate-400 cursor-not-allowed;
}

.voucher-btn.clear {
  @apply bg-red-500 text-white hover:bg-red-600;
}

/* Submit Button */
.submit-btn {
  @apply w-full flex items-center justify-between px-6 py-4 rounded-lg;
  @apply bg-slate-800 text-white font-medium transition-colors;
}

.submit-btn:hover {
  @apply bg-slate-900;
}

.submit-btn:focus {
  @apply outline-none;
  box-shadow: 0 0 0 3px rgba(30, 41, 59, 0.3);
}

.submit-btn.disabled,
.submit-btn:disabled {
  @apply bg-gray-300 text-gray-500 cursor-not-allowed;
}

/* Price Display */
.price-display {
  @apply flex items-center gap-2;
}

.original-price {
  @apply line-through text-gray-300 text-sm;
}

.discount-badge {
  @apply text-xs px-2 py-1 rounded text-white;
}

.discount-badge.service {
  @apply bg-red-500;
}

.discount-badge.voucher {
  @apply bg-green-500;
}

.final-price {
  @apply font-semibold;
}

/* Empty State */
.empty-state {
  @apply flex items-center justify-center p-4 bg-gray-50 rounded-lg;
}

.empty-state p {
  @apply text-sm text-gray-500;
}

/* Messages */
.error-text {
  @apply text-sm text-red-500;
}

.success-text {
  @apply text-sm text-green-500;
}

.error-message {
  @apply text-red-500 text-center p-3 bg-red-50 rounded-lg;
}