<div class="simple-dropdown-container" [ngClass]="customClassDropdown">

  <button class="simple-dropdown-button" (click)="toggleDropdown($event)" [ngClass]="customClassButton">
    <app-simple-service-label [lite]="lite" [service]="selectedOption"></app-simple-service-label>
    <fa-icon class="dropdown-arrow" [icon]='["fas", "angle-down"]'></fa-icon>
  </button>

  <div *ngIf="isOpen" class="simple-dropdown-menu" (click)="$event.stopPropagation()">
    <ul class="dropdown-list">
      <li *ngFor="let option of options" 
          (click)="selectOption(option, $event)"
          class="dropdown-item">
        <div class="dropdown-item-content">
          <app-simple-service-label [lite]="lite" [service]="option"></app-simple-service-label>
        </div>
      </li>
    </ul>
  </div>

</div>
