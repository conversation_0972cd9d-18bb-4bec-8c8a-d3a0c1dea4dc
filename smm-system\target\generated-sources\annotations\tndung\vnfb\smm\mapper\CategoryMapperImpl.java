package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.processing.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.dto.request.CategoryReq;
import tndung.vnfb.smm.dto.response.category.CategoryRes;
import tndung.vnfb.smm.dto.response.category.SuperCategoryRes;
import tndung.vnfb.smm.dto.response.service.ServiceRes;
import tndung.vnfb.smm.dto.response.service.SuperServiceRes;
import tndung.vnfb.smm.entity.Category;
import tndung.vnfb.smm.entity.Category.CategoryBuilder;
import tndung.vnfb.smm.entity.GService;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class CategoryMapperImpl implements CategoryMapper {

    @Autowired
    private GSvMapper gSvMapper;

    @Override
    public Category toEntity(CategoryReq req) {
        if ( req == null ) {
            return null;
        }

        CategoryBuilder category = Category.builder();

        category.name( req.getName() );
        category.status( req.getStatus() );

        return category.build();
    }

    @Override
    public CategoryRes toRes(Category entity) {
        if ( entity == null ) {
            return null;
        }

        CategoryRes categoryRes = new CategoryRes();

        categoryRes.setServices( gServiceListToServiceResList( entity.getServices() ) );
        categoryRes.setDescription( entity.getDescription() );
        if ( entity.getId() != null ) {
            categoryRes.setId( entity.getId().intValue() );
        }
        categoryRes.setName( entity.getName() );
        categoryRes.setSort( entity.getSort() );

        return categoryRes;
    }

    @Override
    public List<CategoryRes> toRes(List<Category> entity) {
        if ( entity == null ) {
            return null;
        }

        List<CategoryRes> list = new ArrayList<CategoryRes>( entity.size() );
        for ( Category category : entity ) {
            list.add( toRes( category ) );
        }

        return list;
    }

    @Override
    public Set<CategoryRes> toRes(Set<Category> entity) {
        if ( entity == null ) {
            return null;
        }

        Set<CategoryRes> set = new HashSet<CategoryRes>( Math.max( (int) ( entity.size() / .75f ) + 1, 16 ) );
        for ( Category category : entity ) {
            set.add( toRes( category ) );
        }

        return set;
    }

    @Override
    public SuperCategoryRes toSuperRes(Category entity) {
        if ( entity == null ) {
            return null;
        }

        SuperCategoryRes superCategoryRes = new SuperCategoryRes();

        superCategoryRes.setServices( gServiceListToSuperServiceResList( entity.getServices() ) );
        superCategoryRes.setDescription( entity.getDescription() );
        if ( entity.getId() != null ) {
            superCategoryRes.setId( entity.getId().intValue() );
        }
        superCategoryRes.setName( entity.getName() );
        superCategoryRes.setSort( entity.getSort() );
        superCategoryRes.setStatus( entity.getStatus() );

        return superCategoryRes;
    }

    @Override
    public List<SuperCategoryRes> toSuperRes(List<Category> entity) {
        if ( entity == null ) {
            return null;
        }

        List<SuperCategoryRes> list = new ArrayList<SuperCategoryRes>( entity.size() );
        for ( Category category : entity ) {
            list.add( toSuperRes( category ) );
        }

        return list;
    }

    @Override
    public Set<SuperCategoryRes> toSuperRes(Set<Category> entity) {
        if ( entity == null ) {
            return null;
        }

        Set<SuperCategoryRes> set = new HashSet<SuperCategoryRes>( Math.max( (int) ( entity.size() / .75f ) + 1, 16 ) );
        for ( Category category : entity ) {
            set.add( toSuperRes( category ) );
        }

        return set;
    }

    protected List<ServiceRes> gServiceListToServiceResList(List<GService> list) {
        if ( list == null ) {
            return null;
        }

        List<ServiceRes> list1 = new ArrayList<ServiceRes>( list.size() );
        for ( GService gService : list ) {
            list1.add( gSvMapper.toRes( gService ) );
        }

        return list1;
    }

    protected List<SuperServiceRes> gServiceListToSuperServiceResList(List<GService> list) {
        if ( list == null ) {
            return null;
        }

        List<SuperServiceRes> list1 = new ArrayList<SuperServiceRes>( list.size() );
        for ( GService gService : list ) {
            list1.add( gSvMapper.toSuperRes( gService ) );
        }

        return list1;
    }
}
