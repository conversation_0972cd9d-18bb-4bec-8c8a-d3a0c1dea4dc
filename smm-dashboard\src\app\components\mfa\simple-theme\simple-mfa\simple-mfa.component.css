/* Simple theme specific styles for MFA */
.mfa-digit-input {
  transition: all 0.2s ease-in-out;
}

.mfa-digit-input:focus {
  transform: scale(1.05);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.mfa-digit-input:not(:placeholder-shown) {
  background-color: #f3f4f6;
  border-color: #10b981;
}

/* Animation for error state */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.error-shake {
  animation: shake 0.3s ease-in-out;
}

/* Simple button hover effects */
.simple-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Loading animation */
.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
