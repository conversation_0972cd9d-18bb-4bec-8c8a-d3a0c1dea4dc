/* Simple Theme Global Loading Styles */
.simple-global-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.simple-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.simple-loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #ffffff;
  border-radius: 50%;
  animation: simple-spin 1s linear infinite;
}

.simple-loading-message {
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 500;
  text-align: center;
  max-width: 300px;
}

@keyframes simple-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
