.notification-dropdown-container {
  @apply relative;
}

.notification-bell {
  @apply relative p-2 rounded-full hover:bg-gray-100 transition-colors duration-200;
}

.notification-badge {
  @apply absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center font-medium;
  font-size: 10px;
}

.notification-dropdown {
  @apply absolute right-0 top-full mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-[9999];
  max-height: 500px;
}

.triangle-pointer {
  @apply absolute -top-2 right-4 w-4 h-4 bg-white border-l border-t border-gray-200 transform rotate-45;
}

.dropdown-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200;
}

.header-left {
  @apply flex items-center gap-2;
}

.header-title {
  @apply text-lg font-semibold text-gray-800;
}

.unread-count {
  @apply text-sm text-gray-500;
}

.header-actions {
  @apply flex items-center gap-2;
}

.filter-btn {
  @apply px-3 py-1 text-sm rounded-md border border-gray-300 hover:bg-gray-50 transition-colors duration-200;
}

.filter-btn.active {
  @apply bg-blue-50 border-blue-300 text-blue-600;
}

.mark-all-btn {
  @apply px-3 py-1 text-sm text-blue-600 hover:text-blue-800 transition-colors duration-200;
}

.loading-container {
  @apply flex items-center justify-center gap-2 p-8 text-gray-500;
}

.loading-spinner {
  @apply w-5 h-5 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin;
}

.notifications-list {
  @apply max-h-80 overflow-y-auto;
}

.notification-item {
  @apply flex items-start gap-3 p-4 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors duration-200;
}

.notification-item.unread {
  @apply bg-blue-50;
}

.notification-icon {
  @apply flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full bg-gray-100;
}

.notification-content {
  @apply flex-1 min-w-0;
}

.notification-title {
  @apply font-medium text-gray-800 text-sm mb-1;
}

.notification-message {
  @apply text-gray-600 text-sm line-clamp-2 mb-1;
}

.notification-time {
  @apply text-xs text-gray-400;
}

.unread-indicator {
  @apply w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mt-2;
}

.empty-state {
  @apply flex flex-col items-center justify-center p-8 text-gray-500;
}

.empty-icon {
  @apply text-4xl mb-2 text-gray-300;
}

.empty-message {
  @apply text-sm text-center;
}

.dropdown-footer {
  @apply p-3 border-t border-gray-200;
}

.view-all-btn {
  @apply w-full py-2 text-sm text-center text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors duration-200;
}

.overlay {
  @apply fixed inset-0 z-[9998];
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .notification-dropdown {
    @apply w-80 right-0;
  }
}

/* Custom scrollbar for notifications list */
.notifications-list::-webkit-scrollbar {
  width: 4px;
}

.notifications-list::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

.notifications-list::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

.notifications-list::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}
