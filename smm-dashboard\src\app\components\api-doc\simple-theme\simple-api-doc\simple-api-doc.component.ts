import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { BaseApiDocComponent } from '../../base-api-doc.component';
import { ApiDocLogicService } from '../../api-doc.service';

@Component({
  selector: 'app-simple-api-doc',
  standalone: true,
  imports: [CommonModule, TranslateModule, FontAwesomeModule],
  templateUrl: './simple-api-doc.component.html',
  styleUrls: ['./simple-api-doc.component.css'],
  providers: [ApiDocLogicService]
})
export class SimpleApiDocComponent extends BaseApiDocComponent implements OnInit {
  constructor(apiDocLogic: ApiDocLogicService) {
    super(apiDocLogic);
  }

  override ngOnInit(): void {
    super.ngOnInit();
  }
}
