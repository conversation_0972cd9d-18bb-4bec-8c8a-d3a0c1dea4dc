.overlay-black {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal);
  padding: 20px;
  overflow-y: auto; /* Enable scrolling on the overlay */
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.modal-container {
  @apply w-full max-w-md mx-auto;
  margin: 20px 0; /* Add vertical margin for better spacing */
}

.modal-content {
  @apply bg-white rounded-2xl shadow-lg overflow-hidden p-6;
}

.modal-header {
  @apply flex justify-between items-center mb-6;
  position: relative;
}

.modal-title {
  @apply text-xl font-semibold text-[var(--gray-800)];
}

.close-button {
  @apply p-1 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors;
  position: absolute;
  top: 0;
  right: 0;
}

.platform-form {
  @apply space-y-6;
}

.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-[var(--gray-700)];
}

.form-input {
  @apply w-full px-4 py-3 bg-[#f5f7fc] border border-transparent rounded-lg focus:border-[var(--primary)] focus:ring-1 focus:ring-[var(--primary)] outline-none text-[var(--gray-800)];
}

.input-error {
  @apply border-red-500 focus:border-red-500 focus:ring-red-500;
}

.validation-error {
  @apply text-red-500 text-xs mt-1;
}

.error-message {
  @apply bg-red-100 text-red-700 p-3 rounded-lg mb-4 text-sm;
}

.icon-selector {
  @apply relative;
}

.selected-icon {
  @apply flex items-center justify-between w-full px-4 py-3 bg-[#f5f7fc] border border-transparent rounded-lg cursor-pointer hover:border-[var(--primary)];
}

.icon-display {
  @apply flex items-center gap-2;
}

.folder-icon {
  @apply text-[var(--primary)] mr-2;
}

.no-icon-text, .icon-name {
  @apply flex items-center text-[var(--gray-800)];
}

.dropdown-arrow {
  @apply text-gray-500;
}

/* Icon Grid Styles */
.icon-grid-container {
  @apply bg-white border rounded-lg shadow-lg overflow-hidden;
  max-height: 400px;
  width: 350px;
  position: fixed;
  z-index: 10000; /* Higher than any other elements */
  top: auto; /* Will be set by JS */
  left: auto; /* Will be set by JS */
  display: flex;
  flex-direction: column-reverse; /* Header at bottom, content at top */
}

.icon-grid-header {
  @apply px-4 py-2 bg-[var(--primary)] border-t flex items-center justify-between;
  order: 2; /* Ensure header appears below grid in flex-direction: column-reverse */
}

.icon-grid-header h3 {
  @apply text-sm font-medium text-white;
}

.icon-grid-close-btn {
  @apply flex items-center justify-center w-6 h-6 text-white hover:bg-white hover:bg-opacity-20 rounded transition-colors;
}

.icon-grid-close-btn svg {
  @apply w-4 h-4;
}

.icon-grid {
  @apply grid gap-2 p-3 overflow-y-auto;
  max-height: 350px;
  grid-template-columns: repeat(6, 1fr);
  order: 1; /* Ensure grid appears above header in flex-direction: column-reverse */
  /* Custom scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

/* Webkit scrollbar styling */
.icon-grid::-webkit-scrollbar {
  width: 6px;
}

.icon-grid::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 3px;
}

.icon-grid::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.icon-grid::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

.icon-grid-item {
  @apply flex items-center justify-center rounded-md cursor-pointer hover:bg-[#f0f2f5] transition-colors;
  width: 48px;
  height: 48px;
}

.icon-grid-item fa-icon {
  @apply text-xl text-[var(--gray-800)];
}

.icon-grid-item.selected {
  @apply bg-[var(--primary)] bg-opacity-30;
}

.save-button {
  @apply w-full bg-[var(--primary)] text-white font-medium py-3 px-4 rounded-lg hover:bg-[var(--primary-hover)] active:bg-[var(--primary-active)] transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed;
}

/* Upload Icon Styles */
.upload-icon-section {
  @apply mb-3;
}

.upload-icon-btn {
  @apply flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
  font-size: 14px;
}

.upload-icon-btn svg {
  @apply flex-shrink-0;
}

/* Uploaded Icon Display Styles */
.uploaded-icon-preview {
  @apply w-5 h-5 object-contain rounded;
}

.uploaded-icon-grid {
  @apply w-6 h-6 object-contain rounded;
}
