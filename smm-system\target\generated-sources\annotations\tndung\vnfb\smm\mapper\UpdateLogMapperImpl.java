package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.dto.response.UpdateLogRes;
import tndung.vnfb.smm.entity.UpdateLog;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class UpdateLogMapperImpl implements UpdateLogMapper {

    @Override
    public UpdateLogRes toDto(UpdateLog entity) {
        if ( entity == null ) {
            return null;
        }

        UpdateLogRes updateLogRes = new UpdateLogRes();

        updateLogRes.setCreatedAt( entity.getCreatedAt() );
        updateLogRes.setId( entity.getId() );
        updateLogRes.setPriceChangeFrom( entity.getPriceChangeFrom() );
        updateLogRes.setPriceChangeTo( entity.getPriceChangeTo() );
        updateLogRes.setService( entity.getService() );
        updateLogRes.setStatus( entity.getStatus() );

        return updateLogRes;
    }

    @Override
    public List<UpdateLogRes> toDto(List<UpdateLog> entities) {
        if ( entities == null ) {
            return null;
        }

        List<UpdateLogRes> list = new ArrayList<UpdateLogRes>( entities.size() );
        for ( UpdateLog updateLog : entities ) {
            list.add( toDto( updateLog ) );
        }

        return list;
    }
}
