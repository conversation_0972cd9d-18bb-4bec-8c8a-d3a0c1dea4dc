<div class="simple-orders-container">
  <!-- Header -->
  <div class="simple-header">
    <h1 class="simple-title">{{ 'simple_theme.orders.title' | translate }}</h1>
    <div class="simple-actions">
      <button
        class="simple-btn simple-btn-primary"
        (click)="toggleFilters()">
        <i class="fas fa-filter"></i>
        {{ 'simple_theme.common.filter' | translate }}
      </button>
    </div>
  </div>

  <!-- Filters Section -->
  <div class="simple-filters" [class.show]="showFilters">
    <div class="simple-filter-grid">
      <!-- Search -->
      <div class="simple-filter-item">
        <label class="simple-label">{{ 'simple_theme.common.search' | translate }}</label>
        <app-search-box
          [placeholder]="'simple_theme.orders.search_placeholder' | translate"
          (searchEvent)="onSearch($event)">
        </app-search-box>
      </div>

      <!-- Date Range -->
      <div class="simple-filter-item">
        <label class="simple-label">{{ 'simple_theme.common.date_range' | translate }}</label>
        <app-date-range-picker
          (dateRangeChanged)="onDateRangeChanged($event)">
        </app-date-range-picker>
      </div>

      <!-- Category -->
      <div class="simple-filter-item">
        <label class="simple-label">{{ 'simple_theme.common.category' | translate }}</label>
        <app-icon-dropdown
          [options]="categories"
          [placeholder]="'simple_theme.filter.select_category' | translate"
          (selected)="onCategorySelected($event)">
        </app-icon-dropdown>
      </div>

      <!-- Service -->
      <div class="simple-filter-item">
        <label class="simple-label">{{ 'simple_theme.common.service' | translate }}</label>
        <app-service-dropdown
          [options]="services"
          [placeholder]="'simple_theme.filter.select_service' | translate"
          (selected)="onServiceSelected($event)">
        </app-service-dropdown>
      </div>
    </div>

    <!-- Filter Actions -->
    <div class="simple-filter-actions">
      <button
        class="simple-btn simple-btn-secondary"
        (click)="resetFilters()">
        {{ 'simple_theme.common.reset' | translate }}
      </button>
      <button
        class="simple-btn simple-btn-primary"
        (click)="applyFilters()">
        {{ 'simple_theme.common.apply' | translate }}
      </button>
    </div>
  </div>

  <!-- Status Filters -->
  <div class="simple-status-filters">
    <button 
      *ngFor="let filter of statusFilters"
      class="simple-status-btn"
      [class.active]="filter.active"
      (click)="toggleFilter(filter)">
      {{ filter.label | translate }}
    </button>
  </div>

  <!-- Bulk Actions -->
  <div class="simple-bulk-actions" *ngIf="selectedOrders.length > 0">
    <span class="simple-selected-count">
      {{ selectedOrders.length }} {{ 'simple_theme.orders.selected' | translate }}
    </span>
    <div class="simple-bulk-buttons">
      <button
        class="simple-btn simple-btn-outline"
        (click)="copyId()">
        <i class="fas fa-copy"></i>
        {{ 'simple_theme.orders.copy_ids' | translate }}
      </button>
      <button
        class="simple-btn simple-btn-outline"
        (click)="CopyID()">
        <i class="fas fa-clipboard"></i>
        {{ 'simple_theme.orders.copy_details' | translate }}
      </button>
      <button
        class="simple-btn simple-btn-warning"
        (click)="bulkRefillOrders()">
        <i class="fas fa-redo"></i>
        {{ 'simple_theme.orders.bulk_refill' | translate }}
      </button>
    </div>
  </div>

  <!-- Loading -->
  <app-loading *ngIf="isLoading"></app-loading>

  <!-- Orders List -->
  <div class="simple-orders-list" *ngIf="!isLoading">
    <!-- Desktop Table View -->
    <div class="simple-table" *ngIf="viewMode === 'table'">
      <div class="simple-table-header">
        <div class="simple-table-cell simple-checkbox-cell">
          <input
            type="checkbox"
            class="simple-checkbox"
            [checked]="selectAll"
            (change)="toggleAllOrders()">
        </div>
        <div class="simple-table-cell">{{ 'simple_theme.orders.id' | translate }}</div>
        <div class="simple-table-cell">{{ 'simple_theme.orders.service' | translate }}</div>
        <div class="simple-table-cell">{{ 'simple_theme.orders.link' | translate }}</div>
        <div class="simple-table-cell">{{ 'simple_theme.orders.quantity' | translate }}</div>
        <div class="simple-table-cell">{{ 'simple_theme.orders.charge' | translate }}</div>
        <div class="simple-table-cell">{{ 'simple_theme.orders.status' | translate }}</div>
        <div class="simple-table-cell">{{ 'simple_theme.orders.created' | translate }}</div>
        <div class="simple-table-cell">{{ 'simple_theme.common.actions' | translate }}</div>
      </div>

      <div class="simple-table-row" *ngFor="let order of orders">
        <div class="simple-table-cell simple-checkbox-cell">
          <input 
            type="checkbox" 
            class="simple-checkbox"
            [checked]="isOrderSelected(order.id)"
            (change)="toggleOrderSelection(order.id)">
        </div>
        <div class="simple-table-cell">
          <span class="simple-order-id">#{{ order.id }}</span>
        </div>
        <div class="simple-table-cell">
          <div class="simple-service-info">
            <span class="simple-service-name">{{ order.service.name }}</span>
            <span class="simple-service-id">ID: {{ order.service.id }}</span>
          </div>
        </div>
        <div class="simple-table-cell">
          <a [href]="order.link" target="_blank" class="simple-link">
            {{ order.link | slice:0:30 }}...
          </a>
        </div>
        <div class="simple-table-cell">{{ order.quantity }}</div>
        <div class="simple-table-cell">
          <span class="simple-price">{{ formatPrice(order.actual_charge || order.charge) }}</span>
        </div>
        <div class="simple-table-cell">
          <span class="simple-status" [ngClass]="getStatusClass(order.status)">
            {{ order.status }}
          </span>
        </div>
        <div class="simple-table-cell">
          <span class="simple-date">{{ order.created_at | date:'short' }}</span>
        </div>
        <div class="simple-table-cell">
          <div class="simple-actions-menu">
            <button 
              class="simple-action-btn"
              (click)="copySingleOrder(order)">
              <i class="fas fa-copy"></i>
            </button>
            <button 
              class="simple-action-btn"
              (click)="reorder(order)">
              <i class="fas fa-redo"></i>
            </button>
            <button 
              *ngIf="isRefillAvailable(order)"
              class="simple-action-btn simple-refill-btn"
              (click)="refillOrder(order)"
              [disabled]="order.loading">
              <i class="fas fa-sync" [class.fa-spin]="order.loading"></i>
            </button>
            <button 
              *ngIf="isShowCancelButton(order)"
              class="simple-action-btn simple-cancel-btn"
              (click)="cancelOrder(order)"
              [disabled]="order.loading">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile Card View -->
    <div class="simple-cards" *ngIf="viewMode === 'card'">
      <div class="simple-card" *ngFor="let order of orders">
        <div class="simple-card-header">
          <input 
            type="checkbox" 
            class="simple-checkbox"
            [checked]="isOrderSelected(order.id)"
            (change)="toggleOrderSelection(order.id)">
          <span class="simple-order-id">#{{ order.id }}</span>
          <span class="simple-status" [ngClass]="getStatusClass(order.status)">
            {{ order.status }}
          </span>
        </div>
        
        <div class="simple-card-content">
          <div class="simple-card-row">
            <span class="simple-label">{{ 'simple_theme.orders.service' | translate }}:</span>
            <span>{{ order.service.name }}</span>
          </div>
          <div class="simple-card-row">
            <span class="simple-label">{{ 'simple_theme.orders.quantity' | translate }}:</span>
            <span>{{ order.quantity }}</span>
          </div>
          <div class="simple-card-row">
            <span class="simple-label">{{ 'simple_theme.orders.charge' | translate }}:</span>
            <span class="simple-price">{{ formatPrice(order.actual_charge || order.charge) }}</span>
          </div>
          <div class="simple-card-row">
            <span class="simple-label">{{ 'simple_theme.orders.created' | translate }}:</span>
            <span>{{ order.created_at | date:'short' }}</span>
          </div>
        </div>

        <div class="simple-card-actions">
          <button 
            class="simple-btn simple-btn-sm"
            (click)="copySingleOrder(order)">
            <i class="fas fa-copy"></i>
          </button>
          <button 
            class="simple-btn simple-btn-sm"
            (click)="reorder(order)">
            <i class="fas fa-redo"></i>
          </button>
          <button 
            *ngIf="isRefillAvailable(order)"
            class="simple-btn simple-btn-sm simple-refill-btn"
            (click)="refillOrder(order)"
            [disabled]="order.loading">
            <i class="fas fa-sync" [class.fa-spin]="order.loading"></i>
          </button>
          <button 
            *ngIf="isShowCancelButton(order)"
            class="simple-btn simple-btn-sm simple-cancel-btn"
            (click)="cancelOrder(order)"
            [disabled]="order.loading">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div class="simple-empty-state" *ngIf="orders.length === 0">
      <i class="fas fa-inbox simple-empty-icon"></i>
      <h3>{{ 'simple_theme.orders.no_orders' | translate }}</h3>
      <p>{{ 'simple_theme.orders.no_orders_desc' | translate }}</p>
    </div>
  </div>

  <!-- Pagination -->
  <div class="simple-pagination" *ngIf="pagination.totalPages > 1">
    <button 
      class="simple-page-btn"
      [disabled]="pagination.pageNumber === 0"
      (click)="goToPage(0)">
      <i class="fas fa-angle-double-left"></i>
    </button>
    <button 
      class="simple-page-btn"
      [disabled]="pagination.pageNumber === 0"
      (click)="goToPage(pagination.pageNumber - 1)">
      <i class="fas fa-angle-left"></i>
    </button>
    
    <span class="simple-page-info">
      {{ pagination.pageNumber + 1 }} / {{ pagination.totalPages }}
    </span>
    
    <button 
      class="simple-page-btn"
      [disabled]="pagination.pageNumber >= pagination.totalPages - 1"
      (click)="goToPage(pagination.pageNumber + 1)">
      <i class="fas fa-angle-right"></i>
    </button>
    <button 
      class="simple-page-btn"
      [disabled]="pagination.pageNumber >= pagination.totalPages - 1"
      (click)="goToPage(pagination.totalPages - 1)">
      <i class="fas fa-angle-double-right"></i>
    </button>
  </div>
</div>

<!-- Bulk Refill Confirmation Modal -->
<app-delete-confirmation
  *ngIf="showBulkRefillConfirmation"
  [itemName]="'eligible orders for refill'"
  [isLoading]="isBulkRefilling"
  (close)="closeBulkRefillConfirmation()"
  (confirm)="confirmBulkRefill()">
</app-delete-confirmation>
