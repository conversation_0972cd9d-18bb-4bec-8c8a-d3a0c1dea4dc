import { Injectable, HostListener, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject, Subscription, firstValueFrom } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';

// Services
import { LanguageService } from '../../../../core/services/language.service';
import { ThemeService, LayoutTheme } from '../../../../core/services/theme.service';

export interface LangDropdownState {
  // UI state
  isOpen: boolean;
  currentHeaderStyle: string;
  
  // Language data
  selectedFlag: string;
  allLanguages: Array<{ flag: string; code: string }>;
  languages: Array<{ flag: string; code: string }>;
  
  // Theme management
  currentTheme: LayoutTheme;
}

@Injectable({
  providedIn: 'root'
})
export class LangDropdownLogicService {
  private subscriptions: Subscription[] = [];

  // Predefined languages list (same as admin)
  private readonly predefinedLanguages = [
    { flag: 'fi fi-vn', code: 'vi' }, // Tiếng Vi<PERSON>t
    { flag: 'fi fi-us', code: 'en' }, // English
    { flag: 'fi fi-cn', code: 'cn' }, // 中文
    { flag: 'fi fi-fr', code: 'fr' }, // Français
    { flag: 'fi fi-es', code: 'es' }, // Español
    { flag: 'fi fi-de', code: 'de' }, // Deutsch
    { flag: 'fi fi-jp', code: 'ja' }, // 日本語
    { flag: 'fi fi-kr', code: 'ko' }, // 한국어
    { flag: 'fi fi-ru', code: 'ru' }, // Русский
    { flag: 'fi fi-pt', code: 'pt' }, // Português
    { flag: 'fi fi-it', code: 'it' }, // Italiano
    { flag: 'fi fi-nl', code: 'nl' }, // Nederlands
    { flag: 'fi fi-pl', code: 'pl' }, // Polski
    { flag: 'fi fi-tr', code: 'tr' }, // Türkçe
    { flag: 'fi fi-ar', code: 'ar' }, // العربية
    { flag: 'fi fi-th', code: 'th' }, // ไทย
    { flag: 'fi fi-id', code: 'id' }, // Bahasa Indonesia
    { flag: 'fi fi-my', code: 'ms' }, // Bahasa Malaysia
    { flag: 'fi fi-ph', code: 'tl' }, // Filipino
    { flag: 'fi fi-in', code: 'hi' }  // हिन्दी
  ];

  // State management
  private _state$ = new BehaviorSubject<LangDropdownState>({
    isOpen: false,
    currentHeaderStyle: 'standard',
    selectedFlag: 'fi fi-vn',
    allLanguages: this.predefinedLanguages,
    languages: this.predefinedLanguages,
    currentTheme: LayoutTheme.DEFAULT
  });

  // Public state observable
  public readonly state$ = this._state$.asObservable();

  // Current state getter
  private get currentState(): LangDropdownState {
    return this._state$.value;
  }

  constructor(
    private translate: TranslateService,
    private languageService: LanguageService,
    private themeService: ThemeService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.initialize();
  }

  // Public getters for template access
  get isOpen(): boolean {
    return this.currentState.isOpen;
  }

  get currentHeaderStyle(): string {
    return this.currentState.currentHeaderStyle;
  }

  get selectedFlag(): string {
    return this.currentState.selectedFlag;
  }

  get languages(): Array<{ flag: string; code: string }> {
    return this.currentState.languages;
  }

  get currentTheme(): LayoutTheme {
    return this.currentState.currentTheme;
  }

  // Initialize service
  private initialize(): void {
    this.subscribeToHeaderStyleChanges();
    this.subscribeToThemeChanges();
    this.subscribeToLanguageChanges();
  }

  // Update state helper
  private updateState(updates: Partial<LangDropdownState>): void {
    const currentState = this._state$.value;
    this._state$.next({ ...currentState, ...updates });
  }

  // Subscribe to header style changes
  private subscribeToHeaderStyleChanges(): void {
    const headerStyleSubscription = this.themeService.headerStyle$.subscribe(style => {
      this.updateState({ currentHeaderStyle: style });
      this.updateLanguagesBasedOnStyle(style);
    });
    this.subscriptions.push(headerStyleSubscription);
  }

  // Subscribe to theme changes
  private subscribeToThemeChanges(): void {
    const themeSubscription = this.themeService.currentLayoutTheme$.subscribe((theme: LayoutTheme) => {
      this.updateState({ currentTheme: theme });
    });
    this.subscriptions.push(themeSubscription);
  }

  // Subscribe to language changes
  private subscribeToLanguageChanges(): void {
    if (!isPlatformBrowser(this.platformId)) return;

    // Since LanguageService doesn't have currentLanguage$ observable,
    // we'll get the current language from localStorage and TranslateService
    const currentLang = localStorage.getItem('language') || 'vi';
    const selectedFlag = this.currentState.allLanguages.find(l => l.code === currentLang)?.flag || this.currentState.allLanguages[0].flag;
    this.updateState({ selectedFlag });

    // Subscribe to translate service language changes
    const languageSubscription = this.translate.onLangChange.subscribe((event: any) => {
      const selectedFlag = this.currentState.allLanguages.find(l => l.code === event.lang)?.flag || this.currentState.allLanguages[0].flag;
      this.updateState({ selectedFlag });
    });
    this.subscriptions.push(languageSubscription);
  }

  // Update languages based on header style
  private updateLanguagesBasedOnStyle(style: string): void {
    // Always use the full predefined languages list regardless of style
    // This ensures consistency across all themes and styles
    const languages = [...this.predefinedLanguages];

    this.updateState({ languages });
  }

  // Public methods for component interaction
  toggleDropdown(): void {
    console.log('Lang-dropdown: Toggle dropdown called, current state:', this.currentState.isOpen);
    this.updateState({ isOpen: !this.currentState.isOpen });
    console.log('Lang-dropdown: New state:', !this.currentState.isOpen);
  }

  closeDropdown(): void {
    this.updateState({ isOpen: false });
  }

  async selectLanguage(lang: string): Promise<void> {
    const selectedFlag = this.currentState.languages.find(l => l.code === lang)?.flag || this.currentState.languages[0].flag;
    this.updateState({ selectedFlag });
    
    console.log('Lang-dropdown: Language selected:', lang);

    // Use the translate service to immediately change the language in UI
    await firstValueFrom(this.translate.use(lang));

    // Use the language service to change language properly (handles localStorage and API)
    await this.languageService.changeLanguage(lang);
    
    this.closeDropdown();
  }

  // Handle dropdown position updates
  updateDropdownPosition(): void {
    // This method can be called from components to update dropdown position
    // Implementation depends on specific positioning requirements
  }

  // Handle window scroll
  onWindowScroll(): void {
    if (this.currentState.isOpen) {
      // Use requestAnimationFrame for smoother performance
      requestAnimationFrame(() => {
        this.updateDropdownPosition();
      });
    }
  }

  // Handle click outside
  onClickOutside(event: Event, dropdownElement?: HTMLElement): void {
    if (!this.currentState.isOpen) {
      return;
    }

    const target = event.target as HTMLElement;
    console.log('Lang-dropdown: Click outside detected, target:', target);

    // If dropdownElement is provided, check if click is outside
    if (dropdownElement) {
      const isOutside = !dropdownElement.contains(target);
      console.log('Lang-dropdown: Is click outside?', isOutside);
      if (isOutside) {
        console.log('Lang-dropdown: Closing dropdown due to outside click');
        this.closeDropdown();
      }
    } else {
      // Fallback: close dropdown (this maintains backward compatibility)
      console.log('Lang-dropdown: No dropdown element provided, closing dropdown');
      this.closeDropdown();
    }
  }

  // Cleanup
  destroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.subscriptions = [];
  }
}
