import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ToggleSwitchComponent } from '../../common/toggle-switch/toggle-switch.component';
import { LoadingComponent } from '../../common/loading/loading.component';
import { ToastService } from '../../../core/services/toast.service';
import { TenantInfo, TenantService } from '../../../core/services/tenant.service';
import { ModalService } from '../../../core/services/modal.service';
import { PanelService } from '../../../core/services/panel.service';
import { NewPanelStep1Component } from '../../popup/new-panel-step1/new-panel-step1.component';
import { NewPanelStep2Component } from '../../popup/new-panel-step2/new-panel-step2.component';
import { NewPanelStep3Component } from '../../popup/new-panel-step3/new-panel-step3.component';



@Component({
  selector: 'app-panels-setting',
  standalone: true,
  imports: [
    CommonModule,
    FontAwesomeModule,
    TranslateModule,
    ToggleSwitchComponent,
    LoadingComponent
  ],
  templateUrl: './panels-setting.component.html',
  styleUrls: ['./panels-setting.component.css']
})
export class PanelsSettingComponent implements OnInit {
  panels: TenantInfo[] = [];
  loading = false;

  // Panel creation properties
  currentStep = 0; // 0 = no modal, 1-3 = steps
  domainName = '';
  isCreatingPanel = false;

  constructor(
    private tenantService: TenantService,
    private toastService: ToastService,
    private modalService: ModalService,
    private panelService: PanelService,
    private translateService: TranslateService
  ) {}

  ngOnInit(): void {
    this.loadPanels();
  }

  loadPanels(): void {
    this.loading = true;
    this.tenantService.getAccessibleTenants().subscribe({
      next: (data) => {
        this.panels = data;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading panels:', error);
        this.toastService.showError(error?.message || this.translateService.instant('panels.failed_to_load'));



        this.loading = false;
      }
    });
  }

  onToggleChange(newValue: boolean, panel: TenantInfo): void {
    console.log('Toggle changed for panel:', panel, 'new value:', newValue);

    this.tenantService.toggleAutoRenewal(panel.id, newValue).subscribe({
      next: (response) => {
        console.log('Auto-renewal toggle success:', response);
        // Update UI after successful API call
        panel.auto_renewal = newValue;
        this.toastService.showSuccess(
          newValue ? this.translateService.instant('panels.auto_renewal_enabled') : this.translateService.instant('panels.auto_renewal_disabled')
        );
      },
      error: (error) => {
        console.error('Error toggling auto-renewal:', error);
        // Revert the toggle state on error
        panel.auto_renewal = !newValue;
        this.toastService.showError(error?.message || this.translateService.instant('panels.failed_to_update_auto_renewal'));
      }
    });
  }

  onRestore(panel: TenantInfo): void {
    // Implement restore functionality
    console.log('Restore panel:', panel);
    this.toastService.showSuccess(this.translateService.instant('panels.restore_not_implemented'));
  }

  onActions(panel: TenantInfo): void {
    // Implement actions menu
    console.log('Actions for panel:', panel);
    this.toastService.showSuccess(this.translateService.instant('panels.actions_not_implemented'));
  }

  getDaysLeftText(panel: TenantInfo): string {
    if (panel.status === 'Expired') {
      return this.translateService.instant('panels.expired');
    }

    if (panel.days_until_expiration === undefined || panel.days_until_expiration === null) {
      return 'N/A';
    }

    if (panel.days_until_expiration < 0) {
      return this.translateService.instant('panels.expired');
    }

    if (panel.days_until_expiration === 0) {
      return this.translateService.instant('panels.expires_today');
    }

    if (panel.days_until_expiration === 1) {
      return `1 ${this.translateService.instant('panels.day_left')}`;
    }

    return `${panel.days_until_expiration} ${this.translateService.instant('panels.days_left_text')}`;
  }

  getStatusClass(status?: string): string {
    if (!status) return 'bg-gray-100 text-gray-800';

    switch (status) {
      case 'Active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Failed':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'Expired':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'New':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Nginx80':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Configured':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  }

  getStatusLabel(status?: string): string {
    if (!status) return this.translateService.instant('panels.unknown');

    switch (status) {
      case 'Ready':
        return this.translateService.instant('panels.active');
      case 'Failed':
        return this.translateService.instant('panels.error');
      case 'Expired':
        return this.translateService.instant('panels.expired');
      case 'New':
        return this.translateService.instant('panels.new');
      case 'Nginx80':
      case 'Configured':
        return this.translateService.instant('panels.configuring');
      case 'Pending':
        return this.translateService.instant('panels.pending');
      default:
        return status;
    }
  }

  private formatDate(dateString: string): string {
    if (!dateString) return 'N/A';

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return this.translateService.instant('panels.invalid_date');
    }
  }

  createPanel(): void {
    // Reset domain name
    this.domainName = '';

    // Open the first step modal
    const modalRef = this.modalService.open(NewPanelStep1Component, {
      domain: this.domainName
    });

    // Subscribe to events
    modalRef.instance.close.subscribe(() => {
      this.modalService.close();
    });

    modalRef.instance.next.subscribe((domain: string) => {
      this.domainName = domain;
      this.openStep2Modal();
    });
  }

  openStep2Modal(): void {
    // Close previous modal
    this.modalService.close();

    // Open step 2 modal
    const modalRef = this.modalService.open(NewPanelStep2Component, {
      domain: this.domainName
    });

    // Subscribe to events
    modalRef.instance.close.subscribe(() => {
      this.modalService.close();
    });

    modalRef.instance.back.subscribe(() => {
      this.modalService.close();
      this.createPanel(); // Go back to step 1
    });

    modalRef.instance.next.subscribe(() => {
      this.openStep3Modal();
    });
  }

  openStep3Modal(): void {
    // Close previous modal
    this.modalService.close();

    // Open step 3 modal
    const modalRef = this.modalService.open(NewPanelStep3Component, {
      domain: this.domainName
    });

    // Subscribe to events
    modalRef.instance.close.subscribe(() => {
      this.modalService.close();
    });

    modalRef.instance.back.subscribe(() => {
      this.modalService.close();
      this.openStep2Modal(); // Go back to step 2
    });

    modalRef.instance.createPanel.subscribe((panelInfo: any) => {
      this.createPanelSubmit();
    });
  }

  createPanelSubmit(): void {
    this.isCreatingPanel = true;

    // Call the API to create the panel
    this.panelService.createPanel(this.domainName).subscribe({
      next: (response) => {
        this.toastService.showSuccess(`${this.translateService.instant('panels.panel_created_success')} ${this.domainName}`);
        this.modalService.close();
        this.isCreatingPanel = false;

        // Refresh the panels list
        this.loadPanels();
      },
      error: (error) => {
        console.error('Error creating panel:', error);

        // Use error message with fallback to default message
        const errorMessage = error?.message || this.translateService.instant('panels.failed_to_create');

        this.toastService.showError(errorMessage);
        this.isCreatingPanel = false;
      }
    });
  }
}
