import { Injectable, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { BehaviorSubject, Subscription } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';

// Services
import { SidebarService } from '../../../core/services/sidebar.service';
import { UserService } from '../../../core/services/user.service';
import { CurrencyService } from '../../../core/services/currency.service';
import { ThemeService, LayoutTheme } from '../../../core/services/theme.service';
import { AuthService } from '../../../core/services/auth.service';

// Models
import { UserRes } from '../../../model/response/user-res.model';

// Components
import { LangDropdownComponent } from '../../common/lang-dropdown/lang-dropdown.component';

export interface HeaderState {
  // User data
  user: UserRes | undefined;
  formattedBalance: string;
  
  // UI state
  isOpenMenuUser: boolean;
  currentHeaderStyle: string;
  
  // Theme management
  currentTheme: LayoutTheme;
}

@Injectable({
  providedIn: 'root'
})
export class HeaderLogicService {
  private subscriptions: Subscription[] = [];
  
  @ViewChild(LangDropdownComponent) langDropdown!: LangDropdownComponent;

  // State management
  private _state$ = new BehaviorSubject<HeaderState>({
    user: undefined,
    formattedBalance: '',
    isOpenMenuUser: false,
    currentHeaderStyle: 'standard',
    currentTheme: LayoutTheme.DEFAULT
  });

  // Public state observable
  public readonly state$ = this._state$.asObservable();

  // Current state getter
  private get currentState(): HeaderState {
    return this._state$.value;
  }

  constructor(
    private router: Router,
    private translate: TranslateService,
    private sidebarService: SidebarService,
    private userService: UserService,
    private currencyService: CurrencyService,
    private themeService: ThemeService,
    private authService: AuthService
  ) {
    this.initialize();
  }

  // Public getters for template access
  get user(): UserRes | undefined {
    return this.currentState.user;
  }

  get formattedBalance(): string {
    return this.currentState.formattedBalance;
  }

  get isOpenMenuUser(): boolean {
    return this.currentState.isOpenMenuUser;
  }

  get currentHeaderStyle(): string {
    return this.currentState.currentHeaderStyle;
  }

  get currentTheme(): LayoutTheme {
    return this.currentState.currentTheme;
  }

  // Initialize service
  private initialize(): void {
    this.loadUserData();
    this.subscribeToHeaderStyleChanges();
    this.subscribeToThemeChanges();
    this.subscribeToAuthChanges();
    
    // Trigger getting user information if needed
    this.userService.get$.next();
  }

  // Update state helper
  private updateState(updates: Partial<HeaderState>): void {
    const currentState = this._state$.value;
    this._state$.next({ ...currentState, ...updates });
  }

  // Data loading methods
  private loadUserData(): void {
    this.subscriptions.push(
      this.userService.user$.subscribe(user => {
        const updates: Partial<HeaderState> = { user };
        
        if (user) {
          // Format the balance using the currency service
          updates.formattedBalance = this.currencyService.formatPrice(user.balance);
        }
        
        this.updateState(updates);
      })
    );
  }

  private subscribeToHeaderStyleChanges(): void {
    this.subscriptions.push(
      this.themeService.headerStyle$.subscribe(style => {
        this.updateState({ currentHeaderStyle: style });
      })
    );
  }

  private subscribeToThemeChanges(): void {
    this.subscriptions.push(
      this.themeService.currentLayoutTheme$.subscribe((theme: LayoutTheme) => {
        this.updateState({ currentTheme: theme });
      })
    );
  }

  private subscribeToAuthChanges(): void {
    this.subscriptions.push(
      this.authService.auth$.subscribe(user => {
        if (user && this.langDropdown) {
          // User logged in, clear cache and reset language dropdown
          console.log('HeaderLogicService: User logged in, clearing cache and resetting lang dropdown');
          setTimeout(async () => {
            try {
              await this.langDropdown.clearCacheAndReset();
              console.log('HeaderLogicService: Lang dropdown cache cleared and reset successfully');
            } catch (error) {
              console.error('HeaderLogicService: Error clearing lang dropdown cache:', error);
              // Fallback to simple refresh
              this.langDropdown.refreshSelection();
            }
          }, 200); // Slightly longer delay to ensure everything is ready
        }
      })
    );
  }

  // Public methods for component interaction
  toggleSidebar(): void {
    this.sidebarService.toggleSidebar();
  }

  navigateTo(link: string): void {
    this.router.navigate([link]);
  }

  toggleUserMenu(): void {
    this.updateState({ isOpenMenuUser: !this.currentState.isOpenMenuUser });
  }

  closeUserMenu(): void {
    this.updateState({ isOpenMenuUser: false });
  }

  // Cleanup method
  destroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.subscriptions = [];
  }

  // Method to set lang dropdown reference (called from component after view init)
  setLangDropdownRef(langDropdown: LangDropdownComponent): void {
    this.langDropdown = langDropdown;
  }
}
