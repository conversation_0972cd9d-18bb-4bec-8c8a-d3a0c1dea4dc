import { Component, ElementRef, Renderer2 } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconsModule } from '../../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { SocialIconComponent } from '../../social-icon/social-icon.component';
import { BaseIconDropdownComponent } from '.././base-icon-dropdown.component';
import { IconDropdownLogicService } from '.././icon-dropdown-logic.service';

@Component({
  selector: 'app-simple-icon-dropdown',
  standalone: true,
  imports: [CommonModule, IconsModule, SocialIconComponent, TranslateModule],
  providers: [IconDropdownLogicService],
  templateUrl: './simple-icon-dropdown.component.html',
  styleUrls: ['./simple-icon-dropdown.component.css']
})
export class SimpleIconDropdownComponent extends BaseIconDropdownComponent {

  constructor(
    iconDropdownLogicService: IconDropdownLogicService,
    elementRef: ElementRef,
    renderer: Renderer2
  ) {
    super(iconDropdownLogicService, elementRef, renderer);
  }
}
