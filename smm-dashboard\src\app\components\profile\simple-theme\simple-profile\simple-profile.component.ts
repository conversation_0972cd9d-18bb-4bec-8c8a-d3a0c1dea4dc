import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

// Base component
import { BaseProfileComponent } from '../../base-profile.component';

// Services
import { ProfileLogicService } from '../../services/profile-logic.service';

// Components
import { IconsModule } from '../../../../icons/icons.module';
import { LiteDropdownComponent } from '../../../common/lite-dropdown/lite-dropdown.component';
import { MfaSettingComponent } from '../../../popup/mfa-setting/mfa-setting.component';
import { MfaDisabledComponent } from '../../../popup/mfa-disabled/mfa-disabled.component';

@Component({
  selector: 'app-simple-profile',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    IconsModule,
    LiteDropdownComponent,
    MfaSettingComponent,
    MfaDisabledComponent
  ],
  templateUrl: './simple-profile.component.html',
  styleUrl: './simple-profile.component.css'
})
export class SimpleProfileComponent extends BaseProfileComponent implements OnInit, OnDestroy {

  // Predefined languages list (same as admin)
  allLanguages = [
    { flag: 'fi fi-vn', code: 'vi', name: 'Tiếng Việt' },
    { flag: 'fi fi-us', code: 'en', name: 'English' },
    { flag: 'fi fi-cn', code: 'cn', name: '中文' },
    { flag: 'fi fi-fr', code: 'fr', name: 'Français' },
    { flag: 'fi fi-es', code: 'es', name: 'Español' },
    { flag: 'fi fi-de', code: 'de', name: 'Deutsch' },
    { flag: 'fi fi-jp', code: 'ja', name: '日本語' },
    { flag: 'fi fi-kr', code: 'ko', name: '한국어' },
    { flag: 'fi fi-ru', code: 'ru', name: 'Русский' },
    { flag: 'fi fi-pt', code: 'pt', name: 'Português' },
    { flag: 'fi fi-it', code: 'it', name: 'Italiano' },
    { flag: 'fi fi-nl', code: 'nl', name: 'Nederlands' },
    { flag: 'fi fi-pl', code: 'pl', name: 'Polski' },
    { flag: 'fi fi-tr', code: 'tr', name: 'Türkçe' },
    { flag: 'fi fi-ar', code: 'ar', name: 'العربية' },
    { flag: 'fi fi-th', code: 'th', name: 'ไทย' },
    { flag: 'fi fi-id', code: 'id', name: 'Bahasa Indonesia' },
    { flag: 'fi fi-my', code: 'ms', name: 'Bahasa Malaysia' },
    { flag: 'fi fi-ph', code: 'tl', name: 'Filipino' },
    { flag: 'fi fi-in', code: 'hi', name: 'हिन्दी' }
  ];

  // Get language names for dropdown
  get languageOptions(): string[] {
    return this.allLanguages.map(lang => lang.name);
  }

  constructor(profileLogicService: ProfileLogicService) {
    super(profileLogicService);
  }

  override ngOnInit(): void {
    super.ngOnInit();
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
  }

  // Implementation of abstract methods
  onProfileSubmit(): void {
    // Implementation for profile form submission
    console.log('Profile form submitted');
  }

  onPasswordSubmit(): void {
    // Implementation for password form submission
    console.log('Password form submitted');
  }

  onCurrencyChange(currency: string): void {
    // Implementation for currency change
    console.log('Currency changed:', currency);
  }

  onLanguageChange(language: string): void {
    // Implementation for language change
    console.log('Language changed:', language);
  }

  loadLoginHistory(): void {
    // Implementation for loading login history
    console.log('Loading login history');
  }

  changePage(page: number): void {
    // Implementation for pagination
    console.log('Page changed:', page);
  }

  toggle2FA(): void {
    // Implementation for 2FA toggle
    console.log('2FA toggled');
  }

  verifyMFA(): void {
    // Implementation for MFA verification
    console.log('MFA verified');
  }

  disableMFA(): void {
    // Implementation for MFA disable
    console.log('MFA disabled');
  }
}
