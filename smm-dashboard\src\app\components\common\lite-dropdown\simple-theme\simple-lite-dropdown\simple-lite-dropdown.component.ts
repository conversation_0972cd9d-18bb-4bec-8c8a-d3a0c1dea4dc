import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LiteDropdownBaseComponent } from '../../lite-dropdown-base.component';
import { LiteDropdownLogicService } from '../../lite-dropdown.service';
import { IconsModule } from '../../../../../icons/icons.module';

@Component({
  selector: 'app-simple-lite-dropdown',
  standalone: true,
  imports: [CommonModule, IconsModule],
  templateUrl: './simple-lite-dropdown.component.html',
  styleUrls: ['./simple-lite-dropdown.component.css'],
  providers: [LiteDropdownLogicService]
})
export class SimpleLiteDropdownComponent extends LiteDropdownBaseComponent {
}
