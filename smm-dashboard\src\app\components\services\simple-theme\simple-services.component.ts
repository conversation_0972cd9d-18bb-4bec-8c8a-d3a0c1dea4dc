import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconsModule } from '../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';

import { IconDropdownComponent } from "../../common/icon-dropdown/icon-dropdown.component";
import { HeartCheckboxComponent } from "../../common/heart-checkbox/heart-checkbox.component";
import { ServiceLabelComponent } from "../../common/service-label/service-label.component";
import { TimeFormatPipe } from '../../../core/pipes/time.pipe';
import { SocialIconComponent } from "../../common/social-icon/social-icon.component";
import { LoadingComponent } from '../../common/loading/loading.component';
import { ServicesBaseComponent } from '../services-base.component';
import { ServicesLogicService } from '../services.service';

@Component({
  selector: 'app-simple-services',
  standalone: true,
  imports: [
    IconDropdownComponent,
    CommonModule,
    IconsModule,
    HeartCheckboxComponent,
    ServiceLabelComponent,
    TimeFormatPipe,
    TranslateModule,
    SocialIconComponent,
    LoadingComponent
  ],
  templateUrl: './simple-services.component.html',
  styleUrl: './simple-services.component.css',
  providers: [ServicesLogicService]
})
export class SimpleServicesComponent extends ServicesBaseComponent {
}
