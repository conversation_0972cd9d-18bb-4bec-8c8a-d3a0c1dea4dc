import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { IconsModule } from '../../../icons/icons.module';
import { IconName, SizeProp } from '@fortawesome/fontawesome-svg-core';
import { brandColors } from '../../../icons/icons.font-awesome-brands';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-social-icon',
  standalone: true,
  imports: [IconsModule, CommonModule],
  templateUrl: './social-icon.component.html',
  styleUrl: './social-icon.component.css'
})
export class SocialIconComponent implements OnChanges {
  ngOnChanges(changes: SimpleChanges): void {
    this.iconName = this.icon as IconName;
    this.isImageIcon = this.isImageUrl(this.icon);
  }
  ngOnInit(): void {

  }
  brandColors = brandColors;
  @Input() icon: string = 'facebook';
  @Input() zClass: string = '';

  iconName: IconName = 'facebook';
  isImageIcon: boolean = false;

  // Check if a value is an image URL
  isImageUrl(value: string): boolean {
    if (!value) return false;

    const valueStr = value.toString();

    // Check if it's a URL (contains http/https or starts with /)
    const isUrl = valueStr.startsWith('http') || valueStr.startsWith('https') || valueStr.startsWith('/');

    // Check if it has image extension or is a CDN URL
    const hasImageExtension = /\.(png|jpg|jpeg|gif|svg|webp)$/i.test(valueStr);
    const isCdnUrl = valueStr.includes('/cdn/') || valueStr.includes('/api/image/');

    return isUrl && (hasImageExtension || isCdnUrl);
  }
}
