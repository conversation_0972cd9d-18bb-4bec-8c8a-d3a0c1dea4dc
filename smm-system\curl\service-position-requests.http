###
# group: service-position
# @name changeCategoryWithPosition
PUT {{smm-system}}/v1/services/1/categories/2/position?position=0
Authorization: Bearer {{access_token}}
x-client-id: {{client_id}}
Content-Type: application/json

###
# @name changeCategoryWithPositionMiddle
PUT {{smm-system}}/v1/services/1/categories/2/position?position=2
Authorization: Bearer {{access_token}}
x-client-id: {{client_id}}
Content-Type: application/json

###
# @name changeCategoryWithPositionEnd
PUT {{smm-system}}/v1/services/1/categories/2/position?position=10
Authorization: Bearer {{access_token}}
x-client-id: {{client_id}}
Content-Type: application/json
