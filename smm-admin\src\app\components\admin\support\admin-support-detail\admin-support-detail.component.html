<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
  <div class="bg-white rounded-lg shadow-lg w-full max-w-3xl max-h-[90vh] flex flex-col">
    <!-- Header -->
    <div class="p-4 border-b">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">{{ 'admin.support_detail.ticket_detail' | translate }}{{ ticketId }}</h2>
        <button (click)="onClose()" class="text-gray-500 hover:text-gray-700">
          <fa-icon [icon]="['fas', 'times']" class="text-lg"></fa-icon>
        </button>
      </div>

      <!-- Status select moved to header for more prominence -->
      <div *ngIf="!isLoading && ticket && selectedStatus" class="flex items-center gap-3">
        <div class="flex-1">
          <app-ticket-status-select
            [status]="selectedStatus"
            [loading]="isLoading"
            (statusChange)="selectedStatus = $event"
            (updateStatus)="updateStatus()">
          </app-ticket-status-select>
        </div>
      </div>
    </div>

    <!-- Loading indicator -->
    <div *ngIf="isLoading" class="flex-1">
      <app-loading [size]="'lg'" [overlay]="true"></app-loading>
    </div>

    <!-- Ticket content -->
    <div *ngIf="!isLoading && ticket" class="flex-1 overflow-y-auto">
      <!-- Ticket information -->
      <div class="p-6 border-b">
        <h3 class="text-lg font-semibold mb-4">{{ 'admin.support_detail.information' | translate }}</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p class="text-gray-600 mb-1">{{ 'admin.support_detail.subject' | translate }}</p>
            <p class="font-medium">{{ ticket.subject }}</p>
          </div>

          <div>
            <p class="text-gray-600 mb-1">{{ 'admin.support_detail.customer' | translate }}</p>
            <p class="font-medium">Customer#{{ ticket.id }} (ID: {{ ticket.id }})</p>
          </div>

          <div>
            <p class="text-gray-600 mb-1">{{ 'admin.support_detail.created_date' | translate }}</p>
            <p class="font-medium">{{ getFormattedDate(ticket.created_at) }}</p>
          </div>

          <div>
            <p class="text-gray-600 mb-1">{{ 'admin.support_detail.last_updated' | translate }}</p>
            <p class="font-medium">{{ getFormattedDate(ticket.updated_at) }}</p>
          </div>
        </div>

        <div class="mt-2">
          <p class="text-gray-600 mb-1">{{ 'admin.support_detail.status' | translate }}</p>
          <app-ticket-status [status]="ticket.status"></app-ticket-status>
        </div>
      </div>

      <!-- Chat section -->
      <div class="p-6">
        <h3 class="text-lg font-semibold mb-4">{{ 'admin.support_detail.chat' | translate }}</h3>

        <div class="bg-gray-50 rounded-lg p-4 mb-4 h-64 overflow-y-auto">

          <!-- Replies -->
          <div *ngFor="let reply of ticket.replies" class="mb-4" [ngClass]="{'text-right': reply.replied_by !== firstRepliedBy}">
            <div [ngClass]="{'bg-[#5bc0de] text-white': reply.replied_by !== firstRepliedBy, 'bg-gray-200': reply.replied_by === firstRepliedBy}"
                 class="rounded-lg p-3 inline-block max-w-[80%] text-left">
              <p>{{ reply.content }}</p>
            </div>
           <p class="text-xs text-gray-500 mt-1"><span class="font-medium text-blue-400">{{ reply.replied_by }}</span> {{ getFormattedDate(reply.created_at) }}</p>
          </div>
        </div>

        <!-- Reply input -->
        <div class="flex gap-2">
          <input
            type="text"
            [(ngModel)]="replyMessage"
            placeholder="{{ 'admin.support_detail.enter_message' | translate }}"
            class="flex-1 p-2 border border-gray-300 rounded-lg h-[46px]"
            (keyup.enter)="sendReply()">
          <button
            (click)="sendReply()"
            [disabled]="!replyMessage.trim() || isLoading"
            [ngClass]="{'opacity-50 cursor-not-allowed': !replyMessage.trim() || isLoading}"
            class="bg-[var(--primary)] text-white px-4 h-[46px] rounded-lg hover:opacity-90 flex items-center justify-center min-w-[80px]">
            <span *ngIf="!isLoading">{{ 'admin.support_detail.send' | translate }}</span>
            <app-loading *ngIf="isLoading" [size]="'sm'"></app-loading>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
