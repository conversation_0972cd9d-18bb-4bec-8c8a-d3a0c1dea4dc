package tndung.vnfb.smm.controller;

import dev.samstevens.totp.exceptions.QrGenerationException;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import tndung.vnfb.smm.anotation.TenantCheck;
import tndung.vnfb.smm.dto.ApiResponseEntity;
import tndung.vnfb.smm.dto.TokenPairDto;
import tndung.vnfb.smm.dto.request.CreateTokenForUserReq;
import tndung.vnfb.smm.dto.request.LoginReq;
import tndung.vnfb.smm.dto.request.MFADisabledReq;
import tndung.vnfb.smm.dto.request.MFALoginReq;
import tndung.vnfb.smm.dto.request.MFAReq;
import tndung.vnfb.smm.dto.response.GUserRes;
import tndung.vnfb.smm.dto.response.MFARes;
import tndung.vnfb.smm.dto.response.TenantInfoDto;
import tndung.vnfb.smm.service.AccessService;

import javax.validation.Valid;
import java.util.List;



@RestController
@RequestMapping("/v1/access")
@RequiredArgsConstructor
public class AccessController {

    private final AccessService accessService;

    @PostMapping("/login")
    public ApiResponseEntity<GUserRes> login(@RequestBody @Valid LoginReq loginReq) {
        return ApiResponseEntity.success(accessService.login(loginReq));
    }


    @PostMapping("/mfa")
    public ApiResponseEntity<GUserRes> mfa(@RequestBody @Valid MFALoginReq loginReq) {
        return ApiResponseEntity.success(accessService.mfa(loginReq));
    }

    @GetMapping("/mfa/generate")
    @PreAuthorize("hasAnyRole('ROLE_USER' ,'ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<MFARes> generateMFA() throws QrGenerationException {
        return ApiResponseEntity.success(accessService.generateMFA());
    }

    @PutMapping("/mfa/verify")
    @PreAuthorize("hasAnyRole('ROLE_USER' ,'ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<String> verifyMFA(@RequestBody @Valid MFAReq req) {
        accessService.verifyMFA(req);
        return ApiResponseEntity.success();
    }

    @PutMapping("/mfa/disabled")
    @PreAuthorize("hasAnyRole('ROLE_USER' ,'ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<String> disabledMFA(@RequestBody @Valid MFADisabledReq req) {
        accessService.disableMFA(req);
        return ApiResponseEntity.success();
    }

    @PostMapping("/logout")
    @PreAuthorize("hasAnyRole('ROLE_USER' ,'ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<String> logout() {
        accessService.logout();
        return ApiResponseEntity.success();
    }

    @PostMapping("/refresh")
    public ApiResponseEntity<GUserRes> refresh() {
        return ApiResponseEntity.success(accessService.refresh());
    }

    @GetMapping("/tenants/accessible")
    @PreAuthorize("hasAnyRole('ROLE_PANEL',  'ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<List<TenantInfoDto>> getAccessibleTenants() {
        return ApiResponseEntity.success(accessService.getAccessibleTenants());
    }

    @PostMapping("/switch/{tenantId}")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<TokenPairDto> switchTenant(@PathVariable String tenantId) {

            // Use the service to handle tenant switching
            TokenPairDto tokenPair = accessService.switchTenant(tenantId);

            // Return the full token pair
            return ApiResponseEntity.success(tokenPair);

    }

    @PostMapping("/create-token")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<TokenPairDto> createTokenForUser(@RequestBody @Valid CreateTokenForUserReq request) {

        // Create token for the specified user
        TokenPairDto tokenPair = accessService.createTokenForUser(request.getUserId());

        return ApiResponseEntity.success(tokenPair);
    }
}
