import { Component, OnInit, AfterViewInit } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';

// Base component
import { BaseMfaComponent } from '../../base-mfa.component';

// Services
import { MfaLogicService } from '../../services/mfa-logic.service';

@Component({
  selector: 'app-simple-mfa',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterModule, TranslateModule],
  templateUrl: './simple-mfa.component.html',
  styleUrl: './simple-mfa.component.css'
})
export class SimpleMfaComponent extends BaseMfaComponent implements OnInit, AfterViewInit {
  constructor(mfaLogicService: MfaLogicService) {
    super(mfaLogicService);
  }
}
