<!-- Add service and Import services buttons -->
<div class="flex justify-between items-center gap-3 mb-4">
  <div class="flex gap-3">
    <button (click)="onAddService()"
      class="bg-[var(--primary)] text-white font-medium text-sm px-6 py-3 rounded-lg cursor-pointer transition-colors duration-300 hover:bg-blue-600 active:bg-blue-700">
      {{ 'admin.services.add_service' | translate }}
    </button>
    <button (click)="onImportServices()"
      class="bg-transparent border border-[var(--primary)] text-[var(--primary)] font-medium text-sm px-6 py-3 rounded-lg cursor-pointer transition-colors duration-300 hover:bg-[#0095f6]/10 active:bg-[#0095f6]/20">
      {{ 'admin.services.import_services' | translate }}
    </button>
  </div>

  <!-- Category management buttons -->
  <div class="flex gap-2">
    <!-- Platform Management Button -->
    <button (click)="onPlatformManagement()"
      class="bg-transparent border border-purple-500 text-purple-500 hover:bg-purple-50 font-medium text-sm px-4 py-2 rounded-lg cursor-pointer transition-colors duration-300">
      <fa-icon [icon]="['fas', 'cog']" class="mr-2"></fa-icon>
      Platform Management
    </button>

    <!-- Move Categories Button (hidden on mobile) -->
    <button (click)="onToggleCategoryMoveMode()"
      [class]="isCategoryMoveMode ?
        'bg-orange-500 text-white border-orange-500 hover:bg-orange-600' :
        'bg-transparent border border-orange-500 text-orange-500 hover:bg-orange-50'"
      class="hidden md:flex font-medium text-sm px-4 py-2 rounded-lg cursor-pointer transition-colors duration-300">
      <fa-icon [icon]="['fas', 'arrows-alt']" class="mr-2"></fa-icon>
      {{ isCategoryMoveMode ? 'Exit Move' : 'Move Categories' }}
    </button>
  </div>
</div>

<!-- Search and Filter Section -->
<div class="flex flex-col md:flex-row gap-4 mb-4 bg-white rounded-2xl">
  <div class="flex-1 md:flex-1">
    <div class="text-gray-900 font-medium text-sm mb-1">{{ 'search' | translate }}</div>
    <input type="text" [(ngModel)]="searchValue" (input)="onSearchInput()"
      placeholder="Search by name or ID..."
      class="border-none w-full bg-[#f5f7fc] rounded-lg" style="height: 52px;">
  </div>

  <!-- Category Section -->
  <div class="flex-1 md:flex-1">
    <div class="text-gray-900 font-medium text-sm mb-1">{{ 'category' | translate }}</div>
    <div class="relative">
      <app-icon-dropdown [options]="categoryOptions" [selectedOption]="selectedCategory"
        (selected)="onCategorySelected($event)"
        [customClassDropdown]="'bg-[#f5f7fc] rounded-lg '"></app-icon-dropdown>
      <button (click)="onNewCategory()"
        class="absolute right-0 top-0 bg-[var(--primary)] text-white font-medium text-sm px-3 rounded-r-lg cursor-pointer transition-colors duration-300 hover:bg-[var(--primary-hover)] active:bg-[var(--primary-active)]"
        style="height: 52px;">
        <fa-icon [icon]="['fas', 'plus']" class="mr-1"></fa-icon>
        {{ 'Add' | translate }}
      </button>
    </div>
  </div>

  <!-- Reset Button Section -->
  <!-- <div class="flex gap-2 items-end">
    <button (click)="onResetFilter()"
      class="bg-gray-100 text-gray-700 font-medium text-sm px-4 rounded-lg cursor-pointer transition-colors duration-300 hover:bg-gray-200 active:bg-gray-300"
      style="height: 52px;">
      <fa-icon [icon]="['fas', 'undo']" class="mr-1"></fa-icon>
      {{ 'Reset' | translate }}
    </button>
  </div> -->
</div>
