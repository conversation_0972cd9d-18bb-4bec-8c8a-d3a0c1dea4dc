import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';
import { CurrencySettingsComponent } from '../currency-settings/currency-settings.component';

@Component({
  selector: 'app-currency-settings-page',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IconsModule,
    CurrencySettingsComponent
  ],
  templateUrl: './currency-settings-page.component.html',
  styleUrl: './currency-settings-page.component.css'
})
export class CurrencySettingsPageComponent implements OnInit {

  constructor(private router: Router) {}

  ngOnInit(): void {
    // Component initialization
  }

  /**
   * Navigate back to general settings
   */
  goBack(): void {
    this.router.navigate(['/panel/settings/general']);
  }
}
