-- Update the tenant table to match our new schema
-- First, check if the tenant table already exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'tenant') THEN
        -- Table exists, add new columns if they don't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tenant' AND column_name = 'name') THEN
            ALTER TABLE tenant ADD COLUMN name VARCHAR(100);
        END IF;

        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tenant' AND column_name = 'subdomain') THEN
            ALTER TABLE tenant ADD COLUMN subdomain VARCHAR(100);
        END IF;

        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tenant' AND column_name = 'primary_color') THEN
            ALTER TABLE tenant ADD COLUMN primary_color VARCHAR(20);
        END IF;

        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tenant' AND column_name = 'secondary_color') THEN
            ALTER TABLE tenant ADD COLUMN secondary_color VARCHAR(20);
        END IF;

        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tenant' AND column_name = 'active') THEN
            ALTER TABLE tenant ADD COLUMN active BOOLEAN NOT NULL DEFAULT TRUE;
        END IF;

        -- Update existing records
        UPDATE tenant SET
            name = domain,
            active = (status = 1 OR status IS NULL)
        WHERE name IS NULL;
    ELSE
        -- Create the tenant table if it doesn't exist
        CREATE TABLE tenant (
            id VARCHAR(36),
            tenant_id VARCHAR(255) NOT NULL,
            name VARCHAR(100),
            domain VARCHAR(255) NOT NULL,
            subdomain VARCHAR(100),
            logo_url VARCHAR(255),
            primary_color VARCHAR(20),
            secondary_color VARCHAR(20),
            active BOOLEAN NOT NULL DEFAULT TRUE,
            is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
            api_url VARCHAR(255),
            site_url VARCHAR(255),
            theme VARCHAR(100),
            company_name VARCHAR(255),
            contact_email VARCHAR(255),
            status INTEGER DEFAULT 1,
            user_id BIGINT,
            default_language VARCHAR(4) DEFAULT 'vi',
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            PRIMARY KEY (id)
        );

        -- Create index for faster lookups
        CREATE INDEX IF NOT EXISTS idx_tenant_domain ON tenant(domain);
        CREATE INDEX IF NOT EXISTS idx_tenant_tenant_id ON tenant(tenant_id);
        CREATE UNIQUE INDEX IF NOT EXISTS uk_tenant_domain ON tenant(domain) WHERE is_deleted = FALSE;

        -- Insert the default tenant
        INSERT INTO tenant (id, tenant_id, name, domain, active, created_at, updated_at)
        VALUES ('0e22c37d-bfb5-4276-bd30-355fcdb39c9e', '0e22c37d-bfb5-4276-bd30-355fcdb39c9e', 'autovnfb.dev', 'autovnfb.dev', TRUE, NOW(), NOW());

        -- Insert the subdomain tenant
        INSERT INTO tenant (id, tenant_id, name, domain, subdomain, active, created_at, updated_at)
        VALUES ('1f33d48e-c7b6-4387-ae42-466f9c1b2d7f', '1f33d48e-c7b6-4387-ae42-466f9c1b2d7f', 'autovnfb.com', 'autovnfb.com', 'autovnfb-com', TRUE, NOW(), NOW());
    END IF;
END $$;

-- Add comments to the table and columns for better documentation
COMMENT ON TABLE tenant IS 'Stores information about each tenant in the multi-tenant system';
COMMENT ON COLUMN tenant.id IS 'Primary key (UUID)';
COMMENT ON COLUMN tenant.tenant_id IS 'Tenant ID used for backward compatibility';
COMMENT ON COLUMN tenant.name IS 'Display name of the tenant';
COMMENT ON COLUMN tenant.domain IS 'Primary domain of the tenant';
COMMENT ON COLUMN tenant.subdomain IS 'Subdomain of the tenant (if applicable)';
COMMENT ON COLUMN tenant.logo_url IS 'URL to the tenant logo';
COMMENT ON COLUMN tenant.primary_color IS 'Primary brand color in hex format';
COMMENT ON COLUMN tenant.secondary_color IS 'Secondary brand color in hex format';
COMMENT ON COLUMN tenant.active IS 'Whether the tenant is active';
COMMENT ON COLUMN tenant.is_deleted IS 'Whether the tenant has been deleted';
COMMENT ON COLUMN tenant.created_at IS 'Timestamp when the tenant was created';
COMMENT ON COLUMN tenant.updated_at IS 'Timestamp when the tenant was last updated';
