<!-- No service message -->
<div *ngIf="!service || !service.id" class="simple-no-service-message">
  {{ 'no_services_available' | translate }}
</div>

<!-- Service content when available -->
<ng-container *ngIf="service && service.id">
  <div *ngIf="lite; else full" class="simple-header-container">
    <div class="simple-service-row">
      <div class="simple-icon-container">
        <app-social-icon *ngIf="service.icon" [icon]="service.icon || 'facebook'" class="simple-service-icon"></app-social-icon>
      </div>
      <div class="simple-service-name">
        <span *ngIf="service.name?.startsWith('filter.'); else normalName">{{ service.name | translate }}</span>
        <ng-template #normalName>
          <span>{{service.name}}</span>
        </ng-template>
      </div>
    </div>
  </div>

  <ng-template #full>
    <div class="simple-service-card">
      <!-- Service Header -->
      <div class="simple-service-header">
        <app-social-icon *ngIf="service.icon" [icon]="service.icon || 'facebook'" class="simple-service-icon"></app-social-icon>
        
        <div class="simple-service-id">{{service.id}}</div>
        
        <div class="simple-service-title">
          <span *ngIf="service.name?.startsWith('filter.'); else normalFullName">{{ service.name | translate }}</span>
          <ng-template #normalFullName>
            <span>{{service.name}}</span>
          </ng-template>
        </div>
      </div>

      <!-- Price Section -->
      <div class="simple-price-section">
        <!-- Show original price with strikethrough if there's a discount -->
        <span *ngIf="hasDiscount()" class="simple-original-price">
          {{service.price | currencyConvert}}
        </span>
        
        <!-- Show discount percentage if applicable -->
        <span *ngIf="getDiscountPercent() > 0" class="simple-discount-badge">
          -{{getDiscountPercent()}}%
        </span>
        
        <!-- Always show the final price -->
        <span class="simple-final-price">{{caculateDiscount() | currencyConvert}}</span>
      </div>

      <!-- Tags Section -->
      <div class="simple-tags-section" *ngIf="service.labels && service.labels.length > 0">
        <app-tag-label 
          *ngFor="let tag of service.labels" 
          [color]="tag.type === 'Green' ? '#22c55e' : '#ef4444'" 
          [text]="tag.text"
          class="simple-tag">
        </app-tag-label>
      </div>
    </div>
  </ng-template>
</ng-container>
