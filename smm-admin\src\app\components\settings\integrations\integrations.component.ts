import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

import { IntegrationConfig, IntegrationsService } from '../../../core/services/integrations.service';

import { ToastService } from '../../../core/services/toast.service';
import { IntegrationPosition } from '../../../model/response/integration-res.model';
import { IntegrationReq } from '../../../model/request/integration-req.model';
import { DisconnectConfirmationComponent } from './disconnect-confirmation/disconnect-confirmation.component';
import { IconsModule } from '../../../icons/icons.module';
import { IntegrationConfigComponent } from '../../popup/integration-config/integration-config.component';
import { LoadingComponent } from '../../common/loading/loading.component';
import { CustomIntegrationComponent } from '../../popup/custom-integration/custom-integration.component';

@Component({
  selector: 'app-integrations',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IconsModule,
    IntegrationConfigComponent,
    DisconnectConfirmationComponent,
    LoadingComponent,
    CustomIntegrationComponent
  ],
  templateUrl: './integrations.component.html',
  styleUrl: './integrations.component.css'
})
export class IntegrationsComponent implements OnInit {
  integrations: IntegrationConfig[] = [];
  loading: boolean = false;
  showConfigPopup: boolean = false;
  selectedIntegration: IntegrationConfig | null = null;

  // Delete confirmation
  showDeleteConfirmation: boolean = false;
  integrationToDisconnect: IntegrationConfig | null = null;
  isDisconnecting: boolean = false;

  // Custom integration popup
  showCustomIntegrationPopup: boolean = false;

  constructor(
    private integrationsService: IntegrationsService,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    this.loadIntegrations();
  }

  loadIntegrations(): void {
    this.loading = true;
    this.integrationsService.getIntegrations().subscribe({
      next: (integrations) => {
        this.integrations = integrations;
        this.loading = false;
      },
      error: (error: any) => {
        console.error('Error loading integrations:', error);
        this.toastService.showError(error?.message || 'Failed to load integrations');
        this.loading = false;
      }
    });
  }

  openConfigPopup(integration: IntegrationConfig): void {
    // Make a copy of the integration to avoid modifying the original
    this.selectedIntegration = { ...integration };

    // Always set enabled to true for the popup
    this.selectedIntegration.enabled = true;

    // Note: We don't set a fallback ID here anymore since we handle create vs connect in saveIntegrationConfig

    this.showConfigPopup = true;
  }

  closeConfigPopup(): void {
    this.showConfigPopup = false;
    this.selectedIntegration = null;
  }

  saveIntegrationConfig(config: IntegrationConfig): void {
    // Create the request object
    const req: IntegrationReq = {
      value: config.username || '',
      position: config.position?.toUpperCase() === 'RIGHT' ?
        IntegrationPosition.RIGHT : IntegrationPosition.LEFT
    };

    // Check if integration has an ID - if not, create it first
    if (!config.id) {
      // No ID means this integration doesn't exist yet, so create it
      // Add the key field for new integrations
      req.key = config.type.toLowerCase();

      // Add icon if it's a custom integration with icon
      if (config.token) {
        req.icon = config.token; // Using token field to pass icon data
      }

      this.createNewIntegration(req, config.type);
    } else {
      // ID exists, so connect to existing integration
      this.connectToIntegration(config.id, req);
    }
  }

  createNewIntegration(req: IntegrationReq, type: string): void {
    this.integrationsService.createIntegration(req).subscribe({
      next: (newIntegration) => {
        // Update the integration in the list
        const index = this.integrations.findIndex(i => i.type === type);
        if (index !== -1) {
          this.integrations[index] = newIntegration;
        } else {
          // Add new integration to the list if not found
          this.integrations.push(newIntegration);
        }
        this.toastService.showSuccess('Integration created and connected successfully');
        this.closeConfigPopup();
      },
      error: (error: any) => {
        console.error('Error creating integration:', error);
        this.toastService.showError(error?.message || 'Failed to create integration');
      }
    });
  }

  connectToIntegration(id: number, req: IntegrationReq): void {
    this.integrationsService.connectIntegration(id, req).subscribe({
      next: (updatedConfig) => {
        // Update the integration in the list
        const index = this.integrations.findIndex(i => i.id === id);
        if (index !== -1) {
          this.integrations[index] = updatedConfig;
        }
        this.toastService.showSuccess('Integration connected successfully');
        this.closeConfigPopup();
      },
      error: (error: any) => {
        console.error('Error connecting to integration:', error);
        this.toastService.showError(error?.message || 'Failed to connect integration');
      }
    });
  }

  disconnectFromIntegration(id: number): void {
    this.integrationsService.disconnectIntegration(id).subscribe({
      next: (success) => {
        if (success) {
          // Remove the integration from the list completely
          this.integrations = this.integrations.filter(integration => integration.id !== id);
          this.toastService.showSuccess('Integration disconnected successfully');
        } else {
          this.toastService.showError('Failed to disconnect integration');
        }
        this.closeConfigPopup();
        this.closeDeleteConfirmation();
      },
      error: (error: any) => {
        console.error('Error disconnecting from integration:', error);
        this.toastService.showError(error?.message || 'Failed to disconnect integration');
        this.isDisconnecting = false;
      }
    });
  }

  getIntegrationIcon(integration: IntegrationConfig): any {
    // Check if integration has a custom icon (stored in token field for backward compatibility)
    if (integration.token && this.isImageUrl(integration.token)) {
      return null; // Will be handled by template to show image
    }

    // Check if it's a FontAwesome icon
    if (integration.token && !this.isImageUrl(integration.token)) {
      return ['fab', integration.token];
    }

    // Default icons for known types
    switch (integration.type.toLowerCase()) {
      case 'telegram':
        return ['fab', 'telegram-plane'];
      case 'whatsapp':
        return ['fab', 'whatsapp'];
      default:
        return ['fas', 'plug'];
    }
  }

  // Check if a value is an image URL
  isImageUrl(value: string): boolean {
    if (!value) return false;

    const valueStr = value.toString();

    // Check if it's a URL (contains http/https or starts with /)
    const isUrl = valueStr.startsWith('http') || valueStr.startsWith('https') || valueStr.startsWith('/');

    // Check if it has image extension
    const hasImageExtension = /\.(png|jpg|jpeg|gif|svg|webp)(\?.*)?$/i.test(valueStr);

    // Check if it's a CDN URL (more comprehensive patterns)
    const isCdnUrl = valueStr.includes('/cdn/') ||
                     valueStr.includes('/api/image/') ||
                     valueStr.includes('/uploads/') ||
                     valueStr.includes('/images/') ||
                     valueStr.includes('/assets/') ||
                     !!valueStr.match(/\/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}\.(png|jpg|jpeg|gif|svg|webp)/i);

    return isUrl && (hasImageExtension || isCdnUrl);
  }

  getIntegrationName(type: string): string {
    return type.charAt(0).toUpperCase() + type.slice(1);
  }

  connectIntegration(type: string): void {
    const integration = this.integrations.find(i => i.type === type);

    if (integration && integration.enabled) {
      // If already connected, show confirmation dialog to disconnect
      this.integrationToDisconnect = integration;
      this.showDeleteConfirmation = true;
    } else {
      // If not connected, open the config popup
      if (integration) {
        this.openConfigPopup(integration);
      } else {
        // Create a new integration without ID (will be created via API)
        const newIntegration = {
          id: undefined, // No ID means it will be created
          type,
          enabled: false
        };
        this.openConfigPopup(newIntegration);
      }
    }
  }

  /**
   * Get integration ID from type (same logic as in IntegrationsService)
   */
  private getIntegrationIdFromType(type: string): number {
    switch (type.toLowerCase()) {
      case 'telegram':
        return 1;
      case 'whatsapp':
        return 2;
      default:
        return 0;
    }
  }

  // Close delete confirmation dialog
  closeDeleteConfirmation(): void {
    this.showDeleteConfirmation = false;
    this.integrationToDisconnect = null;
    this.isDisconnecting = false;
  }

  // Confirm disconnect integration
  confirmDisconnectIntegration(): void {
    if (!this.integrationToDisconnect || !this.integrationToDisconnect.id) {
      return;
    }

    this.isDisconnecting = true;
    this.disconnectFromIntegration(this.integrationToDisconnect.id);
  }

  // Open custom integration popup
  openCustomIntegrationPopup(): void {
    this.showCustomIntegrationPopup = true;
  }

  // Close custom integration popup
  closeCustomIntegrationPopup(): void {
    this.showCustomIntegrationPopup = false;
  }

  // Handle custom integration added
  onCustomIntegrationAdded(integration: IntegrationConfig): void {
    // Reload the integrations list to get the latest data from API
    this.loadIntegrations();

    this.closeCustomIntegrationPopup();
    this.toastService.showSuccess('Custom integration added successfully');
  }
}
