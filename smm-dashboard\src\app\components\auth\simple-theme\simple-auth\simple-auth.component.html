<!-- Simple Theme Auth Component -->
<div class="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8" *ngIf="authState$ | async as authState">
  <div class="max-w-md w-full space-y-8">
    <!-- Header -->
    <div class="text-center">
      <h2 class="text-3xl font-bold text-gray-900">{{ 'simple_theme.auth.login' | translate }}</h2>
      <p class="mt-2 text-sm text-gray-600">{{ 'simple_theme.auth.welcome_back' | translate }}</p>
    </div>

    <!-- Login Form -->
    <form [formGroup]="authState.loginForm" (ngSubmit)="onSubmit()" class="mt-8 space-y-6">
      <div class="space-y-4">
        <!-- Email Field -->
        <div>
          <label for="email" class="block text-sm font-medium text-gray-700">
            {{ 'simple_theme.auth.username' | translate }}
          </label>
          <input
            id="email"
            type="email"
            formControlName="email"
            placeholder="{{ 'simple_theme.auth.enter_username' | translate }}"
            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            [ngClass]="{'border-red-500': email.invalid && (email.dirty || email.touched)}"
          >
          <div *ngIf="email.invalid && (email.dirty || email.touched)" class="mt-1 text-sm text-red-600">
            <div *ngIf="email.errors?.['required']">{{ 'simple_theme.auth.username_required' | translate }}</div>
            <div *ngIf="email.errors?.['email']">{{ 'simple_theme.auth.username_invalid' | translate }}</div>
          </div>
        </div>

        <!-- Password Field -->
        <div>
          <label for="password" class="block text-sm font-medium text-gray-700">
            {{ 'simple_theme.auth.password' | translate }}
          </label>
          <input
            id="password"
            type="password"
            formControlName="password"
            placeholder="{{ 'simple_theme.auth.enter_password' | translate }}"
            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            [ngClass]="{'border-red-500': password.invalid && (password.dirty || password.touched)}"
          >
          <div *ngIf="password.invalid && (password.dirty || password.touched)" class="mt-1 text-sm text-red-600">
            <div *ngIf="password.errors?.['required']">{{ 'simple_theme.auth.password_required' | translate }}</div>
          </div>
        </div>
      </div>

      <!-- Remember Me & Forgot Password -->
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <input
            id="remember-me"
            type="checkbox"
            formControlName="rememberMe"
            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          >
          <label for="remember-me" class="ml-2 block text-sm text-gray-900">
            {{ 'simple_theme.auth.remember_me' | translate }}
          </label>
        </div>

        <div class="text-sm">
          <a href="#" class="font-medium text-blue-600 hover:text-blue-500">
            {{ 'simple_theme.auth.forgot_password' | translate }}
          </a>
        </div>
      </div>

      <!-- Error Message -->
      <div *ngIf="authState.loginError" class="text-red-600 text-sm text-center bg-red-50 p-3 rounded-md">
        {{ authState.loginError }}
      </div>

      <!-- Submit Button -->
      <div>
        <button
          type="submit"
          [disabled]="authState.loginForm.invalid || authState.isLoading"
          class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span *ngIf="!authState.isLoading">{{ 'simple_theme.auth.login' | translate }}</span>
          <span *ngIf="authState.isLoading" class="flex items-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ 'simple_theme.auth.logging_in' | translate }}
          </span>
        </button>
      </div>

      <!-- Register Link -->
      <div class="text-center">
        <p class="text-sm text-gray-600">
          {{ 'simple_theme.auth.no_account' | translate }}
          <a routerLink="/auth/register" class="font-medium text-blue-600 hover:text-blue-500">
            {{ 'simple_theme.auth.register_now' | translate }}
          </a>
        </p>
      </div>
    </form>

    <!-- Simple Stats -->
    <div class="mt-8 grid grid-cols-3 gap-4 text-center">
      <div class="bg-white p-4 rounded-lg shadow">
        <div class="text-2xl font-bold text-blue-600">1M+</div>
        <div class="text-xs text-gray-500">{{ 'simple_theme.auth.completed_orders' | translate }}</div>
      </div>
      <div class="bg-white p-4 rounded-lg shadow">
        <div class="text-2xl font-bold text-green-600">24/7</div>
        <div class="text-xs text-gray-500">{{ 'simple_theme.auth.support' | translate }}</div>
      </div>
      <div class="bg-white p-4 rounded-lg shadow">
        <div class="text-2xl font-bold text-purple-600">$0.01</div>
        <div class="text-xs text-gray-500">{{ 'simple_theme.auth.price_starts' | translate }}</div>
      </div>
    </div>
  </div>
</div>
