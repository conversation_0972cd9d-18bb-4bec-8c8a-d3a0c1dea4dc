package tndung.vnfb.smm.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import tndung.vnfb.smm.anotation.TenantCheck;
import tndung.vnfb.smm.dto.request.TenantCurrencyReq;
import tndung.vnfb.smm.dto.response.TenantCurrencyRes;
import tndung.vnfb.smm.entity.Currency;
import tndung.vnfb.smm.dto.ApiResponseEntity;
import tndung.vnfb.smm.service.TenantCurrencyService;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/v1/tenant-currencies")
@RequiredArgsConstructor
public class TenantCurrencyController {

    private final TenantCurrencyService tenantCurrencyService;

    @GetMapping()
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<TenantCurrencyRes> getTenantCurrencies() {
        return ApiResponseEntity.success(tenantCurrencyService.getTenantCurrencies());
    }

    @PutMapping()
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<TenantCurrencyRes> updateTenantCurrencies(@RequestBody @Valid TenantCurrencyReq req) {
        return ApiResponseEntity.success(tenantCurrencyService.updateTenantCurrencies(req));
    }

    @GetMapping("/available")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL',  'ROLE_USER')")
    public ApiResponseEntity<List<Currency>> getAvailableCurrencies() {
        return ApiResponseEntity.success(tenantCurrencyService.getAvailableCurrenciesForCurrentTenant());
    }

    @PostMapping("/sync")
    @PreAuthorize("hasAnyRole('ROLE_PANEL','ROLE_ADMIN_PANEL' )")
    @TenantCheck
    public ApiResponseEntity<String> triggerCurrencySync() {
        tenantCurrencyService.triggerCurrencySync();
        return ApiResponseEntity.success("Currency sync triggered successfully");
    }
}
