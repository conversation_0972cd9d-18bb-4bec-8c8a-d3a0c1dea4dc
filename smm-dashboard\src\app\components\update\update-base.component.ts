import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostListener, inject } from '@angular/core';
import { UpdateLogicService } from './update.service';
import { LiteDropdownComponent } from '../common/lite-dropdown/lite-dropdown.component';
import { SearchBoxComponent } from '../common/search-box/search-box.component';
import { UpdateLogItem } from '../../model/response/update-logs-res.model';

@Component({
  template: ''
})
export abstract class UpdateBaseComponent implements OnInit, OnDestroy {
  protected updateLogic = inject(UpdateLogicService);
  
  window: Window = window;

  // References to UI components
  @ViewChild('filterDropdown') filterDropdown!: LiteDropdownComponent;
  @ViewChild('searchBox') searchBox!: SearchBoxComponent;

  // Listen for window resize events
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.updateLogic.onResize();
  }

  ngOnInit(): void {
    // Base initialization is handled by the service
  }

  ngOnDestroy(): void {
    // Cleanup is handled by the service
  }

  // Expose service properties
  get searchTerm(): string { return this.updateLogic.searchTerm; }
  get currentPage(): number { return this.updateLogic.currentPage; }
  get itemsPerPage(): number { return this.updateLogic.itemsPerPage; }
  get totalItems(): number { return this.updateLogic.totalItems; }
  get loading(): boolean { return this.updateLogic.loading; }
  get screenWidth(): number { return this.updateLogic.screenWidth; }
  get selectedFilter(): string { return this.updateLogic.selectedFilter; }
  get updateLogs(): UpdateLogItem[] { return this.updateLogic.updateLogs; }
  get pages(): number[] { return this.updateLogic.pages; }
  get filterOptions() { return this.updateLogic.filterOptions; }
  get statusMap() { return this.updateLogic.statusMap; }

  // Expose service methods
  loadUpdateLogs(): void {
    this.updateLogic.loadUpdateLogs();
  }

  updatePagination(): void {
    this.updateLogic.updatePagination();
  }

  goToPage(page: number): void {
    this.updateLogic.goToPage(page);
  }

  nextPage(): void {
    this.updateLogic.nextPage();
  }

  prevPage(): void {
    this.updateLogic.prevPage();
  }

  onSearch(searchTerm: string): void {
    this.updateLogic.onSearch(searchTerm);
  }

  onFilterSelected(filter: string): void {
    this.updateLogic.onFilterSelected(filter);
  }

  loadUpdateLogsWithFilters(): void {
    this.updateLogic.loadUpdateLogsWithFilters();
  }

  getStatusClass(status: string): string {
    return this.updateLogic.getStatusClass(status);
  }

  formatDate(dateString: string): { date: string, time: string } {
    return this.updateLogic.formatDate(dateString);
  }

  formatPriceChange(from: number | null, to: number | null): string | null {
    return this.updateLogic.formatPriceChange(from, to);
  }

  shouldShowPriceChange(status: string): boolean {
    return this.updateLogic.shouldShowPriceChange(status);
  }

  getVisiblePageCount(): number {
    return this.updateLogic.getVisiblePageCount();
  }

  getEllipsisThreshold(): number {
    return this.updateLogic.getEllipsisThreshold();
  }
}
