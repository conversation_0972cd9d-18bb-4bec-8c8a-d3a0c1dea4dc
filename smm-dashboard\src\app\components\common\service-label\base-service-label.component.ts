import { Component, Input, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { ServiceLabelLogicService, ServiceLabelState } from './service-label-logic.service';
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';
import { UserRes } from '../../../model/response/user-res.model';

@Component({
  template: '', // Will be overridden by child components
})
export abstract class BaseServiceLabelComponent implements OnInit, OnDestroy {
  @Input() service: SuperGeneralSvRes = {} as SuperGeneralSvRes;
  @Input() lite: boolean = false;

  // State from service
  state$ = this.serviceLabelLogicService.state$;
  
  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(
    protected serviceLabelLogicService: ServiceLabelLogicService
  ) {}

  ngOnInit(): void {
    // Subscribe to state changes if needed
    const stateSubscription = this.state$.subscribe(state => {
      // Handle state changes if needed
    });
    this.subscriptions.push(stateSubscription);
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.subscriptions = [];
  }

  // Helper methods that can be used by child components
  get user(): UserRes | undefined {
    return this.serviceLabelLogicService.user;
  }

  get userCustomDiscount(): number {
    return this.serviceLabelLogicService.userCustomDiscount;
  }

  // Format price with currency conversion
  formatPrice(price: number): string {
    return this.serviceLabelLogicService.formatPrice(price);
  }

  // Check if service has special prices
  hasSpecialPrices(): boolean {
    return this.serviceLabelLogicService.hasSpecialPrices(this.service);
  }

  // Get special price count
  getSpecialPriceCount(): number {
    return this.serviceLabelLogicService.getSpecialPriceCount(this.service);
  }

  // Check if service has any discount (special price or custom discount)
  hasDiscount(): boolean {
    return this.serviceLabelLogicService.hasDiscount(this.service);
  }

  // Get the original price before discount
  getOriginalPrice(): number {
    return this.serviceLabelLogicService.getOriginalPrice(this.service);
  }

  // Get the discount percentage
  getDiscountPercent(): number {
    return this.serviceLabelLogicService.getDiscountPercent(this.service);
  }

  // Calculate discount price
  calculateDiscount(): number {
    return this.serviceLabelLogicService.calculateDiscount(this.service);
  }

  // Legacy method name for backward compatibility
  caculateDiscount(): number {
    return this.calculateDiscount();
  }
}
