package tndung.vnfb.smm.mapper;

import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.dto.request.CurrencyRes;
import tndung.vnfb.smm.entity.Currency;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class CurrencyMapperImpl implements CurrencyMapper {

    @Override
    public CurrencyRes toRes(Currency entity) {
        if ( entity == null ) {
            return null;
        }

        CurrencyRes currencyRes = new CurrencyRes();

        currencyRes.setCode( entity.getCode() );
        currencyRes.setExchangeRate( entity.getExchangeRate() );
        currencyRes.setSymbol( entity.getSymbol() );

        return currencyRes;
    }
}
