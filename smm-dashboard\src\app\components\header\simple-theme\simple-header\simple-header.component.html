<!-- Simple Header Theme - Completely New Design -->
<div class="simple-header-modern" *ngIf="headerState$ | async as headerState">
  <div class="header-wrapper">

    <!-- Left Section -->
    <div class="header-left">
      <!-- Mobile Menu Toggle with new icon -->
      <button class="mobile-toggle-btn" (click)="toggleSidebar()">
        <svg class="toggle-icon" viewBox="0 0 24 24" fill="currentColor">
          <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
        </svg>
      </button>
    </div>

    <!-- Center Navigation -->
    <nav class="center-nav">
      <div class="nav-pills">
        <a class="nav-pill active">
          <svg class="nav-icon" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
          </svg>
          {{ 'service' | translate }}
        </a>
        <a class="nav-pill">
          <svg class="nav-icon" viewBox="0 0 24 24" fill="currentColor">
            <path d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
          </svg>
          {{ 'grocery' | translate }}
        </a>
        <a class="nav-pill">
          <svg class="nav-icon" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
          {{ 'resources' | translate }}
        </a>
      </div>
    </nav>

    <!-- Right Actions -->
    <div class="header-right">

      <!-- Balance Card -->
      <div class="balance-card">
        <div class="balance-icon">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/>
          </svg>
        </div>
        <div class="balance-info">
          <span class="balance-label">Balance</span>
          <span class="balance-value">{{ headerState.formattedBalance }}</span>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <app-interface-switcher></app-interface-switcher>
        <app-lang-dropdown></app-lang-dropdown>
        <app-user-notification-dropdown></app-user-notification-dropdown>
        <app-avatar></app-avatar>
      </div>

    </div>
  </div>
</div>
