<div class="providers-container">
  <!-- Header Section -->
  <div class="mb-8">
    <h1 class="text-2xl font-semibold text-gray-800">{{ 'providers.title' | translate }}</h1>
  </div>



  <!-- Your Providers Section -->
  <div>
    <h2 class="text-xl font-semibold text-gray-800 mb-4">{{ 'providers.your_providers' | translate }}</h2>

    <!-- Provider List -->
    <div class="space-y-4">


      <!-- Dynamic Provider Items -->
      <div *ngFor="let provider of providers" class="flex items-center justify-between bg-white rounded-lg p-4 shadow-sm">
        <div class="flex items-center gap-3">
          <div class="w-8 h-8 flex-shrink-0 rounded-md flex items-center justify-center" [ngClass]="getProviderColor(provider.id)">
            <fa-icon [icon]="['fas', 'cube']" class="text-white text-lg"></fa-icon>
          </div>
          <span class="text-gray-800 font-medium">{{ provider.name }}</span>
        </div>
        <div class="flex items-center gap-3">
          <span class="text-gray-700 font-medium">${{ provider.balance }}</span>
          <button class="text-gray-500 hover:text-gray-700" (click)="toggleActionMenu($event, provider)">
            <fa-icon [icon]="['fas', 'ellipsis-h']"></fa-icon>
          </button>

          <!-- Provider Action Menu -->
          <div *ngIf="activeActionMenu === provider.id"
               class="fixed dropdown-menu z-50 w-40 bg-white shadow-lg rounded-lg overflow-hidden"
               [style.top.px]="menuPosition.top"
               [style.left.px]="menuPosition.left"
               appClickOutside
               (clickOutside)="closeActionMenu()">
            <div class="py-1">
              <a (click)="openChangeKeyModal(provider); $event.stopPropagation()" class="flex items-center px-4 py-2 text-sm cursor-pointer hover:bg-gray-100">
                <fa-icon [icon]="['fas', 'key']" class="mr-3 text-blue-500 w-4"></fa-icon>
                <span>{{ 'providers.change_key' | translate }}</span>
              </a>
              <a (click)="openBalanceAlertModal(provider); $event.stopPropagation()" class="flex items-center px-4 py-2 text-sm cursor-pointer hover:bg-gray-100">
                <fa-icon [icon]="['fas', 'bell']" class="mr-3 text-blue-500 w-4"></fa-icon>
                <span>{{ 'providers.balance_alert' | translate }}</span>
              </a>

              <a (click)="deleteProvider(provider); $event.stopPropagation()" class="flex items-center px-4 py-2 text-sm cursor-pointer hover:bg-gray-100">
                <fa-icon [icon]="['fas', 'trash']" class="mr-3 text-red-500 w-4"></fa-icon>
                <span>{{ 'providers.delete' | translate }}</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Provider Button -->
    <button class="mt-6 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm flex items-center justify-center w-40" (click)="openAddProviderModal()">
      <fa-icon [icon]="['fas', 'plus']" class="mr-2"></fa-icon>
      {{ 'providers.add_provider' | translate }}
    </button>
  </div>

  <!-- Add/Edit Provider Modal -->
  <app-add-provider
    *ngIf="showAddProviderModal"
    [provider]="selectedProvider"
    [changeKeyMode]="isChangeKeyMode"
    (close)="closeAddProviderModal()"
    (providerAdded)="onProviderAdded($event)">
  </app-add-provider>

  <!-- Balance Alert Modal -->
  <app-balance-alert
    *ngIf="showBalanceAlertModal"
    [provider]="selectedProvider"
    (close)="closeBalanceAlertModal()"
    (balanceAlertUpdated)="onBalanceAlertUpdated($event)">
  </app-balance-alert>

  <!-- Delete Confirmation Modal -->
  <app-delete-confirmation
    *ngIf="showDeleteConfirmation"
    [itemName]="providerToDelete?.name || ('providers.this_provider' | translate)"
    [isLoading]="isDeleting"
    (close)="closeDeleteConfirmation()"
    (confirm)="confirmDeleteProvider()">
  </app-delete-confirmation>
</div>
