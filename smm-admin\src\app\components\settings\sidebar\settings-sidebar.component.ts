import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, NavigationEnd } from '@angular/router';

import { IconName } from '@fortawesome/fontawesome-svg-core';
import { TranslateModule } from '@ngx-translate/core';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { IconsModule } from '../../../icons/icons.module';
import { SettingsSidebarService } from '../../../core/services/settings-sidebar.service';

interface SideNavItem {
  icon: IconName;
  label: string;
  link: string;
  isActive: boolean;
}

interface SideNavSection {
  title: string;
  items: SideNavItem[];
}

@Component({
  selector: 'app-settings-sidebar',
  standalone: true,
  imports: [CommonModule, RouterModule, IconsModule, TranslateModule, FaIconComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './settings-sidebar.component.html',
  styleUrl: './settings-sidebar.component.css'
})
export class SettingsSidebarComponent implements OnInit {
  @Input() isOpen: boolean = true;

  settingsNavItems: SideNavSection[] = [
    {
      title: 'settings.nav.setup',
      items: [
        {
          icon: 'cog' as IconName,
          label: 'settings.nav.general_settings',
          link: '/panel/settings/general',
          isActive: false
        },
        {
          icon: 'server' as IconName,
          label: 'settings.nav.providers',
          link: '/panel/settings/providers',
          isActive: false
        },
        {
          icon: 'credit-card' as IconName,
          label: 'settings.nav.payment_methods',
          link: '/panel/settings/payment-methods',
          isActive: false
        },
        {
          icon: 'palette' as IconName,
          label: 'settings.nav.design',
          link: '/panel/settings/design',
          isActive: false
        },
        {
          icon: 'language' as IconName,
          label: 'settings.nav.internationalization',
          link: '/panel/settings/i18n',
          isActive: false
        },
        {
          icon: 'dollar-sign' as IconName,
          label: 'settings.nav.currency',
          link: '/panel/settings/currency',
          isActive: false
        },

        {
          icon: 'plug' as IconName,
          label: 'settings.nav.integrations',
          link: '/panel/settings/integrations',
          isActive: false
        },

        // {
        //   icon: 'comments' as IconName,
        //   label: 'Interaction',
        //   link: '',
        //   isActive: false
        // }
      ]
    },
    {
      title: 'settings.nav.create',
      items: [
        {
          icon: 'user-plus' as IconName,
          label: 'settings.nav.notify_users',
          link: '/panel/settings/interaction',
          isActive: false
        },
        {
          icon: 'percentage' as IconName,
          label: 'settings.nav.promotions',
          link: '/panel/settings/promotions',
          isActive: false
        },
        {
          icon: 'tags' as IconName,
          label: 'settings.nav.promo_codes',
          link: '/panel/settings/promo-codes',
          isActive: false
        },
        // {
        //   icon: 'file' as IconName,
        //   label: 'Extra pages',
        //   link: '/admin/settings/extra-pages',
        //   isActive: false
        // },
        // {
        //   icon: 'search' as IconName,
        //   label: 'Meta data & SEO',
        //   link: '/admin/settings/meta-seo',
        //   isActive: false
        // }
      ]
    },
    {
      title: 'settings.nav.other',
      items: [

        // {
        //   icon: 'ad' as IconName,
        //   label: 'Ads',
        //   link: '/admin/settings/ads',
        //   isActive: false
        // },
        {
          icon: 'user-cog' as IconName,
          label: 'settings.nav.profile_settings',
          link: '/panel/settings/profile',
          isActive: false
        },  {
          icon: 'server' as IconName,
          label: 'settings.nav.all_panels',
          link: '/panel/settings/all-panels',
          isActive: false
        },
        //  {
        //   icon: 'users' as IconName,
        //   label: 'Managers',
        //   link: '/panel/settings/managers',
        //   isActive: false
        // },
      ]
    }
  ];

  constructor(private router: Router, private sidebarService: SettingsSidebarService) {}

  ngOnInit() {
    // Set initial active state based on current route
    this.updateActiveState(this.router.url);

    // Subscribe to router events to update active state
    this.router.events.subscribe(event => {
      if (event instanceof NavigationEnd) {
        this.updateActiveState(event.url);
      }
    });
  }

  updateActiveState(currentUrl: string) {
    // Reset all active states
    this.settingsNavItems.forEach(section => {
      section.items.forEach(item => {
        item.isActive = currentUrl.includes(item.link);
      });
    });
  }

  getIconBackgroundColor(icon: string): string {
    // Map icons to background colors
    const colorMap: { [key: string]: string } = {
      'cog': 'bg-blue-500',
      'server': 'bg-purple-500',
      'credit-card': 'bg-green-500',
      'palette': 'bg-pink-500',
      'users': 'bg-yellow-500',
      'language': 'bg-indigo-500',
      'headset': 'bg-red-500',
      'plug': 'bg-teal-500',
      'bell': 'bg-orange-500',
      'comments': 'bg-orange-500',
      'user-plus': 'bg-cyan-500',
      'percentage': 'bg-orange-500',
      'tags': 'bg-lime-500',
      'file': 'bg-amber-500',
      'search': 'bg-emerald-500',
      'code': 'bg-violet-500',
      'ad': 'bg-rose-500',
      'user-cog': 'bg-sky-500'
    };

    return colorMap[icon] || 'bg-gray-500';
  }

  /**
   * Close the sidebar on mobile
   */
  closeSidebar(): void {
    this.sidebarService.setSidebarState(false);
  }

  /**
   * Handle mobile item click - close sidebar after navigation
   */
  onMobileItemClick(): void {
    if (window.innerWidth < 768) {
      // Only close on mobile
      setTimeout(() => {
        this.closeSidebar();
      }, 300); // Small delay to allow navigation to complete
    }
  }
}
