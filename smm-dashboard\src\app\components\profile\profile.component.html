<!-- Theme-based conditional rendering -->
<ng-container *ngIf="profileState$ | async as profileState">
<div [ngSwitch]="profileState.currentTheme">
  <!-- Simple Theme -->
  <app-simple-profile *ngSwitchCase="LayoutTheme.SIMPLE"></app-simple-profile>

  <!-- Default Original Theme -->
  <div *ngSwitchDefault>
<div class="profile-container">
  <!-- Profile Header with User Info -->
  <div class="profile-header-card">
    <div class="profile-header-content">
      <div class="profile-avatar">
        <img [src]="getAvatarPath()" alt="Profile" class="avatar-image">
      </div>
      <div class="profile-info">
        <h2 class="profile-name">{{ profileState.user?.user_name || 'User' }}</h2>
        <p class="profile-email">{{ profileState.user?.email || '<EMAIL>' }}</p>
        <div class="profile-status">
          <div class="online-dot"></div>
          <span>{{ 'profile.online' | translate }}</span>
        </div>
      </div>
    </div>
    <div class="profile-stats">
      <div class="stat-item">
        <span class="stat-value">{{ profileState.formattedBalance }}</span>
        <span class="stat-label">{{ 'profile.balance' | translate }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-value">{{profileState.user?.total_order || 0}}</span>
        <span class="stat-label">{{ 'profile.orders' | translate }}</span>
      </div>
    </div>
  </div>

  <!-- Tab Navigation -->
  <div class="profile-tabs">
    <button class="tab-button" [class.active]="profileState.activeTab === 'account'" (click)="setActiveTab('account')">
      <i class="fas fa-user"></i>
      {{ 'profile.account' | translate }}
    </button>
    <button class="tab-button" [class.active]="profileState.activeTab === 'security'" (click)="setActiveTab('security')">
      <i class="fas fa-shield-alt"></i>
      {{ 'profile.security' | translate }}
    </button>
    <button class="tab-button" [class.active]="profileState.activeTab === 'settings'" (click)="setActiveTab('settings')">
      <i class="fas fa-cog"></i>
      {{ 'profile.settings' | translate }}
    </button>
    <button class="tab-button" [class.active]="profileState.activeTab === 'history'" (click)="setActiveTab('history')">
      <i class="fas fa-history"></i>
      {{ 'profile.login_history_tab' | translate }}
    </button>
  </div>

  <!-- Account Information Tab -->
  <div class="tab-content" *ngIf="profileState.activeTab === 'account'">
    <div class="content-card">
      <h2 class="section-title">{{ 'profile.information' | translate }}</h2>

      <!-- Display error message if any -->
      <div *ngIf="profileState.profileError" class="alert alert-danger mb-4">
        {{ profileState.profileError }}
      </div>

      <form [formGroup]="profileState.profileForm" (ngSubmit)="onSubmit()" class="profile-form">
        <div class="form-grid">
          <!-- Left Column -->
          <div class="form-column">
            <!-- Email Field -->
            <div class="form-group">
              <label for="email" class="form-label">
                <i class="fas fa-envelope"></i>
                {{ 'profile.email' | translate }}
              </label>
              <div class="input-with-button">
                <input
                  type="email"
                  id="email"
                  formControlName="email"
                  class="form-input"
                  [ngClass]="{'input-error': profileState.profileForm.get('email')?.invalid && profileState.profileForm.get('email')?.touched}"
                  placeholder="<EMAIL>"
                >
                <button
                  type="button"
                  class="verify-button"
                  (click)="verifyEmail()"
                  [disabled]="profileState.isVerifying || profileState.profileForm.get('email')?.invalid">
                  <i class="fas fa-check-circle"></i>
                  {{ profileState.isVerifying ? ('profile.verifying' | translate) : ('profile.verify' | translate) }}
                </button>
              </div>
              <div *ngIf="profileState.profileForm.get('email')?.invalid && profileState.profileForm.get('email')?.touched" class="error-message">
                <div *ngIf="profileState.profileForm.get('email')?.errors?.['required']">{{ 'profile.email_required' | translate }}</div>
                <div *ngIf="profileState.profileForm.get('email')?.errors?.['email']">{{ 'profile.email_invalid' | translate }}</div>
              </div>
            </div>

            <!-- Phone Number Field -->
            <div class="form-group">
              <label for="phone" class="form-label">
                <i class="fas fa-phone"></i>
                {{ 'profile.phone_number' | translate }}
              </label>
              <input
                type="text"
                id="phone"
                formControlName="phone"
                class="form-input"
                [ngClass]="{'input-error': profileState.profileForm.get('phone')?.invalid && profileState.profileForm.get('phone')?.touched}"
                placeholder="+84 123 456 789"
              >
              <div *ngIf="profileState.profileForm.get('phone')?.invalid && profileState.profileForm.get('phone')?.touched" class="error-message">
                <div *ngIf="profileState.profileForm.get('phone')?.errors?.['required']">{{ 'profile.phone_required' | translate }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Update Button -->
        <div class="button-container">
          <button
            type="submit"
            class="update-button"
            [disabled]="profileState.isLoading || profileState.profileForm.invalid">
            <i class="fas fa-save"></i>
            {{ profileState.isLoading ? ('profile.updating' | translate) : ('profile.update' | translate) }}
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Security Tab -->
  <div class="tab-content" *ngIf="profileState.activeTab === 'security'">
    <div class="content-card">
      <h2 class="section-title">{{ 'profile.security' | translate }}</h2>

      <!-- Password Change Section -->
      <div class="security-section">
        <h3 class="subsection-title">
          <i class="fas fa-lock"></i>
          {{ 'profile.change_password' | translate }}
        </h3>

        <!-- Display error message if any -->
        <div *ngIf="profileState.passwordError" class="alert alert-danger mb-4">
          {{ profileState.passwordError }}
        </div>

        <form [formGroup]="profileState.passwordForm" (ngSubmit)="onChangePassword()" class="password-form">
          <!-- Current Password Field -->
          <div class="form-group">
            <label for="currentPassword" class="form-label">{{ 'profile.current_password' | translate }}</label>
            <div class="password-input-container">
              <input
                [type]="profileState.showCurrentPassword ? 'text' : 'password'"
                id="currentPassword"
                formControlName="currentPassword"
                class="form-input"
                [ngClass]="{'input-error': profileState.passwordForm.get('currentPassword')?.invalid && profileState.passwordForm.get('currentPassword')?.touched}"
              >
              <button type="button" class="toggle-password" (click)="togglePasswordVisibility('current')">
                <i class="fas" [ngClass]="profileState.showCurrentPassword ? 'fa-eye-slash' : 'fa-eye'"></i>
              </button>
            </div>
            <div *ngIf="profileState.passwordForm.get('currentPassword')?.invalid && profileState.passwordForm.get('currentPassword')?.touched" class="error-message">
              <div *ngIf="profileState.passwordForm.get('currentPassword')?.errors?.['required']">{{ 'profile.current_password_required' | translate }}</div>
            </div>
          </div>

          <!-- New Password Field -->
          <div class="form-group">
            <label for="newPassword" class="form-label">{{ 'profile.new_password' | translate }}</label>
            <div class="password-input-container">
              <input
                [type]="profileState.showNewPassword ? 'text' : 'password'"
                id="newPassword"
                formControlName="newPassword"
                class="form-input"
                [ngClass]="{'input-error': profileState.passwordForm.get('newPassword')?.invalid && profileState.passwordForm.get('newPassword')?.touched}"
              >
              <button type="button" class="toggle-password" (click)="togglePasswordVisibility('new')">
                <i class="fas" [ngClass]="profileState.showNewPassword ? 'fa-eye-slash' : 'fa-eye'"></i>
              </button>
            </div>
            <div *ngIf="profileState.passwordForm.get('newPassword')?.invalid && profileState.passwordForm.get('newPassword')?.touched" class="error-message">
              <div *ngIf="profileState.passwordForm.get('newPassword')?.errors?.['minlength']">{{ 'profile.password_min_length' | translate }}</div>
            </div>
          </div>

          <!-- Confirm Password Field -->
          <div class="form-group">
            <label for="confirmPassword" class="form-label">{{ 'profile.confirm_password' | translate }}</label>
            <div class="password-input-container">
              <input
                [type]="profileState.showConfirmPassword ? 'text' : 'password'"
                id="confirmPassword"
                formControlName="confirmPassword"
                class="form-input"
                [ngClass]="{'input-error': profileState.passwordForm.get('confirmPassword')?.invalid && profileState.passwordForm.get('confirmPassword')?.touched}"
              >
              <button type="button" class="toggle-password" (click)="togglePasswordVisibility('confirm')">
                <i class="fas" [ngClass]="profileState.showConfirmPassword ? 'fa-eye-slash' : 'fa-eye'"></i>
              </button>
            </div>
            <div *ngIf="profileState.passwordForm.get('confirmPassword')?.invalid && profileState.passwordForm.get('confirmPassword')?.touched" class="error-message">
              <div *ngIf="profileState.passwordForm.get('confirmPassword')?.errors?.['passwordMismatch']">{{ 'profile.passwords_not_match' | translate }}</div>
            </div>
          </div>

          <div class="button-container">
            <button
              type="submit"
              class="update-button"
              [disabled]="profileState.isPasswordLoading || profileState.passwordForm.invalid">
              <i class="fas fa-key"></i>
              <span *ngIf="!profileState.isPasswordLoading">{{ 'profile.update_password' | translate }}</span>
              <span *ngIf="profileState.isPasswordLoading">{{ 'profile.updating_password' | translate }}</span>
            </button>
          </div>
        </form>
      </div>

      <!-- 2FA Google Authentication -->
      <div class="security-item">
        <div class="security-content">
          <div class="security-header">
            <h3 class="security-title">
              <i class="fas fa-shield-alt"></i>
              {{ 'profile.two_factor_auth' | translate }}
            </h3>
            <div class="security-status" [ngClass]="{'status-enabled': profileState.is2FAEnabled, 'status-disabled': !profileState.is2FAEnabled}">
              <!-- <span>{{ profileState.is2FAEnabled ? ('profile.enabled' | translate) : ('profile.disabled' | translate) }}</span> -->
            </div>
          </div>
          <p class="security-description">{{ 'profile.two_factor_auth_description' | translate }}</p>
        </div>
        <div class="security-action">
          <button
            *ngIf="profileState.is2FAEnabled"
            class="mfa-button mfa-enable-button"
            (click)="toggle2FA()">
            <i class="fas fa-shield-alt mr-2"></i>
            {{ 'profile.disable' | translate }} 2FA Google Authentication
          </button>
          <button
            *ngIf="!profileState.is2FAEnabled"
            class="mfa-button mfa-disable-button"
            (click)="toggle2FA()">
            <i class="fas fa-shield-alt mr-2"></i>
            {{ 'profile.enable' | translate }} 2FA Google Authentication
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Settings Tab -->
  <div class="tab-content" *ngIf="profileState.activeTab === 'settings'">
    <div class="content-card">
      <h2 class="section-title">{{ 'profile.settings' | translate }}</h2>

      <!-- Language Setting -->
      <div class="setting-item">
        <div class="setting-icon">
          <i class="fas fa-language"></i>
        </div>
        <div class="setting-label">{{ 'profile.language' | translate }}</div>
        <div class="setting-dropdown">
          <div class="relative inline-block text-left w-full border rounded-lg">
            <button
              type="button"
              class="w-full dropdown-container p-4 py-3"
              (click)="showLanguageDropdown = !showLanguageDropdown">
              <div class="flex items-center">
                <span [class]="getCurrentLanguageFlag()" class="w-4 h-3 mr-2 rounded"></span>
                <span>{{ selectedLanguage }}</span>
              </div>
              <i class="fas fa-angle-down absolute right-5 top-1/2 transform -translate-y-1/2 w-5 h-5"></i>
            </button>

            <div *ngIf="showLanguageDropdown" class="absolute right-0 left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-md z-50 max-h-60 overflow-y-auto">
              <button
                *ngFor="let lang of availableLanguages"
                (click)="onLanguageChange(lang.name); showLanguageDropdown = false"
                class="flex items-center w-full px-4 py-3 text-left hover:bg-gray-100 transition-colors"
                [class.font-bold]="selectedLanguage === lang.name">
                <span [class]="lang.flag" class="w-4 h-3 mr-2 rounded"></span>
                {{ lang.name }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Currency Setting -->
      <div class="setting-item">
        <div class="setting-icon">
          <i class="fas fa-money-bill-wave"></i>
        </div>
        <div class="setting-label">{{ 'profile.currency' | translate }}</div>
        <div class="setting-dropdown">
          <app-lite-dropdown
            [options]="currencyOptions"
            [selectedOption]="selectedCurrency"
            (selected)="onCurrencyChange($event)"
            [customClassDropdown]="'border rounded-lg'"
            [customClassButton]="'py-3'"
          ></app-lite-dropdown>
        </div>
      </div>
      <!-- Timezone Setting -->
      <div class="setting-item">
        <div class="setting-icon">
          <i class="fas fa-globe"></i>
        </div>
        <div class="setting-label">{{ 'profile.timezone' | translate }}</div>
        <div class="setting-dropdown">
          <app-lite-dropdown
            [options]="timezoneOptions"
            [selectedOption]="selectedTimezone"
            (selected)="onTimezoneChange($event)"
            [customClassDropdown]="'border rounded-lg'"
            [customClassButton]="'py-3'"
          ></app-lite-dropdown>
        </div>
      </div>
    </div>
  </div>

  <!-- Login History Tab -->
  <div class="tab-content" *ngIf="profileState.activeTab === 'history'">
    <div class="content-card">
      <h2 class="section-title">
        <i class="fas fa-history"></i>
        {{ 'profile.login_history' | translate }}
      </h2>

      <div *ngIf="profileState.isLoadingHistory" class="loading-indicator">
        <div class="spinner"></div>
        <span>{{ 'profile.loading_history' | translate }}</span>
      </div>

      <div *ngIf="!profileState.isLoadingHistory && (!profileState.loginHistory || profileState.loginHistory.length === 0)" class="no-history">
        <i class="fas fa-exclamation-circle"></i>
        {{ 'profile.no_login_history' | translate }}
      </div>

      <div *ngIf="!profileState.isLoadingHistory && profileState.loginHistory && profileState.loginHistory.length > 0">
        <!-- Desktop Table View (hidden on mobile) -->
        <div class="history-table-container hidden md:block">
          <table class="history-table">
            <thead>
              <tr>
                <th>{{ 'profile.time' | translate }}</th>
                <th>{{ 'profile.ip' | translate }}</th>
                <th>{{ 'profile.device' | translate }}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let item of profileState.loginHistory; let i = index">
                <td>{{ item.created_at | date:'medium' }}</td>
                <td>{{ item.ip || 'N/A' }}</td>
                <td class="device-info">{{ item.user_agent }}</td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Mobile Card View (shown only on mobile) -->
        <div class="history-mobile-cards md:hidden">
          <div class="history-mobile-card" *ngFor="let item of profileState.loginHistory; let i = index">
            <div class="history-mobile-card-header">
              <i class="fas fa-clock history-mobile-icon"></i>
              <span class="history-mobile-time">{{ item.created_at | date:'medium' }}</span>
            </div>
            <div class="history-mobile-card-content">
              <div class="history-mobile-item">
                <div class="history-mobile-label">
                  <i class="fas fa-network-wired"></i>
                  {{ 'profile.ip' | translate }}:
                </div>
                <div class="history-mobile-value">{{ item.ip || 'N/A' }}</div>
              </div>
              <div class="history-mobile-item">
                <div class="history-mobile-label">
                  <i class="fas fa-laptop"></i>
                  {{ 'profile.device' | translate }}:
                </div>
                <div class="history-mobile-value">{{ item.user_agent }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Pagination (for both views) -->
        <div class="history-footer">
          <div class="history-count">
            {{ profileState.loginHistory.length }} {{ 'profile.login_records' | translate }}
          </div>
          <div class="pagination">
            <button class="pagination-button" [disabled]="profileState.currentPage === 1" (click)="changePage(profileState.currentPage - 1)">
              <i class="fas fa-chevron-left"></i>
            </button>
            <span class="pagination-info">{{ 'profile.page' | translate }} {{ profileState.currentPage }} {{ 'profile.of' | translate }} {{ profileState.totalPages }}</span>
            <button class="pagination-button" [disabled]="profileState.currentPage === profileState.totalPages" (click)="changePage(profileState.currentPage + 1)">
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
  </div>
</div>
</ng-container>

<!-- MFA Setting Popup -->
<app-mfa-setting
  *ngIf="showMfaSettings && (currentTheme$ | async) === LayoutTheme.DEFAULT"
  (close)="closeMfaSettings()"
  (enabled)="onMfaEnabled()">
</app-mfa-setting>

<app-simple-mfa-setting
  *ngIf="showMfaSettings && (currentTheme$ | async) === LayoutTheme.SIMPLE"
  (close)="closeMfaSettings()"
  (enabled)="onMfaEnabled()">
</app-simple-mfa-setting>

<!-- MFA Disabled Popup -->
<app-mfa-disabled
  *ngIf="showMfaDisabled"
  (close)="closeMfaDisabled()"
  (disabled)="onMfaDisabled()">
</app-mfa-disabled>
