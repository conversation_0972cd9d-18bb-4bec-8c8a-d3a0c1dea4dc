import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { Router } from '@angular/router';
import { TicketService } from '../../../core/services/ticket.service';
import { TicketRes } from '../../../model/response/ticket-res.model';
import { ThemeService, LayoutTheme } from '../../../core/services/theme.service';

export interface TicketState {
  // Data
  tickets: TicketRes[];
  
  // UI State
  isAllChecked: boolean;
  isLoading: boolean;
  viewMode: 'table' | 'card';
  showModal: boolean;
  
  // Pagination
  pagination: {
    pageNumber: number;
    pageSize: number;
    totalElements: number;
    totalPages: number;
  };
  
  // Theme
  currentTheme: LayoutTheme;
}

@Injectable({
  providedIn: 'root'
})
export class TicketLogicService {
  private subscriptions: Subscription[] = [];

  // State management
  private _state$ = new BehaviorSubject<TicketState>({
    tickets: [],
    isAllChecked: false,
    isLoading: false,
    viewMode: 'table',
    showModal: false,
    pagination: {
      pageNumber: 0,
      pageSize: 20,
      totalElements: 0,
      totalPages: 0
    },
    currentTheme: LayoutTheme.DEFAULT
  });

  public readonly state$ = this._state$.asObservable();

  constructor(
    private router: Router,
    private ticketService: TicketService,
    private themeService: ThemeService
  ) {
    this.initializeSubscriptions();
  }

  private initializeSubscriptions(): void {
    // Subscribe to tickets
    this.subscriptions.push(
      this.ticketService.tickets$.subscribe(tickets => {
        this.updateState({ tickets });
      })
    );

    // Subscribe to loading state
    this.subscriptions.push(
      this.ticketService.loading$.subscribe(isLoading => {
        this.updateState({ isLoading });
      })
    );

    // Subscribe to pagination
    this.subscriptions.push(
      this.ticketService.pagination$.subscribe(pagination => {
        this.updateState({ pagination });
      })
    );

    // Subscribe to theme changes
    this.subscriptions.push(
      this.themeService.currentLayoutTheme$.subscribe(currentTheme => {
        this.updateState({ currentTheme });
      })
    );
  }

  private updateState(partialState: Partial<TicketState>): void {
    const currentState = this._state$.getValue();
    this._state$.next({ ...currentState, ...partialState });
  }

  // Public methods for components to use
  loadTickets(page: number = 0): void {
    this.ticketService.getMyTickets(page).subscribe();
  }

  detectMobileDevice(): void {
    // Check if screen width is less than 768px (typical mobile breakpoint)
    const viewMode = window.innerWidth < 768 ? 'card' : 'table';
    this.updateState({ viewMode });
  }

  toggleAllCheckboxes(): void {
    // Not implemented with the new API model
    const currentState = this._state$.getValue();
    this.updateState({ isAllChecked: !currentState.isAllChecked });
  }

  updateCheckAll(): void {
    // Not implemented with the new API model
  }

  navigateTo(id: number): void {
    this.router.navigate(['/dashboard/tickets', id]);
  }

  openModal(): void {
    this.updateState({ showModal: true });
  }

  closeModal(): void {
    this.updateState({ showModal: false });
    // Refresh the ticket list to ensure any new tickets are displayed
    const currentState = this._state$.getValue();
    this.loadTickets(currentState.pagination.pageNumber);
  }

  changePage(page: number): void {
    const currentState = this._state$.getValue();
    if (page >= 0 && page < currentState.pagination.totalPages) {
      this.loadTickets(page);
    }
  }

  onSearch(searchTerm: string): void {
    console.log('Searching for:', searchTerm);
    if (searchTerm.trim()) {
      this.ticketService.search(searchTerm);
    } else {
      this.loadTickets();
    }
  }

  onTicketCreated(ticket: any): void {
    console.log('New ticket created:', ticket);
    // Refresh the ticket list to include the new ticket
    const currentState = this._state$.getValue();
    this.loadTickets(currentState.pagination.pageNumber);
  }

  // Initialize the service
  initialize(): void {
    this.loadTickets();
    this.detectMobileDevice();
  }

  // Cleanup
  destroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.subscriptions = [];
  }
}
