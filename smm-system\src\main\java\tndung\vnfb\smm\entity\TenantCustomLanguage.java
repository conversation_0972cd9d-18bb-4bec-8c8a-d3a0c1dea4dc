package tndung.vnfb.smm.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import tndung.vnfb.smm.entity.audit.AbstractTenantEntity;

import javax.persistence.*;

@Entity
@Table(name = "tenant_custom_languages", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"tenant_id", "language_code"}))
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TenantCustomLanguage extends AbstractTenantEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;




    @Column(name = "language_code", nullable = false, length = 20)
    private String languageCode;

    @Column(name = "language_name", nullable = false, length = 100)
    private String languageName;

    @Column(name = "flag_class", length = 20)
    private String flagClass;

    @Column(name = "description", length = 500)
    private String description;

    @Column(name = "is_active", nullable = false)
    private boolean isActive = true;



    public TenantCustomLanguage(String languageCode, String languageName, String flagClass, String description) {
        this.languageCode = languageCode;
        this.languageName = languageName;
        this.flagClass = flagClass;
        this.description = description;
    }
}
