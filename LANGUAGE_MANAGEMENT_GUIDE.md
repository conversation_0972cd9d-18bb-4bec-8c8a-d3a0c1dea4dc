# Language Management System Guide

## Overview

The new Language Management System provides a comprehensive solution for managing internationalization (i18n) in the SMM platform. It allows administrators to:

1. **Manage Translations**: Edit translations for different languages
2. **Language Management**: Add predefined and custom languages
3. **Settings**: Configure which languages are available for users and set default language

## Features

### 1. Translations Tab
- **Language Selection**: Choose which language to edit translations for
- **Search & Filter**: Find specific translation keys by search term or category
- **Real-time Editing**: Edit translations directly in the interface
- **Template Loading**: Load default translation templates
- **Bulk Operations**: Save all changes at once or reset modifications

### 2. Languages Tab
- **Predefined Languages**: Choose from 70+ predefined languages with proper flags and names
- **Custom Languages**: Create custom language variants (e.g., "Vietnam 2", "English US")
- **Language Selection**: Click to add/remove languages from your tenant's available languages
- **Custom Language Management**: Edit, delete, and manage custom languages

### 3. Settings Tab
- **Available Languages**: Configure which languages users can select
- **Default Language**: Set the default language for new users and visitors
- **Language Priority**: Manage the order and priority of languages

## Custom Languages

### Creating Custom Languages
1. Go to the **Languages** tab
2. Click **"Add Custom Language"** in the Custom Languages section
3. Fill in the form:
   - **Language Code**: Unique identifier (e.g., `vi2`, `en-us`, `custom1`)
   - **Language Name**: Display name (e.g., "Việt Nam 2", "English (US)")
   - **Flag Class**: CSS class for flag icon (optional, e.g., `fi fi-vn`)
   - **Description**: Optional description
   - **Active**: Whether the language is active

### Use Cases for Custom Languages
- **Regional Variants**: "English (US)" vs "English (UK)"
- **Business Variants**: "Vietnamese (Formal)" vs "Vietnamese (Casual)"
- **Version Control**: "Version 1" vs "Version 2" of translations
- **A/B Testing**: Different translation variants for testing

## API Endpoints

### Language Management
- `GET /api/tenant-settings/languages/predefined` - Get predefined languages
- `GET /api/tenant-settings/languages/custom` - Get custom languages
- `POST /api/tenant-settings/languages/custom` - Create custom language
- `PUT /api/tenant-settings/languages/custom/{code}` - Update custom language
- `DELETE /api/tenant-settings/languages/custom/{code}` - Delete custom language
- `GET /api/tenant-settings/languages/all` - Get all available languages

### Translation Management
- `GET /api/tenant-settings/i18n/{languageCode}` - Get translations for language
- `PUT /api/tenant-settings/i18n/{languageCode}` - Update translations
- `GET /api/tenant-settings/i18n/template` - Get default template
- `POST /api/tenant-settings/i18n/{languageCode}/import` - Import translations

### Settings
- `GET /api/tenant-settings/language` - Get language settings
- `PUT /api/tenant-settings/language` - Update language settings

## Database Schema

### TenantCustomLanguage Entity
```sql
CREATE TABLE tenant_custom_languages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL,
    language_code VARCHAR(20) NOT NULL,
    language_name VARCHAR(100) NOT NULL,
    flag_class VARCHAR(20),
    description VARCHAR(500),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_tenant_language (tenant_id, language_code)
);
```

## Frontend Components

### Main Component
- `I18nManagementComponent`: Main container with tab navigation

### Key Features
- **Responsive Design**: Works on desktop and mobile
- **Real-time Updates**: Changes are reflected immediately
- **Form Validation**: Proper validation for custom language creation
- **Error Handling**: User-friendly error messages
- **Loading States**: Visual feedback during operations

## Usage Examples

### 1. Adding a Custom Language
```typescript
const customLanguage = {
  languageCode: 'vi2',
  languageName: 'Việt Nam 2',
  flagClass: 'fi fi-vn',
  description: 'Alternative Vietnamese translation set',
  isActive: true
};
```

### 2. Setting Available Languages
```typescript
const settings = {
  default_language: 'vi',
  available_languages: ['vi', 'en', 'vi2', 'custom1']
};
```

## Best Practices

1. **Language Codes**: Use descriptive, unique codes (avoid conflicts with ISO codes)
2. **Naming**: Use clear, descriptive names for custom languages
3. **Flags**: Use appropriate flag classes or leave empty for default
4. **Testing**: Always test custom languages before making them available to users
5. **Backup**: Export translations before making major changes

## Migration Guide

### From Old System
1. Existing translations will continue to work
2. Add custom languages as needed
3. Configure language settings in the new Settings tab
4. Test thoroughly before deploying

### Database Migration
Run the SQL script: `smm-system/sql/create_tenant_custom_languages_table.sql`

## Troubleshooting

### Common Issues
1. **Language Code Conflicts**: Ensure custom language codes don't conflict with predefined ones
2. **Flag Icons**: Make sure flag CSS classes are available in your CSS framework
3. **Permissions**: Ensure users have proper roles to access language management
4. **Browser Cache**: Clear browser cache after making changes

### Support
For technical support or questions, please refer to the development team or create an issue in the project repository.
