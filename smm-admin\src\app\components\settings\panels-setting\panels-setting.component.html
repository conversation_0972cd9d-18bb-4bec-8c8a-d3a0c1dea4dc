<div class="panel-layout-container md:p-6 p-2">
  <div class="mb-4">
    <h2 class="text-xl font-bold">{{ 'panels.title' | translate }}</h2>
  </div>

  <!-- Info Banner -->
  <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
    <div class="flex items-center">
      <fa-icon [icon]="['fas', 'lightbulb']" class="text-blue-600 mr-3"></fa-icon>
      <div>
        <div class="text-blue-800 font-medium">{{ 'panels.start_business' | translate }}</div>
        <div class="text-blue-600 text-sm">{{ 'panels.price_per_month' | translate }}</div>
      </div>
    </div>
  </div>

  <!-- New Panel Button -->
  <div class="mb-6">
    <button
      class="px-4 py-2 bg-[#3b82f6] text-white rounded-lg hover:bg-[#2563eb] transition-colors duration-200"
      (click)="createPanel()"
      [disabled]="isCreatingPanel"
    >
      <span *ngIf="!isCreatingPanel">{{ 'panels.new_panel' | translate }}</span>
      <span *ngIf="isCreatingPanel" class="flex items-center">
        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        {{ 'panels.creating' | translate }}
      </span>
    </button>
  </div>

  <!-- Panels Table -->
  <div>
    <!-- Desktop Table View -->
    <div class="hidden md:block overflow-x-auto">
      <div class="inline-block min-w-full align-middle">
        <div class="overflow-hidden rounded-lg shadow-sm">
          <app-loading *ngIf="loading"></app-loading>

          <table *ngIf="!loading" class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ 'panels.domain' | translate }}
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ 'panels.expiry' | translate }}
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ 'panels.days_left' | translate }}
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ 'panels.renew' | translate }}
                  <fa-icon [icon]="['fas', 'info-circle']" class="ml-1 text-gray-400"></fa-icon>
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ 'panels.status' | translate }}
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ 'panels.actions' | translate }}
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr *ngFor="let panel of panels" class="hover:bg-[#f0f7ff] transition-colors duration-150">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {{ panel.domain }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 ">
                  {{ panel.subscription_end_date | date:'short' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                  <span [ngClass]="{
                    'text-yellow-600 font-medium': panel.days_until_expiration !== undefined && panel.days_until_expiration <= 7,
                    'text-green-600 font-medium': panel.days_until_expiration !== undefined && panel.days_until_expiration > 7 && panel.days_until_expiration <= 30,

                    'text-red-500': panel.days_until_expiration === undefined || panel.status === 'Expired'
                  }">
                    {{ getDaysLeftText(panel) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <app-toggle-switch
                  circleColor="#FFF"
                  toggledBgColor="#3B82F6"
                    [isChecked]="panel.auto_renewal"
                    (toggled)="onToggleChange($event, panel)">
                  </app-toggle-switch>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    [ngClass]="getStatusClass(panel.status)"
                    class="px-2 py-1 text-xs font-medium rounded-full">
                    {{ getStatusLabel(panel.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button
                      *ngIf="panel.status === 'Expired'"
                      (click)="onRestore(panel)"
                      class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-xs text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                      <fa-icon [icon]="['fas', 'undo']" class="mr-1"></fa-icon>
                      {{ 'panels.restore' | translate }}
                    </button>

                  </div>
                </td>
              </tr>

              <!-- Empty State Row -->
              <tr *ngIf="panels.length === 0">
                <td colspan="6" class="px-6 py-12 text-center">
                  <div class="flex flex-col items-center justify-center">
                    <fa-icon [icon]="['fas', 'server']" class="text-gray-300 text-4xl mb-3"></fa-icon>
                    <p class="text-gray-500">{{ 'panels.no_panels' | translate }}</p>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Mobile Card View -->
    <div class="md:hidden">
      <app-loading *ngIf="loading"></app-loading>

      <div *ngIf="!loading" class="space-y-4">
        <div *ngFor="let panel of panels"
             class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:border-[#3b82f6] transition-colors duration-150">
          <!-- Card Header -->
          <div class="flex justify-between items-center p-4 border-b border-gray-100">
            <div>
              <div class="font-medium text-gray-900">{{ panel.domain }}</div>
              <div class="text-sm text-gray-500 ">{{ panel.subscription_end_date | date:'short' }}</div>
              <div class="text-xs mt-1" [ngClass]="{
                'text-red-600 font-medium': panel.days_until_expiration !== undefined && panel.days_until_expiration <= 7,
                'text-yellow-600 font-medium': panel.days_until_expiration !== undefined && panel.days_until_expiration > 7 && panel.days_until_expiration <= 30,
                'text-green-600': panel.days_until_expiration !== undefined && panel.days_until_expiration > 30,
                'text-gray-500': panel.days_until_expiration === undefined || panel.status === 'Expired'
              }">
                {{ getDaysLeftText(panel) }}
              </div>
            </div>
            <span
              [ngClass]="getStatusClass(panel.status)"
              class="px-2 py-1 text-xs font-medium rounded-full">
              {{ getStatusLabel(panel.status) }}
            </span>
          </div>

          <!-- Card Body -->
          <div class="p-4 space-y-3">
            <!-- Auto Renewal -->
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <fa-icon [icon]="['fas', 'sync-alt']" class="text-gray-400 w-5"></fa-icon>
                <span class="ml-2 text-sm text-gray-700">{{ 'panels.auto_renewal' | translate }}</span>
              </div>
              <app-toggle-switch
               circleColor="#FFF"
                  toggledBgColor="#3B82F6"
                [isChecked]="panel.auto_renewal"
                (toggled)="onToggleChange($event, panel)">
              </app-toggle-switch>
            </div>
          </div>

          <!-- Card Footer -->
          <div class="px-4 py-3 bg-gray-50 flex justify-end space-x-2">
            <button
              *ngIf="panel.status === 'Expired'"
              (click)="onRestore(panel)"
              class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-xs text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
              <fa-icon [icon]="['fas', 'undo']" class="mr-1"></fa-icon>
              {{ 'panels.restore' | translate }}
            </button>
            <button
              (click)="onActions(panel)"
              class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-xs text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
              <fa-icon [icon]="['fas', 'ellipsis-h']" class="mr-1"></fa-icon>
              {{ 'panels.actions' | translate }}
            </button>
          </div>
        </div>

        <!-- Empty State for Mobile -->
        <div *ngIf="panels.length === 0" class="text-center py-12">
          <div class="flex flex-col items-center justify-center">
            <fa-icon [icon]="['fas', 'server']" class="text-gray-300 text-4xl mb-3"></fa-icon>
            <p class="text-gray-500">{{ 'panels.no_panels' | translate }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
