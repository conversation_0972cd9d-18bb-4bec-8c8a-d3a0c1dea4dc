import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Observable } from 'rxjs';

// Services
import { AuthLogicService, AuthState } from './services/auth-logic.service';

@Component({
  template: '', // Base component has no template
  standalone: true
})
export class BaseAuthComponent implements OnInit, OnDestroy {
  // Expose state as observable for template
  authState$: Observable<AuthState>;

  constructor(protected authLogicService: AuthLogicService) {
    this.authState$ = this.authLogicService.state$;
  }

  ngOnInit(): void {
    // AuthLogicService handles all initialization
  }

  ngOnDestroy(): void {
    // AuthLogicService is singleton, no cleanup needed
  }

  // Delegate methods to AuthLogicService for template compatibility
  onSubmit(): void {
    this.authLogicService.onSubmit();
  }

  loginWithGoogle(): void {
    this.authLogicService.loginWithGoogle();
  }

  setActiveTab(tab: number): void {
    this.authLogicService.setActiveTab(tab);
  }

  // Getters for form controls (used in template)
  get email(): FormControl {
    return this.authLogicService.email;
  }

  get password(): FormControl {
    return this.authLogicService.password;
  }

  get rememberMe(): FormControl {
    return this.authLogicService.rememberMe;
  }
}
