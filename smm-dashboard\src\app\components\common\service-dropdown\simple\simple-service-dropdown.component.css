/* Simple Service Dropdown Styles */

.simple-dropdown-container {
  @apply relative inline-block text-left w-full;
}

.simple-dropdown-button {
  @apply w-full bg-white border border-gray-300 rounded-lg px-4 py-3 text-left shadow-sm;
  @apply hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
  @apply transition-all duration-200 ease-in-out;
  position: relative;
  min-height: 48px;
}

.simple-dropdown-button:hover {
  @apply shadow-md;
}

.dropdown-arrow {
  @apply absolute right-4 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-500;
  @apply transition-transform duration-200;
}

.simple-dropdown-container .simple-dropdown-button:focus .dropdown-arrow,
.simple-dropdown-container .simple-dropdown-button:hover .dropdown-arrow {
  @apply text-gray-700;
}

/* Rotate arrow when dropdown is open */
.simple-dropdown-container[data-open="true"] .dropdown-arrow {
  @apply transform rotate-180 -translate-y-1/2;
}

.simple-dropdown-menu {
  @apply absolute left-0 right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-[99999];
  @apply max-h-64 overflow-y-auto;
  animation: slideDown 0.15s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-list {
  @apply list-none p-0 m-0;
}

.dropdown-item {
  @apply cursor-pointer transition-colors duration-150;
  border-bottom: 1px solid #f3f4f6;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  @apply bg-gray-50;
}

.dropdown-item:active {
  @apply bg-gray-100;
}

.dropdown-item-content {
  @apply px-4 py-3;
}

/* Focus states for accessibility */
.dropdown-item:focus {
  @apply outline-none bg-blue-50;
}

.dropdown-item:focus-visible {
  @apply outline-2 outline-blue-500 outline-offset-2;
}

/* Loading state */
.simple-dropdown-button[disabled] {
  @apply opacity-50 cursor-not-allowed;
}

.simple-dropdown-button[disabled]:hover {
  @apply border-gray-300 shadow-sm;
}

/* Error state */
.simple-dropdown-container.error .simple-dropdown-button {
  @apply border-red-300 focus:ring-red-500 focus:border-red-500;
}

/* Success state */
.simple-dropdown-container.success .simple-dropdown-button {
  @apply border-green-300 focus:ring-green-500 focus:border-green-500;
}

/* Responsive Design */
@media (max-width: 640px) {
  .simple-dropdown-button {
    @apply px-3 py-2;
    min-height: 44px;
  }
  
  .dropdown-arrow {
    @apply right-3;
  }
  
  .dropdown-item-content {
    @apply px-3 py-2;
  }
  
  .simple-dropdown-menu {
    @apply max-h-56;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .simple-dropdown-button {
    @apply bg-gray-800 border-gray-600 text-gray-100;
  }
  
  .simple-dropdown-button:hover {
    @apply border-gray-500;
  }
  
  .simple-dropdown-menu {
    @apply bg-gray-800 border-gray-600;
  }
  
  .dropdown-item {
    border-bottom-color: #4b5563;
  }
  
  .dropdown-item:hover {
    @apply bg-gray-700;
  }
  
  .dropdown-item:active {
    @apply bg-gray-600;
  }
  
  .dropdown-arrow {
    @apply text-gray-400;
  }
  
  .simple-dropdown-button:hover .dropdown-arrow {
    @apply text-gray-200;
  }
}

/* Prevent body scroll when dropdown is open */
body.simple-service-dropdown-open {
  overflow: hidden;
}

/* Custom scrollbar for dropdown menu */
.simple-dropdown-menu::-webkit-scrollbar {
  width: 6px;
}

.simple-dropdown-menu::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded;
}

.simple-dropdown-menu::-webkit-scrollbar-thumb {
  @apply bg-gray-400 rounded;
}

.simple-dropdown-menu::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500;
}
