import { Injectable, ElementRef, Renderer2, OnD<PERSON>roy } from '@angular/core';
import { BehaviorSubject, Subscription } from 'rxjs';
import { DropdownService } from '../../../core/services/dropdown.service';

export interface LiteDropdownState {
  options: string[];
  placeholder: string;
  iconSize: number;
  selectedOption: string | undefined | null;
  customClassButton: string;
  customClassDropdown: string;
  isOpen: boolean;
}

@Injectable()
export class LiteDropdownLogicService implements OnDestroy {
  private subscriptions: Subscription[] = [];
  private dropdownMenuElement: HTMLElement | null = null;
  private justOpened = false;
  private dropdownId: string = `lite-dropdown-${Math.random().toString(36).substring(2, 9)}`;

  private _state$ = new BehaviorSubject<LiteDropdownState>({
    options: [],
    placeholder: 'Chọn một tùy chọn',
    iconSize: 20,
    selectedOption: undefined,
    customClassButton: '',
    customClassDropdown: '',
    isOpen: false
  });

  public state$ = this._state$.asObservable();

  constructor(
    private elementRef: ElementRef,
    private renderer: Renderer2,
    private dropdownService: DropdownService
  ) {
    this.initialize();
  }

  ngOnDestroy(): void {
    this.cleanup();
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private get currentState(): LiteDropdownState {
    return this._state$.value;
  }

  // Getters
  get options(): string[] { return this.currentState.options; }
  get placeholder(): string { return this.currentState.placeholder; }
  get iconSize(): number { return this.currentState.iconSize; }
  get selectedOption(): string | undefined | null { return this.currentState.selectedOption; }
  get customClassButton(): string { return this.currentState.customClassButton; }
  get customClassDropdown(): string { return this.currentState.customClassDropdown; }
  get isOpen(): boolean { return this.currentState.isOpen; }

  // Setters
  setOptions(options: string[]): void {
    this.updateState({ options });
    this.initializeSelectedOption();
  }

  setPlaceholder(placeholder: string): void {
    this.updateState({ placeholder });
  }

  setIconSize(iconSize: number): void {
    this.updateState({ iconSize });
  }

  setSelectedOption(selectedOption: string | undefined | null): void {
    this.updateState({ selectedOption });
    this.initializeSelectedOption();
  }

  setCustomClassButton(customClassButton: string): void {
    this.updateState({ customClassButton });
  }

  setCustomClassDropdown(customClassDropdown: string): void {
    this.updateState({ customClassDropdown });
  }

  private initialize(): void {
    // Subscribe to the closeAllDropdowns observable
    this.subscriptions.push(
      this.dropdownService.closeAllDropdowns.subscribe(() => {
        if (this.isOpen) {
          this.updateState({ isOpen: false });
          this.removeDropdownFromDOM();
          document.body.classList.remove('dropdown-open');
        }
      })
    );
  }

  private initializeSelectedOption(): void {
    const { selectedOption, options } = this.currentState;
    
    // If selectedOption is provided and not null, use it
    if (selectedOption) {
      // Make sure the selectedOption is in the options list
      if (!options.includes(selectedOption) && options.length > 0) {
        this.updateState({ selectedOption: options[0] });
      }
    } else if (options.length > 0 && selectedOption !== null) {
      // Only set default if selectedOption is undefined (not null)
      // This allows parent components to explicitly set null to prevent auto-selection
      this.updateState({ selectedOption: options[0] });
    }
  }

  private updateState(updates: Partial<LiteDropdownState>): void {
    const currentState = this._state$.value;
    this._state$.next({ ...currentState, ...updates });
  }

  onDocumentClick(event: MouseEvent): void {
    // If we just opened the dropdown, ignore this click event
    if (this.justOpened) {
      this.justOpened = false;
      return;
    }

    // Check if the click is outside the dropdown
    const target = event.target as HTMLElement;
    const isInsideDropdown = this.elementRef.nativeElement.contains(target);
    const isInsideDropdownMenu = this.dropdownMenuElement && this.dropdownMenuElement.contains(target);

    if (!isInsideDropdown && !isInsideDropdownMenu) {
      this.updateState({ isOpen: false });
      this.removeDropdownFromDOM();
      document.body.classList.remove('dropdown-open');
    }
  }

  onWindowResize(): void {
    if (this.isOpen) {
      this.updateDropdownPosition();
    }
  }

  onWindowScroll(): void {
    if (this.isOpen) {
      // Use requestAnimationFrame for smoother performance
      requestAnimationFrame(() => {
        this.updateDropdownPosition();
      });
    }
  }

  toggleDropdown(event?: MouseEvent): void {
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }

    const newIsOpen = !this.isOpen;
    this.updateState({ isOpen: newIsOpen });

    if (newIsOpen) {
      // Notify the dropdown service that this dropdown is now open
      this.dropdownService.openDropdown(this.dropdownId, this);

      // Set the flag to indicate we just opened the dropdown
      this.justOpened = true;

      // Add a small delay to ensure the dropdown is rendered before positioning
      setTimeout(() => {
        this.updateDropdownPosition();
        document.body.classList.add('dropdown-open');

        // Force a reflow to ensure the dropdown is properly positioned
        const dropdownMenuElement = this.elementRef.nativeElement.querySelector('.lite-dropdown-menu-container');
        if (dropdownMenuElement) {
          void dropdownMenuElement.offsetHeight;
        }
      }, 0);
    } else {
      this.removeDropdownFromDOM();
      document.body.classList.remove('dropdown-open');
      this.dropdownService.closeDropdown();
    }
  }

  selectOption(option: string, event?: MouseEvent, onSelected?: (option: string) => void): void {
    if (event) {
      event.stopPropagation();
    }

    this.updateState({ 
      selectedOption: option,
      isOpen: false 
    });
    
    if (onSelected) {
      onSelected(option);
    }
    
    this.removeDropdownFromDOM();
    document.body.classList.remove('dropdown-open');
    this.dropdownService.closeDropdown();
  }

  private updateDropdownPosition(): void {
    if (!this.isOpen) return;

    // Get the button element
    const buttonElement = this.elementRef.nativeElement.querySelector('button');
    if (!buttonElement) return;

    // Get the dropdown menu element
    const dropdownMenuElement = this.elementRef.nativeElement.querySelector('.lite-dropdown-menu-container');
    if (!dropdownMenuElement) return;

    // Store reference to the dropdown menu for click detection
    this.dropdownMenuElement = dropdownMenuElement;

    // Get the button's position
    const buttonRect = buttonElement.getBoundingClientRect();

    // Calculate the position for the dropdown
    const top = buttonRect.bottom;
    const left = buttonRect.left;
    const width = buttonRect.width;

    // Set the position and width of the dropdown
    this.renderer.setStyle(dropdownMenuElement, 'position', 'fixed');
    this.renderer.setStyle(dropdownMenuElement, 'top', `${top}px`);
    this.renderer.setStyle(dropdownMenuElement, 'left', `${left}px`);
    this.renderer.setStyle(dropdownMenuElement, 'width', `${width}px`);
    this.renderer.setStyle(dropdownMenuElement, 'z-index', '99999');

    // Check if the dropdown would go off the bottom of the screen
    const dropdownHeight = dropdownMenuElement.offsetHeight;
    const viewportHeight = window.innerHeight;

    if (top + dropdownHeight > viewportHeight) {
      // Position the dropdown above the button instead
      const newTop = buttonRect.top - dropdownHeight;
      if (newTop >= 0) {
        this.renderer.setStyle(dropdownMenuElement, 'top', `${newTop}px`);
      } else {
        // If there's not enough space above either, limit the height and add scrolling
        this.renderer.setStyle(dropdownMenuElement, 'top', '10px');
        this.renderer.setStyle(dropdownMenuElement, 'max-height', `${viewportHeight - 20}px`);
        this.renderer.setStyle(dropdownMenuElement, 'overflow-y', 'auto');
      }
    }
  }

  private removeDropdownFromDOM(): void {
    this.dropdownMenuElement = null;
  }

  private cleanup(): void {
    // Clean up any dropdown menu that might be in the DOM
    this.removeDropdownFromDOM();

    // Remove the class from the body if it was added
    document.body.classList.remove('dropdown-open');
  }
}
