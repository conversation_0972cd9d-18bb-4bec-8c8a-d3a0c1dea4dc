import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Clipboard } from '@angular/cdk/clipboard';
import { TranslateModule } from '@ngx-translate/core';

import { IconDropdownComponent } from "../../common/icon-dropdown/icon-dropdown.component";
import { DateRangePickerComponent } from '../../common/date-range-picker/date-range-picker.component';
import { SearchBoxComponent } from '../../common/search-box/search-box.component';
import { ServiceDropdownComponent } from "../../common/service-dropdown/service-dropdown.component";
import { CurrencyConvertPipe } from '../../../core/pipes/currency-convert.pipe';
import { LoadingComponent } from '../../common/loading/loading.component';
import { DeleteConfirmationComponent } from '../../settings/delete-confirmation/delete-confirmation.component';
import { OrdersBaseComponent } from '../orders-base.component';
import { OrdersLogicService } from '../orders.service';

@Component({
  selector: 'app-simple-orders',
  standalone: true,
  imports: [
    CommonModule, 
    IconDropdownComponent, 
    FormsModule, 
    TranslateModule, 
    SearchBoxComponent, 
    DateRangePickerComponent, 
    ServiceDropdownComponent, 
    CurrencyConvertPipe, 
    LoadingComponent, 
    DeleteConfirmationComponent
  ],
  templateUrl: './simple-orders.component.html',
  styleUrl: './simple-orders.component.css',
  providers: [OrdersLogicService, Clipboard, TranslateModule],
})
export class SimpleOrdersComponent extends OrdersBaseComponent {
}
