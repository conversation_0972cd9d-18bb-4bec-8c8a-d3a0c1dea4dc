import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from './config.service';
import { TenantCurrencyRes } from '../../model/response/tenant-currency-res.model';
import { TenantCurrencyReq } from '../../model/request/tenant-currency-req.model';
import { Currency } from '../../model/response/currency-res.model';

@Injectable({
  providedIn: 'root'
})
export class TenantCurrencyService {

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {}

  getTenantCurrencies(): Observable<TenantCurrencyRes> {
    return this.http.get<TenantCurrencyRes>(
      `${this.configService.apiUrl}/tenant-currencies`
    );
  }

  updateTenantCurrencies(req: TenantCurrencyReq): Observable<TenantCurrencyRes> {
    return this.http.put<TenantCurrencyRes>(
      `${this.configService.apiUrl}/tenant-currencies`,
      req
    );
  }

  getAvailableCurrencies(): Observable<Currency[]> {
    return this.http.get<Currency[]>(
      `${this.configService.apiUrl}/tenant-currencies/available`
    );
  }

  triggerCurrencySync(): Observable<any> {
    return this.http.post(
      `${this.configService.apiUrl}/tenant-currencies/sync`,
      {}
    );
  }
}
