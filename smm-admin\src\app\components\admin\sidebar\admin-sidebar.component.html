<!-- Sidebar component -->
<div class="sidebar" [class.open]="isOpen">
  <div class="flex-container flex flex-col h-full">
    <!-- Logo -->
    <!-- <div class="logo-container">
      <div class="flex items-center justify-center">
        <img [src]="logoUrl" alt="Logo" class="logo h-12 w-auto object-contain">
      </div>
    </div> -->

    <!-- Menu -->
    <div class="menu-sidebar">
      <div *ngFor="let section of adminNavItems" class="menu-section">
        <h3 class="section-title">{{ section.title | translate }}</h3>
        <ul class="menu-list">
          <li *ngFor="let item of section.items"
              class="menu-item"
              [class.active]="item.isActive"
              [routerLink]="item.link"
              routerLinkActive="active">
              <div class="icon-container relative">
                <fa-icon
                  [icon]="['fas', item.icon]"
                  [ngClass]="{'text-[#3b82f6]': item.isActive, 'text-gray-500': !item.isActive}"
                  class="mr-3 text-lg flex-shrink-0 transition-all duration-200"
                ></fa-icon>
                <span *ngIf="item.isActive" class="absolute -bottom-1 -right-1 w-2 h-2 bg-[#3b82f6] rounded-full"></span>
              </div>
              <span class="menu-label" [class.active]="item.isActive">
                {{ item.label | translate }}
              </span>
          </li>
        </ul>
      </div>
    </div>

    <!-- Footer -->
    <div class="mt-auto p-4 text-center text-xs text-gray-500">
      <p class="mb-1">{{ 'admin.copyright' | translate }}</p>
      <p>{{ 'admin.version' | translate }}</p>
    </div>
  </div>
</div>
