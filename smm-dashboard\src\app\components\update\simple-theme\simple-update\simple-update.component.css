/* Simple Update Component Styles */

.simple-update-container {
  padding: 1.5rem;
  background: #f8fafc;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Header */
.simple-header {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  text-align: center;
}

.simple-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

/* Controls */
.simple-controls {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.simple-search-wrapper {
  flex: 1;
  min-width: 250px;
}

.simple-filter-wrapper {
  flex-shrink: 0;
  min-width: 150px;
}

/* Search Box Styles */
.simple-search-container {
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #d1d5db;
  transition: border-color 0.2s ease;
}

.simple-search-container:focus-within {
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.simple-search-input {
  background: white;
  border: none;
  color: #374151;
  padding: 0.625rem 0.75rem;
  font-size: 0.875rem;
  outline: none;
}

.simple-search-button {
  background: #4299e1;
  color: white;
  border: none;
  padding: 0.625rem 1rem;
  font-weight: 500;
  transition: background-color 0.2s ease;
  font-size: 0.875rem;
}

.simple-search-button:hover {
  background: #3182ce;
}

/* Filter Dropdown Styles */
.simple-filter-button {
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 0.625rem 0.75rem;
  font-size: 0.875rem;
  color: #374151;
  transition: border-color 0.2s ease;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.simple-filter-button:hover {
  border-color: #4299e1;
}

.simple-filter-dropdown {
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Loading */
.simple-loading {
  display: flex;
  justify-content: center;
  padding: 3rem;
}

/* Content */
.simple-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

/* Desktop Table */
.simple-table-wrapper {
  overflow-x: auto;
}

.simple-table {
  width: 100%;
  border-collapse: collapse;
}

.simple-table-header {
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
}

.simple-th {
  padding: 1.25rem 1.5rem;
  text-align: left;
  font-weight: 700;
  color: #475569;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 2px solid #e2e8f0;
}

.simple-th-service {
  width: 50%;
}

.simple-th-time {
  width: 25%;
}

.simple-th-status {
  width: 25%;
  text-align: center;
}

.simple-table-body {
  background: white;
}

.simple-table-row {
  transition: all 0.2s;
  border-bottom: 1px solid #f1f5f9;
}

.simple-table-row:hover {
  background: #f8fafc;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.simple-td {
  padding: 1.25rem 1.5rem;
  vertical-align: top;
}

.simple-service-name {
  font-weight: 600;
  color: #1e293b;
  line-height: 1.5;
}

.simple-date {
  font-weight: 600;
  color: #334155;
  margin-bottom: 0.25rem;
}

.simple-time {
  color: #64748b;
  font-size: 0.875rem;
}

.simple-td-status {
  text-align: center;
}

.simple-status-wrapper {
  display: flex;
  justify-content: center;
}

/* Status Badges */
.simple-status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  text-align: center;
  transition: all 0.2s;
}

.simple-status-increase {
  background: linear-gradient(135deg, #f97316, #ea580c);
  color: white;
  box-shadow: 0 2px 8px rgba(249, 115, 22, 0.3);
}

.simple-status-decrease {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  color: white;
  box-shadow: 0 2px 8px rgba(6, 182, 212, 0.3);
}

.simple-status-new {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.simple-status-on {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.simple-status-off {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  color: white;
  box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);
}

.simple-status-default {
  background: #f1f5f9;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.simple-price-change {
  margin-left: 0.5rem;
  font-size: 0.75rem;
  opacity: 0.9;
}

/* Mobile Cards */
.simple-cards-wrapper {
  padding: 1rem;
}

.simple-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  margin-bottom: 1rem;
  padding: 1.25rem;
  transition: all 0.2s;
}

.simple-card:hover {
  border-color: #cbd5e1;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.simple-card-header {
  margin-bottom: 1rem;
}

.simple-card-service {
  font-weight: 700;
  color: #1e293b;
  font-size: 1.1rem;
  line-height: 1.4;
}

.simple-card-body {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.simple-card-time {
  flex: 1;
}

.simple-card-date {
  font-weight: 600;
  color: #334155;
  margin-bottom: 0.25rem;
}

.simple-card-time-value {
  color: #64748b;
  font-size: 0.875rem;
}

.simple-card-status {
  flex-shrink: 0;
}

/* No Data */
.simple-no-data {
  text-align: center;
  padding: 3rem;
  color: #64748b;
  font-size: 1.1rem;
  font-weight: 500;
}

/* Pagination */
.simple-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: white;
  border-top: 1px solid #e2e8f0;
  flex-wrap: wrap;
  gap: 1rem;
}

.simple-pagination-info {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
}

.simple-pagination-controls {
  display: flex;
  gap: 0.25rem;
}

.simple-pagination-button {
  padding: 0.5rem 0.75rem;
  border: 1px solid #e2e8f0;
  background: white;
  color: #64748b;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s;
  cursor: pointer;
}

.simple-pagination-button:hover:not(.simple-pagination-disabled) {
  background: #f8fafc;
  border-color: #cbd5e1;
}

.simple-pagination-active {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border-color: #3b82f6;
}

.simple-pagination-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.simple-pagination-ellipsis {
  padding: 0.5rem 0.75rem;
  color: #64748b;
}

.simple-pagination-size {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.simple-pagination-size-label {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
}

.simple-pagination-select {
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #334155;
  font-size: 0.875rem;
}

/* Responsive */
.desktop-only {
  display: block;
}

.mobile-only {
  display: none;
}

@media (max-width: 768px) {
  .simple-update-container {
    padding: 1rem;
  }
  
  .simple-title {
    font-size: 1.5rem;
  }
  
  .simple-controls {
    flex-direction: column;
  }
  
  .simple-search-wrapper,
  .simple-filter-wrapper {
    min-width: auto;
  }
  
  .desktop-only {
    display: none;
  }
  
  .mobile-only {
    display: block;
  }
  
  .simple-pagination {
    flex-direction: column;
    text-align: center;
  }
  
  .simple-pagination-info {
    order: 3;
  }
  
  .simple-pagination-controls {
    order: 1;
  }
  
  .simple-pagination-size {
    order: 2;
  }
}
