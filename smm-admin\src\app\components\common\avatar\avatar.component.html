<div class="user-menu-container">
    <!-- Standard Header Style -->
    <div *ngIf="currentHeaderStyle === 'standard'" class="profile cursor-pointer" (click)="isOpen= !isOpen">
        <div class="avatar">
            <img [src]="getAvatarPath()" alt="Profile"
                class="w-10 h-10 rounded-full">
        </div>
        <div class="user-info" class="md:block hidden">
            <span class="font-semibold text-base block">{{ user?.user_name }}</span>
        </div>
    </div>

    <!-- Compact Header Style -->
    <div *ngIf="currentHeaderStyle === 'compact'" class="profile-compact cursor-pointer" (click)="isOpen= !isOpen">
        <div class="avatar">
            <img [src]="getAvatarPath()" alt="Profile"
                class="w-8 h-8 rounded-full">
        </div>
        <div class="user-info" class="md:block hidden">
            <span class="font-medium text-sm block">{{ user?.user_name }}</span>
        </div>
    </div>

    <!-- Modern Header Style -->
    <div *ngIf="currentHeaderStyle === 'modern'" class="profile-modern cursor-pointer" (click)="isOpen= !isOpen">
        <div class="avatar">
            <img [src]="getAvatarPath()" alt="Profile"
                class="w-10 h-10 rounded-full border-2 border-[var(--primary)]">
        </div>
        <div class="user-info" class="md:block hidden">
            <span class="font-semibold text-base block">{{ user?.user_name }}</span>
        </div>
    </div>

    <!-- Minimal Header Style -->
    <div *ngIf="currentHeaderStyle === 'minimal'" class="profile-minimal cursor-pointer" (click)="isOpen= !isOpen">
        <div class="avatar">
            <img [src]="getAvatarPath()" alt="Profile"
                class="w-8 h-8 rounded-full">
        </div>
    </div>

    <!-- Mobile View (common for all styles) -->
    <div class="gap-1 md:hidden border border-[var(--primary)] rounded-full p-0.5 relative inline-block cursor-pointer">
        <img [src]="getAvatarPath()" alt="Profile"
            class="w-7 h-7 rounded-full cursor-pointer" (click)="isOpen = !isOpen">
    </div>

    <div class="menu-sidebar" [class.show]="isOpen">
        <div class="profile-menu  items-center gap-2 ">
            <img [src]="getAvatarPath()" alt="Profile"
                class="profile-img">
            <div class="profile-info">
                <div class="profile-name">{{ user?.user_name  }}</div>
                <div class="profile-role">{{ 'avatar.agent' | translate }}</div>
            </div>
        </div>
        <hr class="menu-divider mb-4">
        <div class="menu-items mb-4">
            <div class="menu-item cursor-pointer" (click)="goToSettings()">
                <app-svg-icon [iconName]="'task'"  color="#a0aec0"></app-svg-icon>
                <span>{{ 'avatar.account_info' | translate }}</span>
            </div>

            <div class="menu-item cursor-pointer" (click)="goToSecuritySettings()">
               <app-svg-icon [iconName]="'security'"  color="#a0aec0"></app-svg-icon>
                <span>{{ 'avatar.change_password' | translate }}</span>
            </div>
        </div>

        <hr class="menu-divider mb-4">
        <div class="balance-info mb-4  gap-6 px-4">
            <div class="balance">
                <span class="font-bold">{{ formattedBalance }}</span>
                <span  class="text-xs text-[#a0aec0]">{{ 'avatar.balance' | translate }}</span>
            </div>
            <div class="orders">
                <span class="font-bold">{{ user?.total_order }}</span>
                <span class="text-xs text-[#a0aec0]">{{ 'avatar.total_orders' | translate }}</span>
            </div>
        </div>
        <hr class="menu-divider mb-4">
        <div class="logout " (click)="logout()">
            <app-svg-icon [iconName]="'out'"  color="#e53e3e" ></app-svg-icon>

            <span>{{ 'avatar.logout' | translate }}</span>
        </div>
    </div>
</div>

<div class="overlay" *ngIf="isOpen" (click)="toggleMenu()"></div>