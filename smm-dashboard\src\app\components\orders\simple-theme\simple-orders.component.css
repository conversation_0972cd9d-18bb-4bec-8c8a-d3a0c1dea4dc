/* Simple Orders Theme */
.simple-orders-container {
  @apply p-4 bg-slate-50 min-h-screen;
}

/* Header */
.simple-header {
  @apply flex justify-between items-center mb-6 p-4 bg-white rounded-lg shadow-sm;
}

.simple-title {
  @apply text-2xl font-semibold text-gray-800 m-0;
}

.simple-actions {
  @apply flex gap-2;
}

/* Buttons */
.simple-btn {
  @apply px-4 py-2 border-0 rounded-md text-sm font-medium cursor-pointer;
  @apply transition-all duration-200 inline-flex items-center gap-2;
}

.simple-btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.simple-btn-primary {
  @apply bg-blue-500 text-white hover:bg-blue-600;
}

.simple-btn-secondary {
  @apply bg-gray-500 text-white hover:bg-gray-600;
}

.simple-btn-outline {
  @apply bg-transparent text-blue-500 border border-blue-500;
  @apply hover:bg-blue-500 hover:text-white;
}

.simple-btn-warning {
  @apply bg-amber-500 text-white hover:bg-amber-600;
}

.simple-btn-sm {
  @apply px-2 py-1 text-xs;
}

/* Filters */
.simple-filters {
  @apply bg-white rounded-lg p-4 mb-4 shadow-sm hidden;
}

.simple-filters.show {
  @apply block;
}

.simple-filter-grid {
  @apply grid gap-4 mb-4;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.simple-filter-item {
  @apply flex flex-col gap-2;
}

.simple-label {
  @apply text-sm font-medium text-gray-700;
}

.simple-filter-actions {
  @apply flex gap-2 justify-end;
}

/* Status Filters */
.simple-status-filters {
  @apply flex gap-2 mb-4 flex-wrap;
}

.simple-status-btn {
  @apply px-4 py-2 border border-gray-300 bg-white text-gray-500 rounded-md;
  @apply cursor-pointer transition-all duration-200 text-sm;
}

.simple-status-btn:hover {
  @apply border-blue-500 text-blue-500;
}

.simple-status-btn.active {
  @apply bg-blue-500 text-white border-blue-500;
}

/* Bulk Actions */
.simple-bulk-actions {
  @apply flex justify-between items-center p-4 bg-blue-50 border border-blue-200;
  @apply rounded-lg mb-4;
}

.simple-selected-count {
  @apply text-sm text-blue-700 font-medium;
}

.simple-bulk-buttons {
  @apply flex gap-2;
}

/* Table */
.simple-table {
  @apply bg-white rounded-lg overflow-hidden shadow-sm;
}

.simple-table-header {
  @apply grid bg-gray-50 border-b border-gray-200 font-semibold text-gray-700;
  grid-template-columns: 40px 80px 2fr 2fr 100px 120px 120px 120px 120px;
}

.simple-table-row {
  @apply grid border-b border-gray-100 transition-colors duration-200 hover:bg-gray-50;
  grid-template-columns: 40px 80px 2fr 2fr 100px 120px 120px 120px 120px;
}

.simple-table-cell {
  @apply p-3 flex items-center text-sm;
}

.simple-checkbox-cell {
  @apply justify-center;
}

.simple-checkbox {
  @apply w-4 h-4 accent-blue-500;
}

.simple-order-id {
  @apply font-semibold text-gray-800;
}

.simple-service-info {
  @apply flex flex-col gap-1;
}

.simple-service-name {
  @apply font-medium text-gray-800;
}

.simple-service-id {
  @apply text-xs text-gray-500;
}

.simple-link {
  @apply text-blue-500 no-underline hover:underline;
}

.simple-price {
  @apply font-semibold text-emerald-600;
}

.simple-status {
  @apply px-2 py-1 rounded text-xs font-medium uppercase;
}

.simple-date {
  @apply text-gray-500;
}

.simple-actions-menu {
  @apply flex gap-1;
}

.simple-action-btn {
  @apply p-1 border-0 bg-transparent text-gray-500 cursor-pointer rounded;
  @apply transition-all duration-200;
}

.simple-action-btn:hover {
  @apply bg-gray-100 text-gray-700;
}

.simple-refill-btn:hover {
  @apply text-emerald-600;
}

.simple-cancel-btn:hover {
  @apply text-red-600;
}

/* Cards (Mobile) */
.simple-cards {
  @apply grid gap-4;
}

.simple-card {
  @apply bg-white rounded-lg p-4 shadow-sm;
}

.simple-card-header {
  @apply flex justify-between items-center mb-4 pb-2 border-b border-gray-100;
}

.simple-card-content {
  @apply flex flex-col gap-2 mb-4;
}

.simple-card-row {
  @apply flex justify-between items-center;
}

.simple-card-actions {
  @apply flex gap-2 justify-end;
}

/* Empty State */
.simple-empty-state {
  @apply text-center py-12 px-4 bg-white rounded-lg shadow-sm;
}

.simple-empty-icon {
  @apply text-5xl text-gray-300 mb-4;
}

.simple-empty-state h3 {
  @apply text-lg font-semibold text-gray-700 m-0 mb-2;
}

.simple-empty-state p {
  @apply text-gray-500 m-0;
}

/* Pagination */
.simple-pagination {
  @apply flex justify-center items-center gap-2 mt-6;
}

.simple-page-btn {
  @apply p-2 border border-gray-300 bg-white text-gray-700 rounded cursor-pointer;
  @apply transition-all duration-200 w-10 h-10 flex items-center justify-center;
}

.simple-page-btn:hover:not(:disabled) {
  @apply border-blue-500 text-blue-500;
}

.simple-page-btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.simple-page-info {
  @apply px-4 text-sm text-gray-500;
}

/* Status Colors */


/* Responsive */
@media (max-width: 768px) {
  .simple-orders-container {
    @apply p-2;
  }
  
  .simple-header {
    @apply flex-col gap-4 items-stretch;
  }
  
  .simple-filter-grid {
    @apply grid-cols-1;
  }
  
  .simple-status-filters {
    @apply justify-center;
  }
  
  .simple-bulk-actions {
    @apply flex-col gap-4 items-stretch;
  }
  
  .simple-bulk-buttons {
    @apply justify-center;
  }
}