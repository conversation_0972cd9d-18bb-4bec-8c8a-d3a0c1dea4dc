<!-- Simple Theme Loading -->

<!-- Full screen overlay -->
<div *ngIf="fullScreen" class="simple-loading-fullscreen">
  <div class="simple-loading-content">
    <div class="simple-loading-spinner" [ngClass]="'simple-' + size"></div>
    <div *ngIf="message" class="simple-loading-message">{{ message }}</div>
  </div>
</div>

<!-- Container overlay -->
<div *ngIf="overlay && !fullScreen" class="simple-loading-overlay"
     [ngClass]="{'simple-transparent': transparent}">
  <div class="simple-loading-content">
    <div class="simple-loading-spinner" [ngClass]="'simple-' + size"></div>
    <div *ngIf="message" class="simple-loading-message">{{ message }}</div>
  </div>
</div>

<!-- Inline spinner -->
<div *ngIf="!overlay && !fullScreen" class="simple-loading-inline">
  <div class="simple-loading-content">
    <div class="simple-loading-spinner" [ngClass]="'simple-' + size"></div>
    <div *ngIf="message" class="simple-loading-message">{{ message }}</div>
  </div>
</div>
