<!-- Simple Mess Order Theme - Modern Clean Design -->
<div  *ngIf="messOrderState$ | async as messOrderState">
  
  <!-- Header Section -->
  <div class="order-header">
    <div class="header-content">
      <div class="header-title">
        <h2 class="title-text">{{ 'simple_theme.mass_order.bulk_orders' | translate }}</h2>
        <p class="title-description">{{ 'simple_theme.mass_order.bulk_description' | translate }}</p>
      </div>
      
      <!-- Theme Switcher -->
      <div class="theme-switcher">
        <div class="switcher-container">
          <button 
            class="switcher-btn"
            [class.active]="messOrderState.activeComponent === 'simple'"
            (click)="swapComponent('simple')">
            <svg class="switcher-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            <span>{{ 'mass_order.simple_order' | translate }}</span>
          </button>

          <button
            class="switcher-btn"
            [class.active]="messOrderState.activeComponent === 'classic'"
            (click)="swapComponent('classic')">
            <svg class="switcher-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
            </svg>
            <span>{{ 'mass_order.classic_order' | translate }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Order Content -->
  <div class="order-content">
    <div class="content-wrapper">
      
      <!-- Order Component Container -->
      <div class="order-component-container">
        <ng-container [ngSwitch]="messOrderState.activeComponent">
          <app-simple-mess-order *ngSwitchCase="'simple'"></app-simple-mess-order>
          <app-classic-mess-order *ngSwitchCase="'classic'"></app-classic-mess-order>
        </ng-container>
      </div>
      
      <!-- Help Section -->
      <div class="help-section">
        <div class="help-card">
          <div class="help-header">
            <svg class="help-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z"/>
            </svg>
            <h3 class="help-title">{{ 'simple_theme.mass_order.help_title' | translate }}</h3>
          </div>
          <div class="help-content">
            <div class="help-item">
              <span class="help-label">{{ 'mass_order.format' | translate }}:</span>
              <code class="help-code">service_id | link | quantity</code>
            </div>
            <div class="help-item">
              <span class="help-label">{{ 'mass_order.example' | translate }}:</span>
              <code class="help-code">1 | https://example.com | 1000</code>
            </div>
            <div class="help-item">
              <span class="help-label">{{ 'mass_order.note' | translate }}:</span>
              <span class="help-text">{{ 'mass_order.one_order_per_line' | translate }}</span>
            </div>
          </div>
        </div>
      </div>
      
    </div>
  </div>
</div>
