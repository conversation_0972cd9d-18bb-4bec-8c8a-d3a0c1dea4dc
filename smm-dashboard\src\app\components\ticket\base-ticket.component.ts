import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostListener } from '@angular/core';
import { Observable } from 'rxjs';
import { TicketLogicService, TicketState } from './services/ticket-logic.service';

@Component({
  template: '',
  providers: [TicketLogicService]
})
export abstract class BaseTicketComponent implements OnInit, OnDestroy {
  // State from service
  state$: Observable<TicketState>;

  constructor(protected ticketLogicService: TicketLogicService) {
    this.state$ = this.ticketLogicService.state$;
  }

  ngOnInit(): void {
    // Initialize the service
    this.ticketLogicService.initialize();
  }

  ngOnDestroy(): void {
    // Cleanup is handled by the service
    this.ticketLogicService.destroy();
  }

  /**
   * Listen for window resize events to update view mode
   */
  @HostListener('window:resize')
  onResize(): void {
    this.ticketLogicService.detectMobileDevice();
  }

  // Delegate methods to TicketLogicService for template compatibility
  loadTickets(page: number = 0): void {
    this.ticketLogicService.loadTickets(page);
  }

  detectMobileDevice(): void {
    this.ticketLogicService.detectMobileDevice();
  }

  toggleAllCheckboxes(): void {
    this.ticketLogicService.toggleAllCheckboxes();
  }

  updateCheckAll(): void {
    this.ticketLogicService.updateCheckAll();
  }

  navigateTo(id: number): void {
    this.ticketLogicService.navigateTo(id);
  }

  openModal(): void {
    this.ticketLogicService.openModal();
  }

  closeModal(): void {
    this.ticketLogicService.closeModal();
  }

  changePage(page: number): void {
    this.ticketLogicService.changePage(page);
  }

  onSearch(searchTerm: string): void {
    this.ticketLogicService.onSearch(searchTerm);
  }

  onTicketCreated(ticket: any): void {
    this.ticketLogicService.onTicketCreated(ticket);
  }
}
