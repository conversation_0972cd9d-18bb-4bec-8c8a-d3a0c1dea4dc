/* Simple Ticket Detail Theme - Clean Design matching Header/Sidebar */
.simple-ticket-detail-container {
  padding: 1.5rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #f8fafc;
  min-height: calc(100vh - 80px);
}

/* Header Section */
.ticket-header {
  margin-bottom: 1.5rem;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(30, 41, 59, 0.08);
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.header-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(30, 41, 59, 0.2);
}

.header-icon svg {
  width: 20px;
  height: 20px;
}

.header-text {
  flex: 1;
}

.header-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.header-subtitle {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f8fafc;
  padding: 0.5rem 1rem;
  border-radius: 10px;
  font-weight: 600;
  font-size: 0.875rem;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

/* Main Content */
.main-content {
  display: grid;
  grid-template-columns: 1fr 280px;
  gap: 1.5rem;
}

/* Loading */
.loading-container {
  grid-column: 1 / -1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(30, 41, 59, 0.08);
  border: 1px solid rgba(148, 163, 184, 0.1);
}

/* Error Alert */
.error-alert {
  grid-column: 1 / -1;
  background: white;
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 8px rgba(30, 41, 59, 0.06);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.error-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.error-icon {
  color: #ef4444;
  width: 20px;
  height: 20px;
}

.error-text {
  flex: 1;
}

.error-text h3 {
  margin: 0 0 0.25rem 0;
  color: #ef4444;
  font-weight: 600;
  font-size: 0.875rem;
}

.error-text p {
  margin: 0;
  color: #64748b;
  font-size: 0.875rem;
}

.error-close {
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.error-close:hover {
  background: rgba(239, 68, 68, 0.1);
}

/* Ticket Content */
.ticket-content {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(30, 41, 59, 0.08);
  border: 1px solid rgba(148, 163, 184, 0.1);
}

/* Status Banner */
.status-banner {
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  border-left: 3px solid;
}

.status-banner.status-pending {
  background: #fef3c7;
  border-color: #f59e0b;
}

.status-banner.status-accept {
  background: #dbeafe;
  border-color: #3b82f6;
}

.status-banner.status-closed {
  background: #fee2e2;
  border-color: #ef4444;
}

.status-banner.status-solved {
  background: #d1fae5;
  border-color: #10b981;
}

.status-banner-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.status-icon {
  width: 20px;
  height: 20px;
}

.status-text h3 {
  margin: 0 0 0.25rem 0;
  font-weight: 600;
  font-size: 0.875rem;
}

.status-text p {
  margin: 0;
  opacity: 0.8;
  font-size: 0.75rem;
}

/* Chat Area */
.chat-area {
  margin-bottom: 1.5rem;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(148, 163, 184, 0.2);
}

.chat-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
}

.message-count {
  color: #64748b;
  font-size: 0.75rem;
}

.messages-container {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

/* Messages */
.message {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.message.support-message {
  flex-direction: row;
}

.message.user-message {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.support-message .message-avatar {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
}

.user-message .message-avatar {
  background: #64748b;
}

.message-content {
  flex: 1;
  max-width: 75%;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.message-author {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.75rem;
}

.message-time {
  color: #64748b;
  font-size: 0.625rem;
}

.message-body {
  background: #f8fafc;
  padding: 0.75rem;
  border-radius: 10px;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.support-message .message-body {
  background: #eff6ff;
  border-color: rgba(59, 130, 246, 0.2);
}

.message-subject {
  margin: 0 0 0.5rem 0;
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
}

.message-text {
  margin: 0;
  line-height: 1.5;
  color: #475569;
  white-space: pre-line;
  font-size: 0.875rem;
}

/* Empty Messages */
.empty-messages {
  text-align: center;
  padding: 2rem 1rem;
  color: #64748b;
}

.empty-icon {
  width: 40px;
  height: 40px;
  margin-bottom: 0.75rem;
  opacity: 0.5;
}

/* Reply Form */
.reply-form {
  border-top: 1px solid rgba(148, 163, 184, 0.2);
  padding-top: 1.5rem;
}

.reply-error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #fef2f2;
  color: #ef4444;
  padding: 0.75rem;
  border-radius: 10px;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.reply-error button {
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  padding: 0.25rem;
}

.closed-notice {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f1f5f9;
  color: #64748b;
  padding: 0.75rem;
  border-radius: 10px;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.reply-input-container {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1rem;
}

.input-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.input-header h4 {
  margin: 0;
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
}

.countdown {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  font-size: 0.75rem;
}

.reply-textarea {
  width: 100%;
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 10px;
  padding: 0.75rem;
  font-family: inherit;
  font-size: 0.875rem;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.3s ease;
  margin-bottom: 0.75rem;
  min-height: 100px;
}

.reply-textarea:focus {
  outline: none;
  border-color: #1e293b;
  box-shadow: 0 0 0 3px rgba(30, 41, 59, 0.1);
}

.reply-textarea.disabled {
  background: #f1f5f9;
  color: #94a3b8;
  cursor: not-allowed;
}

.reply-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  color: white;
  border: none;
  border-radius: 10px;
  padding: 0.75rem 1.25rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(30, 41, 59, 0.2);
  font-size: 0.875rem;
}

.reply-button:hover:not(.disabled) {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(30, 41, 59, 0.3);
}

.reply-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  width: 14px;
  height: 14px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Ticket Info Sidebar */
.ticket-info {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(30, 41, 59, 0.08);
  border: 1px solid rgba(148, 163, 184, 0.1);
  height: fit-content;
}

.info-header {
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(148, 163, 184, 0.2);
}

.info-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-item label {
  font-weight: 600;
  color: #64748b;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.info-item span {
  color: #1e293b;
  font-size: 0.875rem;
}

.ticket-id {
  color: #1e293b !important;
  font-weight: 700;
}

.status-value {
  font-weight: 600;
}

.info-note {
  display: flex;
  gap: 0.5rem;
  background: #eff6ff;
  padding: 0.75rem;
  border-radius: 10px;
  border-left: 3px solid #3b82f6;
}

.info-note svg {
  color: #3b82f6;
  width: 14px;
  height: 14px;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.info-note p {
  margin: 0;
  color: #1e40af;
  font-size: 0.75rem;
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .ticket-info {
    order: -1;
  }
}

@media (max-width: 768px) {
  .simple-ticket-detail-container {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
    padding: 1rem;
  }

  .header-text {
    order: 1;
  }

  .header-icon {
    order: 0;
  }

  .status-badge {
    order: 2;
  }

  .header-title {
    font-size: 1.25rem;
  }

  .message {
    flex-direction: column !important;
    align-items: flex-start;
  }

  .message-content {
    max-width: 100%;
  }

  .ticket-content,
  .ticket-info {
    padding: 1rem;
  }

  .main-content {
    gap: 1rem;
  }
}
