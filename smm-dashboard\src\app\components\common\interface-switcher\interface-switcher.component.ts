import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconsModule } from '../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { ThemeService, LayoutTheme, ThemeConfig } from '../../../core/services/theme.service';
import { Subscription } from 'rxjs';
import { IconName } from '@fortawesome/fontawesome-svg-core';

@Component({
  selector: 'app-interface-switcher',
  standalone: true,
  imports: [CommonModule, IconsModule, TranslateModule],
  templateUrl: './interface-switcher.component.html',
  styleUrl: './interface-switcher.component.css'
})
export class InterfaceSwitcherComponent implements OnInit, OnDestroy {
  isDropdownOpen = false;
  currentTheme: LayoutTheme = LayoutTheme.DEFAULT;
  availableThemes: ThemeConfig[] = [];
  private subscription: Subscription = new Subscription();

  constructor(private themeService: ThemeService) {}

  ngOnInit(): void {
    // Get available themes
    this.availableThemes = this.themeService.availableLayoutThemes;

    // Subscribe to current theme changes
    this.subscription.add(
      this.themeService.currentLayoutTheme$.subscribe(theme => {
        this.currentTheme = theme;
      })
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  selectTheme(theme: LayoutTheme): void {
    this.themeService.setLayoutTheme(theme);
    this.isDropdownOpen = false;
  }

  getCurrentThemeName(): string {
    const theme = this.availableThemes.find(t => t.id === this.currentTheme);
    return theme ? theme.name : 'Default';
  }

  getThemeIcon(themeId: LayoutTheme): IconName {
    switch (themeId) {
      case LayoutTheme.DEFAULT:
        return 'home' as IconName;
      case LayoutTheme.SIMPLE:
        return 'home' as IconName;
      default:
        return 'home' as IconName;
    }
  }

  onClickOutside(event: Event): void {
    const target = event.target as HTMLElement;
    if (!target.closest('.interface-switcher')) {
      this.isDropdownOpen = false;
    }
  }
}
