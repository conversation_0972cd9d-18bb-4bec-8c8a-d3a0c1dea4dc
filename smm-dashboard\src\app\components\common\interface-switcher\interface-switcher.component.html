<div class="interface-switcher relative" (clickOutside)="onClickOutside($event)">
  <!-- Trigger <PERSON> -->
  <button 
    (click)="toggleDropdown()"
    class="switcher-button flex items-center gap-2 px-3 py-2 rounded-lg bg-white hover:bg-gray-50 border border-gray-200 transition-colors duration-200"
    [class.active]="isDropdownOpen"
  >
    <fa-icon [icon]="['fas', getThemeIcon(currentTheme)]" class="text-gray-600"></fa-icon>
    <span class="text-sm font-medium text-gray-700">{{ getCurrentThemeName() }}</span>
    <fa-icon 
      [icon]="['fas', 'chevron-down']" 
      class="text-gray-400 text-xs transition-transform duration-200"
      [class.rotate-180]="isDropdownOpen"
    ></fa-icon>
  </button>

  <!-- Dropdown Menu -->
  <div 
    *ngIf="isDropdownOpen"
    class="dropdown-menu absolute top-full right-0 mt-2 w-64 bg-white rounded-xl shadow-lg border border-gray-200 z-50 overflow-hidden"
  >
    <!-- Header -->
    <div class="dropdown-header px-4 py-3 border-b border-gray-100">
      <h3 class="text-sm font-semibold text-gray-800">{{ 'interface_style' | translate }}</h3>
      <p class="text-xs text-gray-500 mt-1">{{ 'choose_interface_description' | translate }}</p>
    </div>

    <!-- Theme Options -->
    <div class="theme-options py-2">
      <button
        *ngFor="let theme of availableThemes"
        (click)="selectTheme(theme.id)"
        class="theme-option w-full px-4 py-3 flex items-center gap-3 hover:bg-gray-50 transition-colors duration-150"
        [class.selected]="currentTheme === theme.id"
      >
        <!-- Theme Icon -->
        <div class="theme-icon flex-shrink-0">
          <div 
            class="w-8 h-8 rounded-lg flex items-center justify-center"
            [class.bg-blue-100]="currentTheme === theme.id"
            [class.bg-gray-100]="currentTheme !== theme.id"
          >
            <fa-icon 
              [icon]="['fas', getThemeIcon(theme.id)]" 
              [class.text-blue-600]="currentTheme === theme.id"
              [class.text-gray-600]="currentTheme !== theme.id"
              class="text-sm"
            ></fa-icon>
          </div>
        </div>

        <!-- Theme Info -->
        <div class="theme-info flex-1 text-left">
          <div class="theme-name text-sm font-medium text-gray-800">{{ theme.name }}</div>
          <div class="theme-description text-xs text-gray-500 mt-0.5">{{ theme.description }}</div>
        </div>

        <!-- Selected Indicator -->
        <div class="selected-indicator flex-shrink-0" *ngIf="currentTheme === theme.id">
          <fa-icon [icon]="['fas', 'check']" class="text-blue-600 text-sm"></fa-icon>
        </div>
      </button>
    </div>

    <!-- Footer -->
    <div class="dropdown-footer px-4 py-3 border-t border-gray-100 bg-gray-50">
      <p class="text-xs text-gray-500 text-center">
        {{ 'interface_saved_automatically' | translate }}
      </p>
    </div>
  </div>
</div>
