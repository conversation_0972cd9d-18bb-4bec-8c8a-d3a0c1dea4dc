.overlay-black {
  @apply flex justify-center items-center fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 z-[var(--z-modal)];
}

.modal-container {
  @apply w-full max-w-3xl mx-auto;
}

.modal-content {
  @apply bg-white rounded-xl shadow-lg overflow-hidden p-4;
}

.modal-header {
  @apply flex justify-between items-center mb-6;
  position: relative;
}

.modal-title {
  @apply text-xl font-semibold text-[var(--gray-800)];
}

.close-button {
  @apply p-1 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors;
  position: absolute;
  top: 0;
  right: 0;
}

.global-charge-section {
  @apply mb-6;
}

.charge-label {
  @apply block text-sm font-medium text-[var(--gray-700)] mb-2;
}

.input-container {
  @apply relative;
}

.charge-input {
  @apply w-full px-4 py-3 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent;
}

.services-table-container {
  @apply mb-6 max-h-[400px] overflow-y-auto;

}

.services-table {
  @apply w-full;
  border-collapse: collapse;
}

.services-table th {
  @apply bg-gray-50 text-left text-sm font-medium text-gray-700 px-4 py-3 border-b;
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: #f9fafb;
}

.services-table td {
  @apply px-4 py-3 border-b border-gray-200;
}

.service-column {
  width: 40%;
}

.price-column {
  width: 20%;
  text-align: center;
}

.charge-column {
  width: 20%;
  text-align: center;
}

.service-cell {
  @apply text-sm;
}

.service-id {
  @apply font-medium text-gray-900;
}

.service-name {
  @apply text-gray-600 truncate;
  max-width: 300px;
}

.price-cell {
  @apply text-center text-sm font-medium text-gray-900;
}

.charge-cell {
  @apply text-center;
}

.charge-input-small {
  @apply w-20 px-2 py-1 text-center bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent;
}

.save-button {
  @apply w-full bg-[var(--primary)] text-white font-medium py-3 px-4 rounded-lg hover:bg-[var(--primary-hover)] transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed;
}

.loading-spinner {
  @apply inline-block w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin;
}

.error-message {
  @apply mb-4 p-3 bg-red-50 text-red-700 rounded-lg flex items-center;
}

.success-message {
  @apply mb-4 p-3 bg-green-50 text-green-700 rounded-lg flex items-center;
}
