<div class="simple-services-container">
  <!-- Header -->
  <div class="simple-header">
    <h1 class="simple-title">{{ 'simple_theme.services.title' | translate }}</h1>
  </div>

  <!-- Filters Section -->
  <div class="simple-filters">
    <div class="simple-filter-row">
      <!-- Platform Dropdown -->
      <div class="simple-filter-item">
        <label class="simple-label">{{ 'simple_theme.common.platform' | translate }}</label>
        <app-icon-dropdown
          [options]="platformOptions"
          [placeholder]="'simple_theme.filter.select_platform' | translate"
          (selected)="onPlatformSelected($event)">
        </app-icon-dropdown>
      </div>

      <!-- Category Dropdown -->
      <div class="simple-filter-item">
        <label class="simple-label">{{ 'simple_theme.common.category' | translate }}</label>
        <app-icon-dropdown
          [options]="categoryOptions"
          [selectedOption]="selectedCategory"
          [placeholder]="'simple_theme.filter.select_category' | translate"
          (selected)="onCategorySelected($event)">
        </app-icon-dropdown>
      </div>

      <!-- Search Input -->
      <div class="simple-filter-item simple-search-item">
        <label class="simple-label">{{ 'simple_theme.common.search' | translate }}</label>
        <div class="simple-search-container">
          <input
            #searchInput
            type="text"
            class="simple-search-input"
            placeholder="{{ 'simple_theme.services.search_placeholder' | translate }}"
            (keyup.enter)="applyFilter(searchInput.value)">
          <div class="simple-search-actions">
            <button
              class="simple-btn simple-btn-primary"
              (click)="applyFilter(searchInput.value)">
              {{ 'simple_theme.common.filter' | translate }}
            </button>
            <button
              class="simple-btn simple-btn-secondary"
              (click)="resetFilter(searchInput)">
              {{ 'simple_theme.common.reset' | translate }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading -->
  <app-loading *ngIf="loading"></app-loading>

  <!-- Services Content -->
  <div class="simple-services-content" *ngIf="!loading">
    <!-- Single Category Display -->
    <div class="simple-category-section" *ngIf="currentCategory">
      <div class="simple-category-header">
        <div class="simple-category-info">
          <app-social-icon 
            *ngIf="currentCategory.platformIcon"
            [icon]="currentCategory.platformIcon"
            class="simple-platform-icon">
          </app-social-icon>
          <div class="simple-category-details">
            <h2 class="simple-category-name">{{ currentCategory.name }}</h2>
            <p class="simple-category-desc" *ngIf="currentCategory.description">
              {{ currentCategory.description }}
            </p>
            <div class="simple-category-meta">
              <span class="simple-platform-name">{{ currentCategory.platformName }}</span>
              <span class="simple-service-count">
                {{ currentCategory.services.length }} {{ 'simple_theme.services.services' | translate }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div class="simple-services-grid">
        <div 
          class="simple-service-card"
          *ngFor="let service of currentCategory.services">
          
          <!-- Service Header -->
          <div class="simple-service-header">
            <div class="simple-service-info">
              <h3 class="simple-service-name">{{ service.name }}</h3>
              <span class="simple-service-id">ID: {{ service.id }}</span>
            </div>
            <app-heart-checkbox
              [checked]="favoriteServiceIds.includes(service.id)"
              [loading]="isServiceLoading(service.id)"
              [serviceId]="service.id"
              (toggleFavorite)="onToggleFavorite($event)">
            </app-heart-checkbox>
          </div>

          <!-- Service Details -->
          <div class="simple-service-details">
            <div class="simple-service-row" *ngIf="service.description">
              <span class="simple-service-desc">{{ service.description }}</span>
            </div>
            
            <div class="simple-service-row">
              <span class="simple-label">{{ 'simple_theme.services.price' | translate }}:</span>
              <span class="simple-price">${{ service.price }}</span>
            </div>

            <div class="simple-service-row" *ngIf="service.min">
              <span class="simple-label">{{ 'simple_theme.services.min_order' | translate }}:</span>
              <span>{{ service.min }}</span>
            </div>

            <div class="simple-service-row" *ngIf="service.max">
              <span class="simple-label">{{ 'simple_theme.services.max_order' | translate }}:</span>
              <span>{{ service.max }}</span>
            </div>

            <div class="simple-service-row" *ngIf="service.average_time">
              <span class="simple-label">{{ 'simple_theme.services.avg_time' | translate }}:</span>
              <span>{{ service.average_time | timeFormat }}</span>
            </div>

            <!-- Service Labels -->
            <div class="simple-service-labels" *ngIf="service.refill || service.cancel_button">
              <span
                *ngIf="service.refill"
                class="simple-label-badge simple-label-success">
                {{ 'simple_theme.services.refill_available' | translate }}
              </span>
              <span
                *ngIf="service.cancel_button"
                class="simple-label-badge simple-label-warning">
                {{ 'simple_theme.services.cancel_available' | translate }}
              </span>
            </div>
          </div>

          <!-- Service Actions -->
          <div class="simple-service-actions">
            <button
              class="simple-btn simple-btn-primary simple-order-btn"
              (click)="orderService(service.id)">
              <i class="fas fa-shopping-cart"></i>
              {{ 'simple_theme.services.order_now' | translate }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Multiple Categories Display -->
    <div class="simple-categories-list" *ngIf="displayCategories.length > 0">
      <div 
        class="simple-category-section"
        *ngFor="let category of displayCategories">
        
        <div class="simple-category-header">
          <div class="simple-category-info">
            <app-social-icon 
              *ngIf="category.platformIcon"
              [icon]="category.platformIcon"
              class="simple-platform-icon">
            </app-social-icon>
            <div class="simple-category-details">
              <h2 class="simple-category-name">{{ category.name }}</h2>
              <p class="simple-category-desc" *ngIf="category.description">
                {{ category.description }}
              </p>
              <div class="simple-category-meta">
                <span class="simple-platform-name">{{ category.platformName }}</span>
                <span class="simple-service-count">
                  {{ category.services.length }} {{ 'simple_theme.services.services' | translate }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div class="simple-services-grid">
          <div 
            class="simple-service-card"
            *ngFor="let service of category.services">
            
            <!-- Service Header -->
            <div class="simple-service-header">
              <div class="simple-service-info">
                <h3 class="simple-service-name">{{ service.name }}</h3>
                <span class="simple-service-id">ID: {{ service.id }}</span>
              </div>
              <app-heart-checkbox
                [checked]="favoriteServiceIds.includes(service.id)"
                [loading]="isServiceLoading(service.id)"
                [serviceId]="service.id"
                (toggleFavorite)="onToggleFavorite($event)">
              </app-heart-checkbox>
            </div>

            <!-- Service Details -->
            <div class="simple-service-details">
              <div class="simple-service-row" *ngIf="service.description">
                <span class="simple-service-desc">{{ service.description }}</span>
              </div>
              
              <div class="simple-service-row">
                <span class="simple-label">{{ 'simple_theme.services.price' | translate }}:</span>
                <span class="simple-price">${{ service.price }}</span>
              </div>

              <div class="simple-service-row" *ngIf="service.min">
                <span class="simple-label">{{ 'simple_theme.services.min_order' | translate }}:</span>
                <span>{{ service.min }}</span>
              </div>

              <div class="simple-service-row" *ngIf="service.max">
                <span class="simple-label">{{ 'simple_theme.services.max_order' | translate }}:</span>
                <span>{{ service.max }}</span>
              </div>

              <div class="simple-service-row" *ngIf="service.average_time">
                <span class="simple-label">{{ 'simple_theme.services.avg_time' | translate }}:</span>
                <span>{{ service.average_time | timeFormat }}</span>
              </div>

              <!-- Service Labels -->
              <div class="simple-service-labels" *ngIf="service.refill || service.cancel_button">
                <span
                  *ngIf="service.refill"
                  class="simple-label-badge simple-label-success">
                  {{ 'simple_theme.services.refill_available' | translate }}
                </span>
                <span
                  *ngIf="service.cancel_button"
                  class="simple-label-badge simple-label-warning">
                  {{ 'simple_theme.services.cancel_available' | translate }}
                </span>
              </div>
            </div>

            <!-- Service Actions -->
            <div class="simple-service-actions">
              <button
                class="simple-btn simple-btn-primary simple-order-btn"
                (click)="orderService(service.id)">
                <i class="fas fa-shopping-cart"></i>
                {{ 'simple_theme.services.order_now' | translate }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div class="simple-empty-state" *ngIf="!currentCategory && displayCategories.length === 0">
      <i class="fas fa-search simple-empty-icon"></i>
      <h3>{{ 'simple_theme.services.no_services' | translate }}</h3>
      <p>{{ 'simple_theme.services.no_services_desc' | translate }}</p>
    </div>
  </div>
</div>
