import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';

// Components
import { NewTicketComponent } from "../../../popup/new-ticket/new-ticket.component";
import { IconsModule } from '../../../../icons/icons.module';
import { SimpleSearchBoxComponent } from "../../../common/search-box/simple-theme/simple-search-box/simple-search-box.component";
import { TicketStatusComponent } from '../../../common/ticket-status/ticket-status.component';
import { LoadingComponent } from '../../../common/loading/loading.component';

// Base component and service
import { BaseTicketComponent } from '../../base-ticket.component';
import { TicketLogicService } from '../../services/ticket-logic.service';

@Component({
  selector: 'app-simple-ticket',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    FormsModule,
    NewTicketComponent,
    IconsModule,
    SimpleSearchBoxComponent,
    TicketStatusComponent,
    LoadingComponent
  ],
  templateUrl: './simple-ticket.component.html',
  styleUrl: './simple-ticket.component.css',
  providers: [TicketLogicService]
})
export class SimpleTicketComponent extends BaseTicketComponent implements OnInit, OnDestroy {
  // Expose Array constructor for template
  Array = Array;

  constructor(ticketLogicService: TicketLogicService) {
    super(ticketLogicService);
  }

  override ngOnInit(): void {
    // Call parent initialization
    super.ngOnInit();
  }

  override ngOnDestroy(): void {
    // Call parent cleanup
    super.ngOnDestroy();
  }

  // TrackBy function for ticket list performance
  trackByTicketId(_index: number, ticket: any): number {
    return ticket.id;
  }
}
