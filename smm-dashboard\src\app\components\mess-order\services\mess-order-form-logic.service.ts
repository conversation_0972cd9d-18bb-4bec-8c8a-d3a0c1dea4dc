import { Injectable } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { BehaviorSubject, Subscription, Observable, forkJoin, of } from 'rxjs';
import { catchError } from 'rxjs/operators';

// Services
import { OrderService } from '../../../core/services/order.service';
import { ToastService } from '../../../core/services/toast.service';
import { CategoriesService } from '../../../core/services/categories.service';
import { ThemeService, LayoutTheme } from '../../../core/services/theme.service';

// Models
import { CreateOrderReq } from '../../../core/services/order.service';
import { OrderRes } from '../../../model/response/order-res.model';
import { IconBaseModel } from '../../../model/base-model';
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';
import { SuperPlatformRes } from '../../../model/response/super-platform.model';

export interface MessOrderFormState {
  // Form state
  orderForm: FormGroup;
  
  // UI state
  isProcessing: boolean;
  successCount: number;
  failedCount: number;
  showResults: boolean;
  
  // Data
  categories: IconBaseModel[];
  services: SuperGeneralSvRes[];
  platforms: SuperPlatformRes[];
  selectedCategory: IconBaseModel | undefined;
  selectedServiceObj: SuperGeneralSvRes | undefined;
  
  // Form values
  content: string;
  quantity: number;
  price: number;
  selectedService: string;
  
  // Results
  orderResults: Array<{ link: string; success: boolean; message: string }>;
  
  // Theme management
  currentTheme: LayoutTheme;
}

@Injectable({
  providedIn: 'root'
})
export class MessOrderFormLogicService {
  private subscriptions: Subscription[] = [];

  // State management
  private _state$ = new BehaviorSubject<MessOrderFormState>({
    orderForm: this.createForm(),
    isProcessing: false,
    successCount: 0,
    failedCount: 0,
    showResults: false,
    categories: [],
    services: [],
    platforms: [],
    selectedCategory: undefined,
    selectedServiceObj: undefined,
    content: '',
    quantity: 0,
    price: 0,
    selectedService: 'Test 123',
    orderResults: [],
    currentTheme: LayoutTheme.DEFAULT
  });

  // Public state observable
  public readonly state$ = this._state$.asObservable();

  // Current state getter
  private get currentState(): MessOrderFormState {
    return this._state$.value;
  }

  constructor(
    private fb: FormBuilder,
    private orderService: OrderService,
    private toastService: ToastService,
    private categoriesService: CategoriesService,
    private themeService: ThemeService
  ) {
    this.initialize();
  }

  // Public getters for template access
  get orderForm(): FormGroup {
    return this.currentState.orderForm;
  }

  get isProcessing(): boolean {
    return this.currentState.isProcessing;
  }

  get successCount(): number {
    return this.currentState.successCount;
  }

  get failedCount(): number {
    return this.currentState.failedCount;
  }

  get showResults(): boolean {
    return this.currentState.showResults;
  }

  get categories(): IconBaseModel[] {
    return this.currentState.categories;
  }

  get services(): SuperGeneralSvRes[] {
    return this.currentState.services;
  }

  get platforms(): SuperPlatformRes[] {
    return this.currentState.platforms;
  }

  get selectedCategory(): IconBaseModel | undefined {
    return this.currentState.selectedCategory;
  }

  get selectedServiceObj(): SuperGeneralSvRes | undefined {
    return this.currentState.selectedServiceObj;
  }

  get content(): string {
    return this.currentState.content;
  }

  get quantity(): number {
    return this.currentState.quantity;
  }

  get price(): number {
    return this.currentState.price;
  }

  get selectedService(): string {
    return this.currentState.selectedService;
  }

  get orderResults(): Array<{ link: string; success: boolean; message: string }> {
    return this.currentState.orderResults;
  }

  get currentTheme(): LayoutTheme {
    return this.currentState.currentTheme;
  }

  // Initialize service
  private initialize(): void {
    this.subscribeToThemeChanges();
    this.loadInitialData();
    this.setupFormSubscriptions();
  }

  // Create reactive form
  private createForm(): FormGroup {
    return this.fb.group({
      category: ['', Validators.required],
      service: ['', Validators.required],
      content: ['', [Validators.required, Validators.minLength(1)]],
      quantity: [10, [Validators.required, Validators.min(1)]]
    });
  }

  // Update state helper
  private updateState(updates: Partial<MessOrderFormState>): void {
    const currentState = this._state$.value;
    this._state$.next({ ...currentState, ...updates });
  }

  // Subscribe to theme changes
  private subscribeToThemeChanges(): void {
    const themeSubscription = this.themeService.currentLayoutTheme$.subscribe((theme: LayoutTheme) => {
      this.updateState({ currentTheme: theme });
    });
    this.subscriptions.push(themeSubscription);
  }

  // Load initial data
  private loadInitialData(): void {
    this.loadPlatforms();
  }

  // Setup form subscriptions
  private setupFormSubscriptions(): void {
    const form = this.currentState.orderForm;
    
    // Subscribe to quantity changes
    const quantitySubscription = form.get('quantity')?.valueChanges.subscribe((value: number) => {
      this.updateState({ quantity: value });
      this.calculatePrice();
    });
    
    if (quantitySubscription) {
      this.subscriptions.push(quantitySubscription);
    }

    // Subscribe to content changes
    const contentSubscription = form.get('content')?.valueChanges.subscribe((value: string) => {
      this.updateState({ content: value });
      this.calculatePrice();
    });
    
    if (contentSubscription) {
      this.subscriptions.push(contentSubscription);
    }
  }

  // Load platforms
  private loadPlatforms(): void {
    this.categoriesService.getPlatforms().subscribe({
      next: (platforms: SuperPlatformRes[]) => {
        this.updateState({ platforms: platforms.filter(platform => !platform.hide) });

        // Create categories array from platforms data
        const categories: IconBaseModel[] = [];

        // Sort platforms by sort field
        const sortedPlatforms = [...platforms].sort((a, b) => a.sort - b.sort);

        sortedPlatforms.forEach(platform => {
          // Sort categories by sort field before processing them
          const sortedCategories = [...platform.categories]
            .filter(category => !category.hide)
            .sort((a, b) => a.sort - b.sort);

          sortedCategories.forEach(category => {
            categories.push({
              id: category.id.toString(),
              label: category.name,
              icon: platform.icon,
              sort: category.sort
            });
          });
        });

        // Sort all categories by sort field
        categories.sort((a, b) => a.sort - b.sort);
        this.updateState({ categories });

        // Select first category by default if available
        if (categories.length > 0) {
          this.onCategorySelected(categories[0]);
        }
      },
      error: (error: any) => {
        console.error('Error loading platforms:', error);
      }
    });
  }

  // Public methods for component interaction
  onCategorySelected(category: IconBaseModel): void {
    this.updateState({ selectedCategory: category });
    
    // Update form
    this.currentState.orderForm.patchValue({
      category: category.id
    });

    // Load services for this category from platforms data
    this.loadServicesForCategory(category.id);
  }

  // Load services for category
  private loadServicesForCategory(categoryId: string): void {
    // Find the selected category in platforms data and get its services
    for (const platform of this.currentState.platforms) {
      const foundCategory = platform.categories.find(c => c.id.toString() === categoryId);
      if (foundCategory) {
        // Map SuperGeneralSvRes to services
        this.updateState({ services: foundCategory.services });
        console.log('Services loaded for category:', categoryId, 'Services count:', foundCategory.services.length);

        // If services are available, select the first one by default
        if (foundCategory.services.length > 0) {
          this.onServiceSelected(foundCategory.services[0]);
        }
        break;
      }
    }
  }

  onServiceSelected(service: SuperGeneralSvRes): void {
    this.updateState({ 
      selectedServiceObj: service,
      selectedService: service.name 
    });

    // Update form
    this.currentState.orderForm.patchValue({
      service: service.id
    });

    // Update quantity validators
    const quantityControl = this.currentState.orderForm.get('quantity');
    if (quantityControl) {
      quantityControl.setValidators([
        Validators.required,
        Validators.min(service.min || 10),
        Validators.max(service.max || 5000000)
      ]);
      quantityControl.updateValueAndValidity();
    }

    this.calculatePrice();
  }

  decreaseQuantity(): void {
    const currentQuantity = this.currentState.quantity;
    if (currentQuantity > 1) {
      const newQuantity = currentQuantity - 1;
      this.updateState({ quantity: newQuantity });
      this.currentState.orderForm.patchValue({ quantity: newQuantity });
    }
  }

  increaseQuantity(): void {
    const currentQuantity = this.currentState.quantity;
    const newQuantity = currentQuantity + 1;
    this.updateState({ quantity: newQuantity });
    this.currentState.orderForm.patchValue({ quantity: newQuantity });
  }

  // Calculate price
  calculatePrice(): void {
    const service = this.currentState.selectedServiceObj;
    const form = this.currentState.orderForm;
    
    if (service && form.get('quantity')?.valid) {
      const quantity = form.get('quantity')?.value || 0;
      const linkCount = this.getLinkCount();
      
      // Calculate price: service price * quantity * number of links
      const newPrice = service.price / 1000 * quantity * linkCount;
      this.updateState({ price: newPrice });
    } else if (service) {
      const newPrice = service.price / 1000;
      this.updateState({ price: newPrice });
    }
  }

  // Get link count from content
  private getLinkCount(): number {
    const content = this.currentState.content;
    if (!content || content.trim() === '') {
      return 1;
    }

    const links = content.split('\n').filter(link => link.trim() !== '');
    return Math.max(1, links.length);
  }

  // Create order (Simple mode)
  createSimpleOrder(): void {
    if (!this.currentState.orderForm.valid) {
      this.toastService.showError('Please fill all required fields');
      return;
    }

    // Reset previous results
    this.updateState({
      isProcessing: true,
      successCount: 0,
      failedCount: 0,
      orderResults: [],
      showResults: false
    });

    // Get form values
    const formValues = this.currentState.orderForm.value;
    const content = formValues.content.trim();
    const quantity = formValues.quantity;

    // Split content by new lines to get individual links
    const links: string[] = content.split('\n').filter((link: string) => link.trim() !== '');

    if (links.length === 0) {
      this.toastService.showError('No valid links found. Please enter at least one link.');
      this.updateState({ isProcessing: false });
      return;
    }

    console.log(`Processing ${links.length} orders`);

    // Create an array of observables for each order
    const orderObservables: Array<Observable<OrderRes | null>> = links.map((link: string) => {
      const orderData: CreateOrderReq = {
        service_id: this.currentState.selectedServiceObj?.id || 0,
        link: link.trim(),
        quantity: quantity
      };

      return this.orderService.createOrder(orderData).pipe(
        catchError((error: any) => {
          // Add to failed results
          const currentState = this.currentState;
          const newFailedCount = currentState.failedCount + 1;
          const newOrderResults = [...currentState.orderResults, {
            link: link.trim(),
            success: false,
            message: error?.message || error.message || 'Unknown error'
          }];

          this.updateState({
            failedCount: newFailedCount,
            orderResults: newOrderResults
          });

          return of(null);
        })
      );
    });

    // Process all orders in parallel
    forkJoin<(OrderRes | null)[]>(orderObservables).subscribe({
      next: (results: (OrderRes | null)[]) => {
        let successCount = 0;
        const orderResults = [...this.currentState.orderResults];

        results.forEach((result: OrderRes | null, index: number) => {
          if (result) {
            successCount++;
            orderResults.push({
              link: links[index],
              success: true,
              message: 'Order created successfully'
            });
          }
        });

        this.updateState({
          successCount,
          orderResults,
          isProcessing: false,
          showResults: true
        });

        // Show summary toast
        this.toastService.showSuccess(`Orders completed: ${successCount} successful, ${this.currentState.failedCount} failed`);
      },
      error: (error: any) => {
        console.error('Error processing orders:', error);
        this.updateState({ isProcessing: false });
        this.toastService.showError('Error processing orders');
      }
    });
  }

  // Create order (Classic mode)
  createClassicOrder(content: string): void {
    if (!content || content.trim() === '') {
      this.toastService.showError('Please enter content');
      return;
    }

    // Reset previous results
    this.updateState({
      isProcessing: true,
      successCount: 0,
      failedCount: 0,
      orderResults: [],
      showResults: false
    });

    // Split content by new lines to get individual orders
    const lines: string[] = content.split('\n').filter(line => line.trim() !== '');

    if (lines.length === 0) {
      this.toastService.showError('No valid orders found. Please enter at least one order.');
      this.updateState({ isProcessing: false });
      return;
    }

    console.log(`Processing ${lines.length} orders`);

    // Parse each line and create an array of valid order requests
    const orderObservables: Array<Observable<OrderRes | null>> = [];
    let failedCount = 0;
    const orderResults: Array<{ link: string; success: boolean; message: string }> = [];

    lines.forEach((line: string) => {
      // Parse line in format: service_id | link | quantity
      const parts = line.split('|').map(part => part.trim());

      if (parts.length !== 3) {
        failedCount++;
        orderResults.push({
          link: line,
          success: false,
          message: 'Invalid format. Expected: service_id | link | quantity'
        });
        return;
      }

      const serviceId = parseInt(parts[0]);
      const link = parts[1];
      const quantity = parseInt(parts[2]);

      // Validate each part
      if (isNaN(serviceId) || serviceId <= 0) {
        failedCount++;
        orderResults.push({
          link: line,
          success: false,
          message: 'Invalid service ID. Must be a positive number.'
        });
        return;
      }

      if (!this.isValidUrl(link)) {
        failedCount++;
        orderResults.push({
          link: line,
          success: false,
          message: 'Invalid URL format.'
        });
        return;
      }

      if (isNaN(quantity) || quantity <= 0) {
        failedCount++;
        orderResults.push({
          link: line,
          success: false,
          message: 'Invalid quantity. Must be a positive number.'
        });
        return;
      }

      // Create order data
      const orderData: CreateOrderReq = {
        service_id: serviceId,
        link: link,
        quantity: quantity
      };

      // Add to observables array
      orderObservables.push(
        this.orderService.createOrder(orderData).pipe(
          catchError((error: any) => {
            failedCount++;
            orderResults.push({
              link: line,
              success: false,
              message: error?.message || error.message || 'Unknown error'
            });
            return of(null);
          })
        )
      );
    });

    // Update state with initial failed count
    this.updateState({
      failedCount,
      orderResults: [...orderResults]
    });

    if (orderObservables.length === 0) {
      this.updateState({
        isProcessing: false,
        showResults: true
      });
      this.toastService.showError('No valid orders to process');
      return;
    }

    // Process all valid orders in parallel
    forkJoin<(OrderRes | null)[]>(orderObservables).subscribe({
      next: (results: (OrderRes | null)[]) => {
        let successCount = 0;
        const currentOrderResults = [...this.currentState.orderResults];

        results.forEach((result: OrderRes | null, index: number) => {
          if (result) {
            successCount++;
            // Find the corresponding valid line for this result
            const validLines = lines.filter(line => {
              const parts = line.split('|').map(part => part.trim());
              return parts.length === 3 &&
                     !isNaN(parseInt(parts[0])) &&
                     this.isValidUrl(parts[1]) &&
                     !isNaN(parseInt(parts[2]));
            });

            if (index < validLines.length) {
              currentOrderResults.push({
                link: validLines[index],
                success: true,
                message: 'Order created successfully'
              });
            }
          }
        });

        this.updateState({
          successCount,
          orderResults: currentOrderResults,
          isProcessing: false,
          showResults: true
        });

        // Show summary toast
        this.toastService.showSuccess(`Orders completed: ${successCount} successful, ${this.currentState.failedCount} failed`);
      },
      error: (error: any) => {
        console.error('Error processing orders:', error);
        this.updateState({ isProcessing: false });
        this.toastService.showError('Error processing orders');
      }
    });
  }

  // Validate URL
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  // Reset form
  resetForm(): void {
    const newForm = this.createForm();
    this.updateState({
      orderForm: newForm,
      content: '',
      quantity: 10,
      price: 0,
      selectedService: 'Test 123',
      selectedCategory: undefined,
      selectedServiceObj: undefined,
      showResults: false,
      orderResults: [],
      successCount: 0,
      failedCount: 0
    });
    this.setupFormSubscriptions();
  }

  // Cleanup
  destroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.subscriptions = [];
  }
}
