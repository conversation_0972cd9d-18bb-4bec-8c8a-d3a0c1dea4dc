.page-container {
  @apply w-full max-w-7xl mx-auto py-2;
}

.content-container {
  @apply bg-white rounded-lg shadow-sm !p-0;
}

.page-header {
  @apply flex justify-between items-center mb-4 pb-3 border-b border-gray-200;
}

.page-title {
  @apply text-2xl font-semibold text-[var(--gray-800)];
}

.back-button {
  @apply p-2 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors;
}

/* Ensure the currency settings component takes full width */
app-currency-settings {
  @apply block w-full;
}
