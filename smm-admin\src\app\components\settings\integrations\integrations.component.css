/* Main Container */
.integrations-settings {
  @apply bg-white rounded-lg  md:p-6 max-w-6xl mx-auto;
}

/* Header Styles */
.settings-header {
  @apply mb-4 sm:mb-6 md:mb-8;
}

.settings-title {
  @apply text-xl sm:text-2xl font-bold text-gray-800;
}

.settings-description {
  @apply text-gray-500 mt-1 text-sm sm:text-base;
}

/* Card Styles */
.settings-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 mb-4 sm:mb-6 overflow-hidden;
}

.card-header {
  @apply bg-gray-50 px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200 flex items-center justify-between;
}

.card-title {
  @apply text-base sm:text-lg font-medium text-gray-800 flex items-center;
}

.section-icon {
  @apply text-gray-500 mr-2 sm:mr-3;
}

.card-content {
  @apply p-3 sm:p-4 md:p-6;
}

/* Integration Item */
.integrations-list {
  @apply space-y-4 sm:space-y-6;
}

.integration-item {
  @apply flex flex-col sm:flex-row items-start sm:items-center justify-between p-3 sm:p-4 border border-gray-200 rounded-lg hover:shadow-sm transition-shadow;
}

.integration-info {
  @apply flex items-center w-full sm:w-auto mb-3 sm:mb-0;
}

.integration-icon {
  @apply w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center mr-3 sm:mr-4 flex-shrink-0;
  background-color: #f0f7ff;
}

.integration-icon .icon {
  @apply text-blue-500 text-xl;
}

.custom-icon-image {
  @apply w-6 h-6 object-contain;
}

.integration-details {
  @apply flex flex-col min-w-0 flex-1 sm:flex-auto overflow-hidden;
}

.integration-name {
  @apply text-base sm:text-lg font-medium text-gray-800 truncate;
}

.integration-status {
  @apply text-sm text-gray-500 mt-1 flex items-center flex-wrap overflow-hidden;
}

.status-badge {
  @apply px-2 py-0.5 rounded-full text-xs font-medium;
}

.status-badge.connected {
  @apply bg-green-100 text-green-800;
}

.status-badge.disconnected {
  @apply bg-gray-100 text-gray-800;
}

/* Integration Actions */
.integration-actions {
  @apply flex gap-2 w-full sm:w-auto justify-end mt-3 sm:mt-0;
  min-width: 160px;
}

/* Connect Button */
.connect-button {
  @apply px-3 sm:px-4 py-1.5 sm:py-2 rounded-md text-sm font-medium transition-colors flex-1 sm:flex-auto text-center;
  @apply bg-blue-500 text-white hover:bg-blue-600;
}

/* Disconnect Button */
.disconnect-button {
  @apply bg-red-500 hover:bg-red-600;
}

/* Edit Button */
.edit-button {
  @apply px-3 sm:px-4 py-1.5 sm:py-2 rounded-md text-sm font-medium transition-colors flex-1 sm:flex-auto text-center;
  @apply bg-gray-200 text-gray-700 hover:bg-gray-300;
}

/* No Integrations Message */
.no-integrations {
  @apply py-8 text-center text-gray-500;
}

/* Add Custom Integration Button */
.add-custom-btn {
  @apply px-3 sm:px-4 py-1.5 sm:py-2 rounded-md text-sm font-medium transition-colors;
  @apply bg-blue-500 text-white hover:bg-blue-600 flex items-center;
}

.add-custom-btn .mr-2 {
  margin-right: 0.5rem;
}

/* Loading Spinner */
.spinner {
  @apply w-8 h-8 border-4 border-blue-200 rounded-full animate-spin;
  border-top-color: #3b82f6;
}
