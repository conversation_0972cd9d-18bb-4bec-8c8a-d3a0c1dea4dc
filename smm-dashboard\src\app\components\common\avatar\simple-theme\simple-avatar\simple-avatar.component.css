/* Simple Avatar Theme Styles */
.simple-avatar-container {
  position: relative;
  display: inline-block;
}

/* Avatar Button */
.avatar-button {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.avatar-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.avatar-image {
  position: relative;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.3);
  object-fit: cover;
}

.user-details {
  display: flex;
  flex-direction: column;
  color: white;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.2;
}

.user-role {
  font-size: 12px;
  opacity: 0.8;
  line-height: 1.2;
}

.dropdown-icon {
  width: 20px;
  height: 20px;
  color: rgba(255, 255, 255, 0.8);
  transition: transform 0.3s ease;
}

.avatar-button:hover .dropdown-icon {
  transform: rotate(180deg);
}

/* Dropdown Menu */
.avatar-menu {
  position: absolute;
  top: calc(100% + 12px);
  right: 0;
  width: 320px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.05);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 9999;
  overflow: hidden;
}

.avatar-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.menu-content {
  padding: 24px;
}

/* Profile Section */
.profile-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.profile-avatar {
  position: relative;
}

.profile-image {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 3px solid #f0f0f0;
  object-fit: cover;
}

.profile-info {
  flex: 1;
}

.profile-name {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.profile-role {
  font-size: 13px;
  color: #666;
}

/* Menu Divider */
.menu-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, #e0e0e0, transparent);
  margin: 16px 0;
}

/* Menu Items */
.menu-items {
  margin-bottom: 16px;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #333;
}

.menu-item:hover {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  transform: translateX(4px);
}

.menu-icon {
  width: 20px;
  height: 20px;
  color: #667eea;
  flex-shrink: 0;
}

.menu-text {
  font-size: 14px;
  font-weight: 500;
}

/* Balance Section */
.balance-section {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
}

.balance-item {
  flex: 1;
  text-align: center;
  padding: 16px 12px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.balance-value {
  font-size: 16px;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 4px;
}

.balance-label {
  font-size: 11px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Logout Section */
.logout-section {
  margin-top: 16px;
}

.logout-item {
  color: #e53e3e;
  background: rgba(229, 62, 62, 0.05);
  border: 1px solid rgba(229, 62, 62, 0.1);
}

.logout-item:hover {
  background: rgba(229, 62, 62, 0.1);
  transform: translateX(4px);
}

.logout-icon {
  color: #e53e3e;
}

/* Overlay */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 9998;
  backdrop-filter: blur(2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .user-details {
    display: none;
  }
  
  .avatar-button {
    padding: 8px;
    border-radius: 50%;
  }
  
  .avatar-menu {
    width: 280px;
    right: -20px;
  }
  
  .balance-section {
    flex-direction: column;
    gap: 12px;
  }
}
