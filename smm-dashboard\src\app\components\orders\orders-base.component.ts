import { Component, <PERSON>Ini<PERSON>, <PERSON><PERSON><PERSON><PERSON>, inject } from '@angular/core';
import { Clipboard } from '@angular/cdk/clipboard';
import { OrdersLogicService } from './orders.service';
import { OrderRes } from '../../model/response/order-res.model';
import { SuperGeneralSvRes } from '../../model/response/super-general-sv.model';
import { IconBaseModel } from '../../model/base-model';
import { StatusFilter } from '../../shared/constants/status-filters';

@Component({
  template: ''
})
export abstract class OrdersBaseComponent implements OnInit, OnDestroy {
  protected ordersLogic = inject(OrdersLogicService);

  ngOnInit(): void {
    this.ordersLogic.loadPlatforms();
    this.ordersLogic.loadOrders();
    this.ordersLogic.detectMobileDevice();

    // Listen for window resize events to update view mode
    window.addEventListener('resize', () => {
      this.ordersLogic.detectMobileDevice();
    });
  }

  ngOnDestroy() {
    this.ordersLogic.ngOnDestroy();

    // Remove resize event listener
    window.removeEventListener('resize', () => {
      this.ordersLogic.detectMobileDevice();
    });
  }

  // Expose service properties
  get searchTerm(): string { return this.ordersLogic.searchTerm; }
  set searchTerm(value: string) { this.ordersLogic.searchTerm = value; }

  get selectedService(): string { return this.ordersLogic.selectedService; }
  set selectedService(value: string) { this.ordersLogic.selectedService = value; }

  get selectedServiceId(): number | null { return this.ordersLogic.selectedServiceId; }
  set selectedServiceId(value: number | null) { this.ordersLogic.selectedServiceId = value; }

  get dateRange(): { startDate: Date | null, endDate: Date | null } { return this.ordersLogic.dateRange; }
  set dateRange(value: { startDate: Date | null, endDate: Date | null }) { this.ordersLogic.dateRange = value; }

  get showFilters(): boolean { return this.ordersLogic.showFilters; }
  set showFilters(value: boolean) { this.ordersLogic.showFilters = value; }

  get viewMode(): 'table' | 'card' { return this.ordersLogic.viewMode; }
  set viewMode(value: 'table' | 'card') { this.ordersLogic.viewMode = value; }

  get statusFilters(): StatusFilter[] { return this.ordersLogic.statusFilters; }
  get services(): SuperGeneralSvRes[] { return this.ordersLogic.services; }
  get categories(): IconBaseModel[] { return this.ordersLogic.categories; }
  get allServices(): SuperGeneralSvRes[] { return this.ordersLogic.allServices; }
  get selectAll(): boolean { return this.ordersLogic.selectAll; }
  set selectAll(value: boolean) { this.ordersLogic.selectAll = value; }

  get orders(): OrderRes[] { return this.ordersLogic.orders; }
  get selectedOrders(): number[] { return this.ordersLogic.selectedOrders; }

  get showBulkRefillConfirmation(): boolean { return this.ordersLogic.showBulkRefillConfirmation; }
  get isBulkRefilling(): boolean { return this.ordersLogic.isBulkRefilling; }

  get pagination() { return this.ordersLogic.pagination; }
  get isLoading(): boolean { return this.ordersLogic.isLoading; }

  // Expose service methods
  formatPrice(price: number): string {
    return this.ordersLogic.formatPrice(price);
  }

  loadOrders(page: number = 0): void {
    this.ordersLogic.loadOrders(page);
  }

  onCategorySelected(category: IconBaseModel): void {
    this.ordersLogic.onCategorySelected(category);
  }

  toggleFilter(filter: any): void {
    this.ordersLogic.toggleFilter(filter);
  }

  onServiceSelected(service: SuperGeneralSvRes): void {
    this.ordersLogic.onServiceSelected(service);
  }

  toggleAllOrders(): void {
    this.ordersLogic.toggleAllOrders();
  }

  toggleOrderSelection(orderId: number): void {
    this.ordersLogic.toggleOrderSelection(orderId);
  }

  isOrderSelected(orderId: number): boolean {
    return this.ordersLogic.isOrderSelected(orderId);
  }

  applyFilters(): void {
    this.ordersLogic.applyFilters();
  }

  onDateRangeChanged(dateRange: { startDate: Date | null, endDate: Date | null }): void {
    this.ordersLogic.onDateRangeChanged(dateRange);
  }

  resetFilters(): void {
    this.ordersLogic.resetFilters();
  }

  onSearch(searchTerm: string): void {
    this.ordersLogic.onSearch(searchTerm);
  }

  toggleFilters(): void {
    this.ordersLogic.toggleFilters();
  }

  getStatusClass(status: string): string {
    return this.ordersLogic.getStatusClass(status);
  }

  goToPage(page: number): void {
    this.ordersLogic.goToPage(page);
  }

  changePageSize(): void {
    this.ordersLogic.changePageSize();
  }

  getPageRange(): number[] {
    return this.ordersLogic.getPageRange();
  }

  // Copy methods
  CopyID(): void {
    this.ordersLogic.copyID();
  }

  formatOrderForCopy(order: OrderRes): string {
    return this.ordersLogic.formatOrderForCopy(order);
  }

  copySingleOrder(order: OrderRes): void {
    this.ordersLogic.copySingleOrder(order);
  }

  copyId(): void {
    this.ordersLogic.copyId();
  }

  reorder(order: OrderRes): void {
    this.ordersLogic.reorder(order);
  }

  isRefillAvailable(order: OrderRes): boolean {
    return this.ordersLogic.isRefillAvailable(order);
  }

  refillOrder(order: OrderRes): void {
    this.ordersLogic.refillOrder(order);
  }

  bulkRefillOrders(): void {
    this.ordersLogic.bulkRefillOrders();
  }

  closeBulkRefillConfirmation(): void {
    this.ordersLogic.closeBulkRefillConfirmation();
  }

  confirmBulkRefill(): void {
    this.ordersLogic.confirmBulkRefill();
  }

  cancelOrder(order: OrderRes): void {
    this.ordersLogic.cancelOrder(order);
  }

  updateNote(order: OrderRes, note: string): void {
    this.ordersLogic.updateNote(order, note);
  }

  onNoteBlur(order: OrderRes, event: Event): void {
    this.ordersLogic.onNoteBlur(order, event);
  }

  isShowCancelButton(order: OrderRes): boolean {
    return this.ordersLogic.isShowCancelButton(order);
  }
}
