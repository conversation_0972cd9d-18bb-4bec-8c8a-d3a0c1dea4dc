<div class="min-h-screen bg-white text-gray-800">
  <!-- Header -->
 <header class="landing-header bg-white shadow-sm">
    <div class="container mx-auto px-4 py-3 flex justify-between items-center">
      <div class="logo-container flex items-center">
        <span class="text-xl font-bold text-blue-600">NEWPANEL</span>
      </div>
      <nav class="hidden md:flex space-x-6">
        <a href="#services" class="text-gray-700 hover:text-blue-600 transition-colors">{{ 'landing.header.services' | translate }}</a>
        <a href="#features" class="text-gray-700 hover:text-blue-600 transition-colors">{{ 'landing.header.features' | translate }}</a>
        <a href="#pricing" class="text-gray-700 hover:text-blue-600 transition-colors">{{ 'landing.header.pricing' | translate }}</a>
      </nav>
      <div class="flex items-center space-x-3">
        <div class="relative mr-2">
          <button (click)="toggleLanguageDropdown()" class="flex items-center px-2 py-1 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-100 transition-colors">
            <span [class]="getCurrentLanguageFlag()" class="w-4 h-3 mr-1 rounded"></span>
            <span class="mr-1">{{ currentLanguage.toUpperCase() }}</span>
            <fa-icon [icon]="['fas', 'chevron-down']" class="text-xs"></fa-icon>
          </button>
          <div *ngIf="showLanguageDropdown" class="absolute right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-md z-50">
            <button
              *ngFor="let lang of languages"
              (click)="changeLanguage(lang.code)"
              class="flex items-center w-full px-4 py-2 text-left hover:bg-gray-100 transition-colors"
              [class.font-bold]="currentLanguage === lang.code"
              [class.bg-blue-50]="currentLanguage === lang.code">
              <span [class]="lang.code === 'en' ? 'fi fi-us' : 'fi fi-vn'" class="w-4 h-3 mr-2 rounded"></span>
              {{ lang.name }}
            </button>
          </div>
        </div>
        <button (click)="navigateToLogin()" class="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-100 transition-colors">
          {{ 'landing.header.login' | translate }}
        </button>
        <button (click)="navigateToRegister()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
          {{ 'landing.header.signup' | translate }}
        </button>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <router-outlet></router-outlet>

  <!-- Footer -->
 <footer class="bg-gray-900 text-white py-12">
    <div class="container mx-auto px-4">
      <div class="flex flex-col md:flex-row justify-between items-center">
        <div class="mb-6 md:mb-0">
          <span class="text-xl font-bold">NEWPANEL</span>
          <p class="mt-2 text-gray-400">{{ 'landing.footer.slogan' | translate }}</p>
        </div>

        <div class="flex space-x-6">
          <a href="#" class="text-gray-400 hover:text-white transition-colors">
            <fa-icon [icon]="['fab', 'facebook']"></fa-icon>
          </a>
          <a href="#" class="text-gray-400 hover:text-white transition-colors">
            <fa-icon [icon]="['fab', 'twitter']"></fa-icon>
          </a>
          <a href="#" class="text-gray-400 hover:text-white transition-colors">
            <fa-icon [icon]="['fab', 'instagram']"></fa-icon>
          </a>
          <a href="#" class="text-gray-400 hover:text-white transition-colors">
            <fa-icon [icon]="['fab', 'telegram-plane']"></fa-icon>
          </a>
        </div>
      </div>

      <hr class="border-gray-800 my-8">

      <div class="flex flex-col md:flex-row justify-between items-center">
        <p class="text-gray-400 mb-4 md:mb-0">{{ 'landing.footer.copyright' | translate }}</p>
        <div class="flex space-x-6">
          <a href="#" class="text-gray-400 hover:text-white transition-colors">{{ 'landing.footer.terms' | translate }}</a>
          <a href="#" class="text-gray-400 hover:text-white transition-colors">{{ 'landing.footer.privacy' | translate }}</a>
          <a href="#" class="text-gray-400 hover:text-white transition-colors">{{ 'landing.footer.contact' | translate }}</a>
        </div>
      </div>
    </div>
  </footer>
</div>