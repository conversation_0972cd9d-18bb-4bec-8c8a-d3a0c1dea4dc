/* Simple Layout Theme - Modern Design */
.simple-layout-container {
  display: flex;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
}

/* Background Elements */
.layout-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  pointer-events: none;
}

.bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.03) 0%, transparent 50%);
  background-size: 800px 800px;
  animation: float-pattern 20s ease-in-out infinite;
}

.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.01) 0%,
    rgba(139, 92, 246, 0.01) 50%,
    rgba(236, 72, 153, 0.01) 100%);
}

@keyframes float-pattern {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(120deg); }
  66% { transform: translate(-20px, 20px) rotate(240deg); }
}

/* Sidebar */
.layout-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 100;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Main Content Area */
.layout-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
  z-index: 1;
  margin-left: 320px;
}

/* Header */
.layout-header {
  position: sticky;
  top: 0;
  z-index: 200;
  backdrop-filter: blur(20px);
}

/* Content */
.layout-content {
  flex: 1;
  position: relative;
  padding: 2rem;
  overflow-y: auto;
  overflow-x: hidden;
}

.content-container {
  position: relative;
  max-width: 100%;
  margin: 0 auto;
  z-index: 2;
}

.page-content {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 24px;
  padding: 2rem;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.15),
    0 10px 10px -5px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.page-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(30, 41, 59, 0.3) 50%,
    transparent 100%);
}

/* Content Decorations */
.content-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg,
    rgba(30, 41, 59, 0.08),
    rgba(15, 23, 42, 0.08));
  filter: blur(1px);
}

.decoration-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  right: 10%;
  animation: float-decoration 15s ease-in-out infinite;
}

.decoration-2 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: 15%;
  animation: float-decoration 18s ease-in-out infinite reverse;
}

.decoration-line {
  position: absolute;
  width: 2px;
  height: 100px;
  background: linear-gradient(180deg,
    transparent 0%,
    rgba(30, 41, 59, 0.15) 50%,
    transparent 100%);
  top: 50%;
  right: 5%;
  transform: translateY(-50%) rotate(15deg);
  animation: pulse-line 3s ease-in-out infinite;
}

@keyframes float-decoration {
  0%, 100% { transform: translate(0, 0) scale(1); }
  50% { transform: translate(20px, -20px) scale(1.1); }
}

@keyframes pulse-line {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.7; }
}

/* Footer */
.layout-footer {
  background: rgba(255, 255, 255, 0.9);
  border-top: 1px solid rgba(226, 232, 240, 0.8);
  padding: 1.5rem 2rem;
  backdrop-filter: blur(20px);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 100%;
  margin: 0 auto;
}

.footer-info {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.footer-text {
  font-size: 0.875rem;
  color: #64748b;
}

.footer-links {
  display: flex;
  gap: 1.5rem;
}

.footer-link {
  font-size: 0.875rem;
  color: #64748b;
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-link:hover {
  color: #3b82f6;
}

.footer-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-dot {
  width: 0.5rem;
  height: 0.5rem;
  background: #10b981;
  border-radius: 50%;
  animation: pulse-status 2s ease-in-out infinite;
}

.status-text {
  font-size: 0.875rem;
  color: #10b981;
  font-weight: 500;
}

@keyframes pulse-status {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Floating Elements */
.floating-elements {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 200;
}

/* Mobile Overlay */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 90;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
}

.mobile-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Responsive Design */
@media (max-width: 768px) {
  .layout-sidebar {
    position: fixed;
    left: -320px;
    transition: left 0.3s ease;
  }

  .layout-sidebar.open {
    left: 0;
  }

  .layout-main {
    margin-left: 0;
  }

  .layout-content {
    padding: 1rem;
  }

  .page-content {
    padding: 1.5rem;
    border-radius: 16px;
  }

  .footer-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .footer-info {
    flex-direction: column;
    gap: 1rem;
  }

  .floating-elements {
    bottom: 1rem;
    right: 1rem;
  }

  .decoration-1,
  .decoration-2 {
    display: none;
  }
}

@media (max-width: 1024px) {
  .layout-content {
    padding: 1.5rem;
  }

  .decoration-1 {
    width: 150px;
    height: 150px;
  }

  .decoration-2 {
    width: 100px;
    height: 100px;
  }
}

/* Theme Classes */

.sidebar-open .layout-main {
  margin-left: 320px;
}

.mobile-layout .layout-sidebar {
  position: fixed;
  top: 0;
  left: -320px;
  z-index: 100;
}

.mobile-layout.sidebar-open .layout-sidebar {
  left: 0;
}

.mobile-layout .layout-main {
  margin-left: 0;
}

.desktop-layout .layout-sidebar {
  position: fixed;
  left: 0;
}

/* Scrollbar Styling for Main Content */
.layout-content::-webkit-scrollbar {
  width: 8px;
}

.layout-content::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
}

.layout-content::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 4px;
}

.layout-content::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}
