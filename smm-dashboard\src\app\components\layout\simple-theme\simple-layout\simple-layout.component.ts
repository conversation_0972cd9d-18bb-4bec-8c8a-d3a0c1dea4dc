import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { Observable } from 'rxjs';

// Services
import { LayoutLogicService, LayoutState } from '../../services/layout-logic.service';

// Components
import { SimpleHeaderComponent } from '../../../header/simple-theme/simple-header/simple-header.component';
import { SimpleSidebarComponent } from '../../../sidebar/simple-theme/simple-sidebar/simple-sidebar.component';
import { FloatingContactButtonsComponent } from '../../../common/floating-contact-buttons/floating-contact-buttons.component';

@Component({
  selector: 'app-simple-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    SimpleHeaderComponent,
    SimpleSidebarComponent,
    FloatingContactButtonsComponent
  ],
  templateUrl: './simple-layout.component.html',
  styleUrls: ['./simple-layout.component.css']
})
export class SimpleLayoutComponent implements OnInit, OnDestroy {
  // Layout logic state
  layoutState$: Observable<LayoutState>;

  constructor(private layoutLogicService: LayoutLogicService) {
    this.layoutState$ = this.layoutLogicService.state$;
  }

  ngOnInit(): void {
    // LayoutLogicService handles all initialization
  }

  ngOnDestroy(): void {
    // LayoutLogicService is singleton, no cleanup needed
  }

  // Delegate methods to LayoutLogicService for template compatibility
  toggleSidebar(): void {
    this.layoutLogicService.toggleSidebar();
  }

  closeSidebar(): void {
    this.layoutLogicService.closeSidebar();
  }

  openSidebar(): void {
    this.layoutLogicService.openSidebar();
  }

  // Helper methods for template
  getLayoutClasses(): string[] {
    return this.layoutLogicService.getLayoutClasses();
  }
}
