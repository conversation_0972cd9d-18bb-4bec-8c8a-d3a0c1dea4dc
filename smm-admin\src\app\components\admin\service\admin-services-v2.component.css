/* Admin container */
.admin-container {
  @apply w-full bg-white rounded-lg md:p-6 p-2;
}

/* Admin header */
.admin-header {
  @apply flex justify-between items-center mb-6;
}

.admin-title {
  @apply text-xl font-bold uppercase;
}

/* Search and filter container */
.search-filter-container {
  @apply mb-6;
}

/* Search input */
.search-input-wrapper {
  @apply relative flex items-center w-full;
}

.search-input {
  @apply w-full h-[52px] px-4 py-2 bg-[#f5f7fc] rounded-lg border-none focus:outline-none focus:ring-2 focus:ring-[var(--primary)];
}

.search-button {
  @apply absolute right-2 bg-[var(--primary)] text-white p-2 rounded-md;
}

/* Category container */
.category-container {
  @apply mb-8 bg-white rounded-lg overflow-hidden;
}

/* Category header */
.category-header-row {
  @apply bg-slate-100 border-b border-gray-200 text-gray-800 cursor-grab;
  transition: all 300ms ease;
}

/* Remove hover effect to prevent color changes */
.category-header-row:hover {
  @apply bg-slate-100 text-gray-800;
}

/* Make entire row draggable */
.category-header-row.cdk-drag {
  @apply cursor-grab;
}

.category-header-row.cdk-drag:active {
  @apply cursor-grabbing;
}

/* Move mode styling */
.category-header-row.move-mode {
  cursor: grab !important;
  border-left: 4px solid #f97316 !important;
  background-color: #fff7ed !important;
}

.category-header-row.move-mode:hover {
  background-color: #fed7aa !important;
}

.category-header-row:not(.move-mode) {
  cursor: default !important;
}

/* Disable drag when not in move mode */
.category-header-row:not(.move-mode).cdk-drag-disabled {
  cursor: default !important;
  opacity: 1 !important;
}

.category-header-row.drag-over {
  background-color: #f1f5f9;
  border-color: #d1d5db;
  color: #1f2937;
}

/* Category with empty services gets subtle lift when receiving drag */
.category-header-row + .empty-category.cdk-drop-list-receiving {
  margin-top: -4px;
}



/* Category drag handle */
.category-drag-handle {
  @apply cursor-grab;
}

.category-drag-handle:active {
  @apply cursor-grabbing;
}

/* Ensure interactive elements don't interfere with drag */
.category-header-row input[type="checkbox"],
.category-header-row button,
.category-header-row .menu-container {
  pointer-events: auto;
}

/* Visual feedback for draggable row */
.category-header-row td {
  @apply select-none;
}

/* Category visibility toggle */
.category-visibility-toggle {
  @apply flex items-center;
}

.toggle-arrow-btn {
  @apply p-1 rounded hover:bg-gray-200 transition-colors duration-200;
}

/* Platform dropdown */
.platform-dropdown-content {
  @apply absolute top-full right-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50;
}

/* Service rows */
.service-row {
  @apply border-b border-gray-100 hover:bg-gray-50 transition-colors duration-200 cursor-grab;
}

.service-row.deactivated {
  @apply opacity-50 bg-gray-100;
}

.service-row.cdk-drag-preview {
  @apply shadow-lg border border-gray-300 rounded-lg bg-white;
}

/* Make entire service row draggable */
.service-row.cdk-drag {
  @apply cursor-grab;
}

.service-row.cdk-drag:active {
  @apply cursor-grabbing;
}

/* Service drag handle */
.service-drag-handle {
  @apply cursor-grab text-gray-400 hover:text-gray-600;
}

.service-drag-handle:active {
  @apply cursor-grabbing;
}

/* Ensure interactive elements in service rows don't interfere with drag */
.service-row input[type="checkbox"],
.service-row button,
.service-row a,
.service-row .menu-container,
.service-row .copy-icon {
  pointer-events: auto;
}

/* Visual feedback for draggable service row */
.service-row td {
  @apply select-none;
}

/* API info column */
.api-info-column {
  max-width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.api-info-column .api-id-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 4px;
}

.api-info-column .api-id {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}

.api-info-column .copy-icon {
  cursor: pointer;
  color: #6b7280;
  transition: color 0.2s;
  font-size: 14px;
}

.api-info-column .copy-icon:hover {
  color: #3b82f6;
}

.api-info-column .api-url-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 4px;
}

.api-info-column .api-url {
  font-size: 14px;
  color: #3b82f6;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  max-width: calc(100% - 24px);
}

.api-info-column .api-url:hover {
  text-decoration: underline;
}

.copy-icon {
  @apply text-gray-400 hover:text-gray-600 cursor-pointer text-xs;
}

/* Refill badge */
.lifetime-refill-badge {
  @apply inline-block px-2 py-1 text-xs font-medium rounded-full;
  @apply bg-green-100 text-green-800;
}

/* Form elements */
.form-checkbox {
  @apply h-4 w-4 text-[var(--primary)] focus:ring-[var(--primary)] border-gray-300 rounded;
}

/* Hide checkbox only during drag operations */
/* .cdk-drag-preview .form-checkbox,
.cdk-drag-dragging .form-checkbox {
  display: none !important;
} */

/* Table styles */
.admin-services-table {
  @apply min-w-full divide-y divide-gray-200;
}

.admin-services-table th {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500  tracking-wider;
}

.admin-services-table td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

/* Drag and drop styles */
.cdk-drop-list {
  @apply block;
}

.cdk-drag {
  @apply transition-transform duration-200;
}

.cdk-drag-preview {
  @apply shadow-lg rounded-lg;
}

.cdk-drag-placeholder {
  @apply opacity-0;
  height: 0 !important;
  overflow: hidden;
  border: none !important;
}

.cdk-drag-placeholder td {
  height: 0 !important;
  padding: 0 !important;
  border: none !important;
}

.cdk-drag-animating {
  @apply transition-transform duration-300 ease-in-out;
}

/* Category drag and drop styles */
.category-drop-list {
  display: table-row-group;
}

.category-header-row.cdk-drag {
  @apply transition-all duration-200;
}

.category-header-row.cdk-drag-preview {
  @apply shadow-lg border border-gray-300 rounded-lg ;

  /* width: var(--category-width) !important;
  max-width: var(--category-width) !important;
  box-sizing: border-box; */
}

.category-header-row.cdk-drag-placeholder {
  opacity: 0;
  height: 60px !important;
  overflow: hidden;
  border: none !important;
  background-color: transparent !important;
  transition: all 250ms cubic-bezier(0, 0, 0.2, 1);
}

.category-header-row.cdk-drag-dragging {
  @apply opacity-60;
}

.category-header-row.drag-over {
  @apply  border-blue-200;
}

/* When dragging categories, make other items shift smoothly like services */
.category-drop-list.cdk-drop-list-dragging .category-header-row:not(.cdk-drag-placeholder):not(.cdk-drag-dragging) {
  transition: transform 250ms cubic-bezier(0.25, 0.8, 0.25, 1) !important;
}

/* Enhanced category drag placeholder */
.category-header-row.cdk-drag-placeholder {
  opacity: 0;
  height: 0 !important;
  overflow: hidden;
  border: none !important;
  transition: all 250ms cubic-bezier(0, 0, 0.2, 1);
}

/* Category drop zone visual feedback */
.category-drop-list.cdk-drop-list-receiving {
  background-color: rgba(249, 115, 22, 0.05);
  border-radius: 8px;
}

/* Category expand toggle in move mode */
.category-expand-toggle {
  display: flex;
  align-items: center;
}

.expand-btn {
  padding: 0.25rem;
  border-radius: 0.375rem;
  transition: all 200ms ease;
  background: transparent;
  border: none;
  cursor: pointer;
}

.expand-btn:hover {
  background-color: #f3f4f6;
}

.expand-btn:active {
  background-color: #e5e7eb;
  transform: scale(0.95);
}

/* Visual hint that this button exits move mode */
.expand-btn:hover fa-icon {
  color: #059669 !important;
}

/* Category Move Mode Styles */
.category-move-drop-list {
  display: table-row-group;
}

/* Category rows in move mode - ensure proper table layout for animations */
.category-header-row.move-mode {
  transition: transform 250ms cubic-bezier(0.25, 0.8, 0.25, 1);
  cursor: grab !important;
  border-left: 4px solid #f97316 !important;
  background-color: #fff7ed !important;
}

.category-header-row.move-mode:hover {
  background-color: #fed7aa !important;
}

.category-header-row.move-mode.cdk-drag {
  transition: all 200ms ease;
}

.category-header-row.move-mode.cdk-drag-preview {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-radius: 0.5rem;
  background: white;
}

.category-header-row.move-mode.cdk-drag-placeholder {
  opacity: 0;
  height: 60px !important;
  overflow: hidden;
  border: none !important;
  background-color: transparent !important;
  transition: all 250ms cubic-bezier(0, 0, 0.2, 1);
}

.category-header-row.move-mode.cdk-drag-dragging {
  opacity: 0.6;
  transform: scale(0.98);
}

/* Critical: Animation for items moving out of the way */
.category-move-drop-list.cdk-drop-list-dragging .category-header-row.move-mode:not(.cdk-drag-placeholder):not(.cdk-drag-dragging) {
  transition: transform 250ms cubic-bezier(0.25, 0.8, 0.25, 1) !important;
}

/* Category move mode drop zone feedback */
.category-move-drop-list.cdk-drop-list-receiving {
  background-color: rgba(249, 115, 22, 0.05);
  border-radius: 8px;
}

/* Ensure smooth reordering animation */
.category-move-drop-list .category-header-row.move-mode {
  transform-origin: center;
}

/* Enhanced placeholder for better visual feedback */
.category-header-row.move-mode.cdk-drag-placeholder {
  /* Remove text content, just show visual placeholder */
}

/* Category drag preview styles */
.category-move-drag-preview,
.category-main-drag-preview {
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  padding: 12px 16px;
  width: 100%;
  min-width: 800px;
  max-width: 1200px;
  z-index: 1000;
}

.category-preview-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  white-space: nowrap;
}

/* Move mode preview - orange accent */
.category-move-drag-preview {
  border-left: 4px solid #f97316;
  background: linear-gradient(135deg, #fff7ed 0%, #ffffff 100%);
}

/* Main table preview - blue accent */
.category-main-drag-preview {
  border-left: 4px solid #3b82f6;
  background: linear-gradient(135deg, #eff6ff 0%, #ffffff 100%);
}

/* Ensure icons and text are properly sized in preview */
.category-preview-content fa-icon {
  font-size: 14px;
}

.category-preview-content app-social-icon {
  width: 20px;
  height: 20px;
}

.category-preview-content span {
  font-size: 14px;
}

/* Service drag and drop styles */
.service-row.cdk-drag {
  transition: all 200ms ease;
}

.service-row.cdk-drag-preview {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  width: var(--service-width) !important;
  max-width: var(--service-width) !important;
  box-sizing: border-box;
}

.service-row.cdk-drag-dragging {
  opacity: 0.6;
  transform: scale(0.98);
}



/* When dragging services, make other items shift smoothly */
.category-services.cdk-drop-list-dragging .service-row:not(.cdk-drag-placeholder) {
  transition: transform 300ms ease-in-out;
}

/* Drop zone visual feedback */
.category-services.cdk-drop-list-receiving {
  background-color: rgba(59, 130, 246, 0.05);
  border-radius: 8px;
}

.category-services.cdk-drop-list-receiving::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: none;
  border-radius: 8px;
  pointer-events: none;
}

/* Service preview table */
.service-preview-table {
  @apply w-full border-collapse;
}

.service-row-preview {
  @apply bg-white shadow-lg rounded-lg;
}

.service-row-preview td {
  @apply px-4 py-2 text-sm;
}

/* Category preview table */
.category-preview-table {
  @apply w-full border-collapse;
}

.category-header-row-preview {
  @apply bg-slate-100 shadow-lg rounded-lg text-gray-800;
}

.category-header-row-preview td {
  @apply px-4 py-2;
}

/* Menu container */
.menu-container {
  @apply relative inline-block;
}

/* Loading states */
.loading-overlay {
  @apply absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10;
}

.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)];
}

/* Mobile responsive */
@media (max-width: 768px) {
  .admin-container {
    @apply p-2;
  }

  .admin-services-table {
    @apply text-xs;
  }

  .admin-services-table th,
  .admin-services-table td {
    @apply px-2 py-2;
  }
}

/* Card view styles */
.service-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4 transition-all duration-200;
}

.service-card:hover {
  @apply shadow-md;
}

.service-card.selected {
  @apply border-[var(--primary)] bg-blue-50;
}

.service-card.deactivated {
  @apply opacity-50 bg-gray-100;
}

/* Category card styles for mobile */
.category-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-3;
}

.category-card:hover {
  @apply shadow-md;
}

/* Mobile card spacing */
.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}

/* Improve mobile touch targets */
@media (max-width: 768px) {
  .service-card {
    @apply p-3;
  }

  .form-checkbox {
    @apply w-5 h-5;
  }

  /* Ensure proper spacing for mobile cards */
  .ml-4 {
    margin-left: 1rem;
  }

  /* Better text sizing for mobile */
  .text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  /* Improve button touch targets */
  button {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Bulk action styles */
.bulk-action-header {
  @apply bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4;
}

.bulk-action-count {
  @apply font-medium text-blue-800;
}

/* Animation classes */
.fade-in {
  animation: fade-in 0.3s ease-in-out;
}

.slide-up {
  animation: slide-up 0.3s ease-in-out;
}


.category-services {
  display: table-row-group;
}

/* Force table layout and override all existing styles */
.admin-services-table {
  table-layout: fixed !important;
  width: 100% !important;
  border-collapse: collapse !important;
}

/* Override any existing th/td styles and force exact widths */
.admin-services-table th,
.admin-services-table td {
  padding: 0.75rem 1.5rem !important;
  box-sizing: border-box !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* Force exact column widths for both th and td */
.admin-services-table th:nth-child(1),
.admin-services-table tbody tr td:nth-child(1) {
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
  text-overflow: clip !important;
  overflow: visible !important;
}

.admin-services-table th:nth-child(2),
.admin-services-table tbody tr td:nth-child(2) {
  width: 55% !important;
}

.admin-services-table th:nth-child(3),
.admin-services-table tbody tr td:nth-child(3) {
  width: 15% !important;
}

.admin-services-table th:nth-child(4),
.admin-services-table tbody tr td:nth-child(4) {
  width: 5% !important;
}

.admin-services-table th:nth-child(5),
.admin-services-table tbody tr td:nth-child(5) {
  width: 5% !important;
}

.admin-services-table th:nth-child(6),
.admin-services-table tbody tr td:nth-child(6) {
  width: 7% !important;
}

.admin-services-table th:nth-child(7),
.admin-services-table tbody tr td:nth-child(7) {
  width: 8% !important;
}

.admin-services-table th:nth-child(8),
.admin-services-table tbody tr td:nth-child(8) {
  width: 5% !important;
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slide-up {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Status indicators */
.status-active {
  @apply text-green-600;
}

.status-inactive {
  @apply text-red-600;
}

.status-pending {
  @apply text-yellow-600;
}

/* Button variants */
.btn-primary {
  @apply bg-[var(--primary)] text-white font-medium px-4 py-2 rounded-lg hover:bg-[var(--primary-hover)] transition-colors duration-200;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-700 font-medium px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors duration-200;
}

.btn-danger {
  @apply bg-red-600 text-white font-medium px-4 py-2 rounded-lg hover:bg-red-700 transition-colors duration-200;
}

/* Utility classes */
.text-truncate {
  @apply truncate;
}

.text-wrap {
  @apply break-words;
}

.cursor-grab {
  cursor: grab;
}

.cursor-grabbing {
  cursor: grabbing;
}

/* Enhanced drag and drop visual feedback */
.cdk-drag-dragging {
  opacity: 0.6 !important;
  transform: scale(0.98) !important;
  z-index: 1000;
}

/* Hover effects for drop zones */
.category-services:hover {
  background-color: rgba(59, 130, 246, 0.02);
  transition: background-color 200ms ease;
}

/* Drop zone active state */
.cdk-drop-list-receiving {
  background-color: transparent !important;
  border: none !important;
  border-radius: 8px !important;
}

/* Smooth transitions for all draggable elements */
.cdk-drag:not(.cdk-drag-dragging) {
  transition: transform 250ms cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* Enhanced placeholder styling - Only for services */
.service-row.cdk-drag-placeholder {
  background: transparent;
  border: none;
  border-radius: 4px;
  opacity: 0 !important;
  height: 60px !important;
  display: table-row;
}

.service-row.cdk-drag-placeholder td {
  height: 60px !important;
  padding: 0 !important;
  border: none !important;
}

/* Remove text content from placeholder */

/* Animation for items moving out of the way */
.cdk-drop-list-dragging .cdk-drag:not(.cdk-drag-placeholder):not(.cdk-drag-dragging) {
  transition: transform 300ms cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* Category specific drag styling */
.category-header-row.cdk-drag:hover {
  background-color: #f1f5f9;
  color: #1f2937;
  cursor: grab;
}

.category-header-row.cdk-drag:active {
  cursor: grabbing;
}

/* Service specific drag styling */
.service-row.cdk-drag:hover {
  background-color: #f9fafb;
  cursor: grab;
}

.service-row.cdk-drag:active {
  cursor: grabbing;
}

/* Empty category styling */
.empty-category {
  position: relative;
  transition: all 300ms ease;
}

.empty-drop-zone {
  transition: all 200ms ease;
}

.empty-drop-cell {
  height: 60px;
  padding: 16px;
  border: none;
  transition: all 300ms ease;
  text-align: center;
  color: #9ca3af;
  background-color: rgba(59, 130, 246, 0.02);
}

/* When dragging over empty category, create space and visual feedback */
.empty-category.cdk-drop-list-receiving {
  transform: translateY(-8px);
  background-color: rgba(59, 130, 246, 0.05);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.empty-category.cdk-drop-list-receiving .empty-drop-cell {
  background-color: rgba(59, 130, 246, 0.08);
  color: #3b82f6;
  font-weight: 500;
}





@keyframes gentle-pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.6;
  }
  50% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.6;
  }
}

/* Improved visual feedback for all drop zones */
.category-services.cdk-drop-list-receiving {
  position: relative;
  background: transparent;
  /* border: 2px dashed #3b82f6; */
  border-radius: 8px;
  animation: none;
}

@keyframes glow-border {
  0% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
  }
}
