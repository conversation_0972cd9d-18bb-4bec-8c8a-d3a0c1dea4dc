<div class="admin-container">

  <!-- Header Component -->
  <app-admin-services-header
    [categoryOptions]="categoryOptions"
    [selectedCategory]="selectedCategory"
    [isCategoryMoveMode]="isCategoryMoveMode"
    [searchTerm]="searchTerm"
    (addServiceClicked)="addService()"
    (importServicesClicked)="importServices()"
    (toggleCategoryMoveModeClicked)="toggleCategoryMoveMode()"
    (platformManagementClicked)="openPlatformManagement()"
    (categorySelected)="onCategorySelected($event)"
    (newCategoryClicked)="openNewCategoryModal()"
    (searchChanged)="applyFilter($event)"
    (filterReset)="resetFilter()">
  </app-admin-services-header>

  <!-- Category Move Mode Table (looks identical to main table) -->
  <div *ngIf="viewMode === 'table' && isCategoryMoveMode" class="table mb-6" style="width: 100%!important; max-width: none!important;">
    <table class="w-full divide-y divide-gray-200 admin-services-table"
      style="table-layout: fixed!important; width: 100%!important; min-width: 100%!important; max-width: none!important;">
      <colgroup>
        <col style="width: 60px;">
        <col style="width: 55%;">
        <col style="width: 15%;">
        <col style="width: 5%;">
        <col style="width: 5%;">
        <col style="width: 7%;">
        <col style="width: 8%;">
        <col style="width: 5%;">
      </colgroup>
      <thead>
        <tr>
          <th class="text-center">
            <input type="checkbox" [checked]="areAllServicesSelected()" (change)="toggleAllServices($event)"
              class="form-checkbox">
          </th>
          <th>
            <div class="flex items-center">
              <ng-container *ngIf="hasSelectedServices(); else normalServiceHeader">
                <span class="font-medium">{{ getSelectedServicesCount() }} {{ 'selected' | translate }}</span>
                <div class="relative ml-2 ">
                  <app-admin-menu  [menuItems]="getBulkActionMenuItems()"
                    (menuItemClicked)="onBulkActionMenuItemClick($event)">
                    <fa-icon [icon]="['fas', 'ellipsis']"></fa-icon>
                  </app-admin-menu>
                </div>
              </ng-container>
              <ng-template #normalServiceHeader>
                {{ 'service' | translate }}
              </ng-template>
            </div>
          </th>
          <th>{{ 'api_info' | translate }}</th>
          <th>{{ 'min_value' | translate }}</th>
          <th>{{ 'max_value' | translate }}</th>
          <th>{{ 'refill' | translate }}</th>
          <th>{{ 'average_time' | translate }}</th>
          <th>{{ 'action' | translate }}</th>
        </tr>
      </thead>

      <!-- Category Move Mode Drop List (looks like normal category headers) -->
      <tbody cdkDropList [cdkDropListData]="displayCategories" (cdkDropListDropped)="onCategoryMoveModeDropCdk($event)"
        class="category-move-drop-list">
        <ng-container *ngFor="let category of displayCategories; let categoryIndex = index">
          <!-- Category Header Row (identical to main table but always draggable) -->
          <tr class="category-header-row move-mode"
            cdkDrag
            [cdkDragData]="category"
            (cdkDragStarted)="onCategoryDragStarted($event)"
            (cdkDragEnded)="onDragEnded()">
            <td colspan="8">
              <div class="flex items-center justify-between w-full rounded-2xl">
                <div class="flex items-center">
                  <input type="checkbox" [checked]="isCategoryFullySelected(category)"
                    (change)="toggleCategorySelection(category, $event)" class="form-checkbox mr-2"
                    (click)="$event.stopPropagation()">
                  <app-social-icon *ngIf="category.platformIcon" [icon]="category.platformIcon"></app-social-icon>
                  <label class="ms-2 text-sm font-medium">
                    <ng-container *ngIf="category.isAllCategories">
                      {{ 'filter.all_categories' | translate }}
                    </ng-container>
                    <ng-container *ngIf="!category.isAllCategories">
                      {{ category.name }}
                    </ng-container>
                  </label>
                  <div class="relative ml-2">
                    <app-admin-menu [menuItems]="getCategoryMenuItems(category)"
                      (menuItemClicked)="onCategoryMenuItemClick($event, category)"
                      (click)="$event.stopPropagation()">
                      <fa-icon [icon]="['fas', 'ellipsis']"></fa-icon>
                    </app-admin-menu>
                  </div>
                </div>
                <div class="flex items-center">
                  <div class="relative inline-block menu-container">
                    <button (click)="toggleCategoryPlatformDropdown($event, category); $event.stopPropagation()"
                      class="bg-blue-500 text-white text-xs px-3 py-1 rounded-full mr-2">
                      {{ category.platformName }}
                    </button>

                    <!-- Platform Dropdown Menu -->
                    <div *ngIf="showCategoryPlatformDropdown && selectedCategoryForPlatform?.id === category.id"
                      class="absolute w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50 platform-dropdown-content top-full mt-1 right-0">
                      <div class="py-2 max-h-60 overflow-y-auto">
                        <div *ngFor="let platform of allPlatforms"
                          class="px-4 py-2 hover:bg-gray-100 flex items-center cursor-pointer"
                          (click)="selectCategoryPlatform(category, platform)">
                          <span class="inline-flex items-center justify-center w-5 h-5 mr-3">
                            <span *ngIf="isCategoryPlatformSelected(category, platform.id)"
                              class="text-blue-500">✓</span>
                          </span>
                          <app-social-icon *ngIf="platform.icon" [icon]="platform.icon"></app-social-icon>
                          <span class="ml-2 text-sm text-gray-700">{{ platform.name }}</span>
                        </div>
                      </div>
                      <div class="border-t border-gray-200">
                        <button (click)="addNewPlatformFromCategory($event)"
                          class="w-full text-left px-4 py-3 text-blue-500 hover:bg-gray-100 flex items-center">
                          <fa-icon [icon]="['fas', 'cog']" class="mr-2"></fa-icon>
                          <span class="text-sm">{{ 'admin.services.configure' | translate }}</span>
                        </button>
                      </div>
                    </div>
                  </div>
                  <div class="category-drag-handle mr-2 cursor-grab">
                    <fa-icon [icon]="['fas', 'arrows-alt']" class="text-gray-400"></fa-icon>
                  </div>
                  <!-- Expand button for categories with services - exits move mode and expands category -->
                  <div class="category-expand-toggle" *ngIf="category.services.length > 0">
                    <button class="expand-btn" (click)="exitMoveAndExpandCategory(category); $event.stopPropagation()"
                      title="Expand category and exit move mode">
                      <fa-icon [icon]="['fas', 'angle-down']" class="text-gray-600 text-xl"></fa-icon>
                    </button>
                  </div>
                </div>
              </div>
            </td>

            <!-- CDK drag preview for category row (full width like real row) -->
            <div *cdkDragPreview class="category-move-drag-preview">
              <div class="category-preview-content">
                <div class="flex items-center">
                  <input type="checkbox" [checked]="isCategoryFullySelected(category)" class="form-checkbox mr-2">
                  <app-social-icon *ngIf="category.platformIcon" [icon]="category.platformIcon" class="mr-2"></app-social-icon>
                  <span class="font-medium text-gray-900 mr-3">
                    <ng-container *ngIf="category.isAllCategories">
                      {{ 'filter.all_categories' | translate }}
                    </ng-container>
                    <ng-container *ngIf="!category.isAllCategories">
                      {{ category.name }}
                    </ng-container>
                  </span>
                </div>
                <div class="flex items-center">
                  <span class="bg-blue-500 text-white text-xs px-2 py-1 rounded-full mr-2">
                    {{ category.platformName }}
                  </span>
                  <fa-icon [icon]="['fas', 'grip-vertical']" class="text-gray-400 mr-2"></fa-icon>
                  <fa-icon *ngIf="category.services.length > 0" [icon]="['fas', 'angle-down']" class="text-gray-600"></fa-icon>
                </div>
              </div>
            </div>
          </tr>
        </ng-container>
      </tbody>
    </table>
  </div>

  <!-- Main Table View (hidden during move mode) -->
  <div class="table mb-6" *ngIf="viewMode === 'table' && !isCategoryMoveMode" style="width: 100%!important; max-width: none!important;">
    <table class="w-full divide-y divide-gray-200 admin-services-table"
      style="table-layout: fixed!important; width: 100%!important; min-width: 100%!important; max-width: none!important;">
      <colgroup>
        <col style="width: 60px;">
        <col style="width: 55%;">
        <col style="width: 15%;">
        <col style="width: 5%;">
        <col style="width: 5%;">
        <col style="width: 7%;">
        <col style="width: 8%;">
        <col style="width: 5%;">
      </colgroup>
      <thead>
        <tr>
          <th class="text-center">
            <input type="checkbox" [checked]="areAllServicesSelected()" (change)="toggleAllServices($event)"
              class="form-checkbox">
          </th>
          <th>
            <div class="flex items-center">
              <ng-container *ngIf="hasSelectedServices(); else normalServiceHeader">
                <span class="font-medium">{{ getSelectedServicesCount() }} {{ 'selected' | translate }}</span>
                <div class="relative ml-2">
                  <app-admin-menu [menuItems]="getBulkActionMenuItems()"
                    (menuItemClicked)="onBulkActionMenuItemClick($event)">
                    <fa-icon [icon]="['fas', 'ellipsis']"></fa-icon>
                  </app-admin-menu>
                </div>
              </ng-container>
              <ng-template #normalServiceHeader>
                {{ 'service' | translate }}
              </ng-template>
            </div>
          </th>
          <th>{{ 'api_info' | translate }}</th>
          <th>{{ 'min_value' | translate }}</th>
          <th>{{ 'max_value' | translate }}</th>
          <th>{{ 'refill' | translate }}</th>
          <th>{{ 'average_time' | translate }}</th>
          <th>{{ 'action' | translate }}</th>
        </tr>
      </thead>
      <!-- <tbody> -->
      <!-- Display multiple categories when "All Categories" is selected -->
      <!-- <ng-container *ngIf="displayCategories.length > 0"> -->
      <!-- Category Drop List Container -->
      <tbody cdkDropList [cdkDropListData]="displayCategories" (cdkDropListDropped)="onCategoryDropCdk($event)"
        class="category-drop-list" style="display: contents!important;">
        <ng-container *ngFor="let category of displayCategories; let categoryIndex = index">
          <!-- Category Header Row -->
          <tr class="category-header-row"
            cdkDrag
            [cdkDragDisabled]="!isCategoryMoveMode"
            [cdkDragData]="category"
            (cdkDragStarted)="onCategoryDragStarted($event)"
            (cdkDragEnded)="onDragEnded()"
            [ngClass]="{'drag-over': dragOverCategoryIndex === categoryIndex, 'move-mode': isCategoryMoveMode}">
            <td colspan="8">
              <div class="flex items-center justify-between w-full rounded-2xl">
                <div class="flex items-center">
                  <input type="checkbox" [checked]="isCategoryFullySelected(category)"
                    (change)="toggleCategorySelection(category, $event)" class="form-checkbox mr-2"
                    (click)="$event.stopPropagation()">
                  <app-social-icon *ngIf="category.platformIcon" [icon]="category.platformIcon"></app-social-icon>
                  <label class="ms-2 text-sm font-medium">
                    <ng-container *ngIf="category.isAllCategories">
                      {{ 'filter.all_categories' | translate }}
                    </ng-container>
                    <ng-container *ngIf="!category.isAllCategories">
                      {{ category.name }}
                    </ng-container>
                  </label>
                  <div class="relative ml-2">
                    <app-admin-menu [menuItems]="getCategoryMenuItems(category)"
                      (menuItemClicked)="onCategoryMenuItemClick($event, category)"
                      (click)="$event.stopPropagation()">
                      <fa-icon [icon]="['fas', 'ellipsis']"></fa-icon>
                    </app-admin-menu>
                  </div>
                </div>
                <div class="flex items-center">
                  <div class="relative inline-block menu-container">
                    <button (click)="toggleCategoryPlatformDropdown($event, category); $event.stopPropagation()"
                      class="bg-blue-500 text-white text-xs px-3 py-1 rounded-full mr-2">
                      {{ category.platformName }}
                    </button>

                    <!-- Platform Dropdown Menu -->
                    <div *ngIf="showCategoryPlatformDropdown && selectedCategoryForPlatform?.id === category.id"
                      class="absolute w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50 platform-dropdown-content top-full mt-1 right-0">
                      <div class="py-2 max-h-60 overflow-y-auto">
                        <div *ngFor="let platform of allPlatforms"
                          class="px-4 py-2 hover:bg-gray-100 flex items-center cursor-pointer"
                          (click)="selectCategoryPlatform(category, platform)">
                          <span class="inline-flex items-center justify-center w-5 h-5 mr-3">
                            <span *ngIf="isCategoryPlatformSelected(category, platform.id)"
                              class="text-blue-500">✓</span>
                          </span>
                          <app-social-icon *ngIf="platform.icon" [icon]="platform.icon"></app-social-icon>
                          <span class="ml-2 text-sm text-gray-700">{{ platform.name }}</span>
                        </div>
                      </div>
                      <div class="border-t border-gray-200">
                        <button (click)="addNewPlatformFromCategory($event)"
                          class="w-full text-left px-4 py-3 text-blue-500 hover:bg-gray-100 flex items-center">
                          <fa-icon [icon]="['fas', 'cog']" class="mr-2"></fa-icon>
                          <span class="text-sm">{{ 'admin.services.configure' | translate }}</span>
                        </button>
                      </div>
                    </div>
                  </div>

                  <div class="category-visibility-toggle" *ngIf="category.services.length > 0">
                    <button class="toggle-arrow-btn" (click)="onToggleCategoryVisibility(category, category.hide); $event.stopPropagation()">
                      <fa-icon [icon]="['fas', category.hide ? 'angle-down' : 'angle-up']"
                        class="text-gray-600 text-xl"></fa-icon>
                    </button>
                  </div>
                </div>
              </div>
            </td>

            <!-- CDK drag preview for category row (full width like real row) -->
            <div *cdkDragPreview class="category-main-drag-preview">
              <div class="category-preview-content">
                <div class="flex items-center">
                  <input type="checkbox" [checked]="isCategoryFullySelected(category)" class="form-checkbox mr-2">
                  <app-social-icon *ngIf="category.platformIcon" [icon]="category.platformIcon" class="mr-2"></app-social-icon>
                  <span class="font-medium text-gray-900 mr-3">
                    <ng-container *ngIf="category.isAllCategories">
                      {{ 'filter.all_categories' | translate }}
                    </ng-container>
                    <ng-container *ngIf="!category.isAllCategories">
                      {{ category.name }}
                    </ng-container>
                  </span>
                </div>
                <div class="flex items-center">
                  <span class="bg-blue-500 text-white text-xs px-2 py-1 rounded-full mr-2">
                    {{ category.platformName }}
                  </span>
                  <fa-icon [icon]="['fas', 'arrows-alt']" class="text-gray-400 mr-2"></fa-icon>
                  <fa-icon *ngIf="category.services.length > 0" [icon]="['fas', category.hide ? 'angle-down' : 'angle-up']" class="text-gray-600"></fa-icon>
                </div>
              </div>
            </div>
          </tr>

          <!-- Category Services -->
          <ng-container *ngIf="!category.hide">
      <tbody id="category-services-{{category.id}}" class="category-services !w-full" cdkDropList
        [cdkDropListData]="category.services" (cdkDropListDropped)="onServiceDropCdk($event, category)"
        [cdkDropListConnectedTo]="getConnectedDropLists(category)"
        [class.empty-category]="category.services.length === 0">

        <!-- Empty category drop zone (only visible during drag) -->
        <tr *ngIf="category.services.length === 0 && isDragging" class="empty-drop-zone">
          <td colspan="8" class="empty-drop-cell">
            <div class="flex items-center justify-center">
              <fa-icon [icon]="['fas', 'plus']" class="mr-2"></fa-icon>
              <span>{{ 'admin.services.drop_service_here' | translate }}</span>
            </div>
          </td>
        </tr>

        <!-- Service rows -->
        <tr *ngFor="let service of category.services; let i = index" class="bg-white service-row"
          [ngClass]="{'deactivated': !isServiceEnabled(service)}" cdkDrag [cdkDragData]="service"
          (cdkDragStarted)="onServiceDragStarted($event)"
          (cdkDragEnded)="onDragEnded()"
          (cdkDragMoved)="onServiceDragMoved($event)">
          <td class="text-center">
            <input type="checkbox" [checked]="isServiceSelected(service)"
              (change)="toggleServiceSelection(service, $event)" class="form-checkbox"
              (click)="$event.stopPropagation()">
          </td>
          <td class="px-4 py-2">
            <div class="p-2">
              <!-- Service info -->
              <div class="flex items-center">

                <app-service-label [service]="service"></app-service-label>
              </div>
            </div>
          </td>
          <td>
            <div class="api-info-column">
              <div class="api-id-container">
                <span class="api-id">{{ service.api_service_id }}</span>
                <fa-icon *ngIf="service.api_service_id" [icon]="['fas', 'copy']" class="copy-icon"
                  (click)="copyToClipboard(service.api_service_id, $event); $event.stopPropagation()" [fixedWidth]="true"></fa-icon>
              </div>
              <div class="api-url-container">
                <a *ngIf="service.api_provider?.url" [href]="'https://' + extractDomainName(service.api_provider.url)"
                  target="_blank" class="api-url" (click)="$event.stopPropagation()">{{ extractDomainName(service.api_provider.url) }}</a>
                <span *ngIf="!service.api_provider?.url" class="api-url">{{ 'admin.services.no_provider' | translate }}</span>
              </div>
            </div>
          </td>
          <td>{{ service.min }}</td>
          <td>{{ service.max }}</td>
          <td>
            <span *ngIf="service.refill === true; else elseBlock" class="lifetime-refill-badge">{{service.refill_days}}
              {{ 'admin.services.days' | translate }}</span>
            <ng-template #elseBlock>
              <span class="lifetime-refill-badge">{{ 'admin.services.no_refill' | translate }}</span>
            </ng-template>
          </td>
          <td>{{ service.average_time | timeFormat }}</td>
          <td>
            <div class="relative">
              <app-admin-menu [menuItems]="getServiceMenuItems(service)"
                (menuItemClicked)="onServiceMenuItemClick($event, service)"
                (click)="$event.stopPropagation()">
                <fa-icon [icon]="['fas', 'ellipsis']"></fa-icon>
              </app-admin-menu>
            </div>
          </td>

          <!-- CDK drag preview for service row -->
          <div *cdkDragPreview class="service-row-preview">
            <table class="service-preview-table">
              <tr class="bg-white service-row-preview" [ngClass]="{'deactivated': !isServiceEnabled(service)}">
                <td class="text-center">
                  <div class="w-4 h-4 mx-auto"></div>
                </td>
                <td class="px-4 py-2">
                  <div class="p-2">
                    <div class="flex items-center">

                      <app-service-label  [service]="service"></app-service-label>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="api-info-column">
                    <div class="api-id-container">
                      <span class="api-id">{{ service.api_service_id }}</span>
                    </div>
                    <div class="api-url-container">
                      <span *ngIf="service.api_provider?.url" class="api-url">{{
                        extractDomainName(service.api_provider.url) }}</span>
                      <span *ngIf="!service.api_provider?.url" class="api-url">{{ 'admin.services.no_provider' | translate }}</span>
                    </div>
                  </div>
                </td>
                <td>{{ service.min }}</td>
                <td>{{ service.max }}</td>
                <td>
                  <span *ngIf="service.refill === true" class="lifetime-refill-badge">{{service.refill_days}}
                    {{ 'admin.services.days' | translate }}</span>
                  <span *ngIf="service.refill !== true" class="lifetime-refill-badge">{{ 'admin.services.no_refill' | translate }}</span>
                </td>
                <td>{{ service.average_time | timeFormat }}</td>
                <td></td>
              </tr>
            </table>
          </div>
        </tr>
         <tr class="h-[5px]"></tr>
      </tbody>
      </ng-container>
      </ng-container>
      </tbody>
      <!-- </ng-container> -->
      <!-- </tbody> -->
    </table>
  </div>

  <!-- Card View (for mobile) -->
  <div *ngIf="viewMode === 'card'" class="mb-6">
    <!-- Select All and Bulk Actions Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-4 p-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <input type="checkbox" [checked]="areAllServicesSelected()" (change)="toggleAllServices($event)"
            class="form-checkbox !mr-2">
          <span class="font-medium">
            <ng-container *ngIf="hasSelectedServices(); else selectAllLabel">
              {{ getSelectedServicesCount() }} {{ 'selected' | translate }}
            </ng-container>
            <ng-template #selectAllLabel>
              {{ 'select all' | translate }}
            </ng-template>
          </span>
        </div>

        <!-- Bulk Action Menu Button -->
        <div class="relative menu-container" *ngIf="hasSelectedServices()">
          <app-admin-menu [menuItems]="getBulkActionMenuItems()" [isOpen]="showBulkActionMenu"
            (menuItemClicked)="onBulkActionMenuItemClick($event)" (menuClosed)="closeAllMenus()">
            <fa-icon [icon]="['fas', 'ellipsis']"></fa-icon>
          </app-admin-menu>
        </div>
      </div>
    </div>

    <!-- Categories and Services Cards -->
    <div class="space-y-4">
      <ng-container *ngFor="let category of displayCategories; let categoryIndex = index">
        <!-- Category Header Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <input type="checkbox" [checked]="isCategoryFullySelected(category)"
                (change)="toggleCategorySelection(category, $event)" class="form-checkbox mr-3">
              <app-social-icon *ngIf="category.platformIcon" [icon]="category.platformIcon" class="mr-2"></app-social-icon>
              <span class="font-medium text-gray-900">
                <ng-container *ngIf="category.isAllCategories">
                  {{ 'filter.all_categories' | translate }}
                </ng-container>
                <ng-container *ngIf="!category.isAllCategories">
                  {{ category.name }}
                </ng-container>
              </span>
              <div class="relative ml-2">
                <app-admin-menu [menuItems]="getCategoryMenuItems(category)"
                  (menuItemClicked)="onCategoryMenuItemClick($event, category)">
                  <fa-icon [icon]="['fas', 'ellipsis']"></fa-icon>
                </app-admin-menu>
              </div>
            </div>
            <div class="flex items-center">
              <span class="bg-blue-500 text-white text-xs px-2 py-1 rounded-full mr-2">
                {{ category.platformName }}
              </span>
              <button *ngIf="category.services.length > 0"
                (click)="onToggleCategoryVisibility(category, category.hide)"
                class="text-gray-600">
                <fa-icon [icon]="['fas', category.hide ? 'angle-down' : 'angle-up']" class="text-xl"></fa-icon>
              </button>
            </div>
          </div>
        </div>

        <!-- Services Cards (only show if category is not hidden) -->
        <div *ngIf="!category.hide" class="space-y-3 ml-4">
          <!-- Empty state for categories with no services -->
          <!-- <div *ngIf="category.services.length === 0" class="bg-gray-50 rounded-lg p-4 text-center text-gray-500">
            <fa-icon [icon]="['fas', 'inbox']" class="text-2xl mb-2"></fa-icon>
            <p>{{ 'No services in this category' | translate }}</p>
          </div> -->

          <!-- Individual Service Cards -->
          <div *ngFor="let service of category.services"
            class="service-card"
            [ngClass]="{'selected': isServiceSelected(service), 'deactivated': !isServiceEnabled(service)}">

            <!-- Service Card Header -->
            <div class="flex items-start justify-between mb-3">
              <div class="flex items-start">
                <input type="checkbox" [checked]="isServiceSelected(service)"
                  (change)="toggleServiceSelection(service, $event)"
                  class="form-checkbox mt-1 mr-3">
                <div class="flex-1">
                  <app-service-label [service]="service"></app-service-label>
                </div>
              </div>
              <div class="relative">
                <app-admin-menu [menuItems]="getServiceMenuItems(service)"
                  (menuItemClicked)="onServiceMenuItemClick($event, service)">
                  <fa-icon [icon]="['fas', 'ellipsis']"></fa-icon>
                </app-admin-menu>
              </div>
            </div>

            <!-- Service Card Content -->
            <div class="space-y-2 text-sm">
              <!-- API Info -->
              <div class="flex justify-between items-center">
                <span class="text-gray-600 font-medium">{{ 'api_info' | translate }}:</span>
                <div class="text-right">
                  <div class="flex items-center">
                    <span class="text-gray-900">{{ service.api_service_id }}</span>
                    <fa-icon *ngIf="service.api_service_id" [icon]="['fas', 'copy']"
                      class="ml-2 text-gray-400 cursor-pointer hover:text-gray-600"
                      (click)="copyToClipboard(service.api_service_id, $event)"></fa-icon>
                  </div>
                  <div class="text-xs text-gray-500">
                    <a *ngIf="service.api_provider?.url"
                      [href]="'https://' + extractDomainName(service.api_provider.url)"
                      target="_blank" class="text-blue-600 hover:underline">
                      {{ extractDomainName(service.api_provider.url) }}
                    </a>
                    <span *ngIf="!service.api_provider?.url">{{ 'admin.services.no_provider' | translate }}</span>
                  </div>
                </div>
              </div>

              <!-- Min/Max Values -->
              <div class="flex justify-between items-center">
                <span class="text-gray-600 font-medium">{{ 'min_value' | translate }}:</span>
                <span class="text-gray-900">{{ service.min }}</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-gray-600 font-medium">{{ 'max_value' | translate }}:</span>
                <span class="text-gray-900">{{ service.max }}</span>
              </div>

              <!-- Refill -->
              <div class="flex justify-between items-center">
                <span class="text-gray-600 font-medium">{{ 'refill' | translate }}:</span>
                <span class="lifetime-refill-badge">
                  <ng-container *ngIf="service.refill === true; else noRefillBlock">
                    {{ service.refill_days }} {{ 'admin.services.days' | translate }}
                  </ng-container>
                  <ng-template #noRefillBlock>
                    {{ 'admin.services.no_refill' | translate }}
                  </ng-template>
                </span>
              </div>

              <!-- Average Time -->
              <div class="flex justify-between items-center">
                <span class="text-gray-600 font-medium">{{ 'average_time' | translate }}:</span>
                <span class="text-gray-900">{{ service.average_time | timeFormat }}</span>
              </div>
            </div>
          </div>
        </div>
      </ng-container>
    </div>
  </div>

</div>

<!-- Modal Components -->
<app-new-service *ngIf="showNewServiceModal" [serviceType]="uiStateService.selectedServiceType"
  [serviceToEdit]="uiStateService.selectedServiceForAction" (close)="closeNewServiceModal()"
  (serviceAdded)="onServiceAdded($event)" (serviceUpdated)="onServiceAdded($event)"
  (openResources)="openResourcesFromNewService()">
</app-new-service>

<app-add-platform-light *ngIf="showAddPlatformLightModal" (close)="closeAddPlatformLightModal()"
  (platformAdded)="onPlatformAdded($event)">
</app-add-platform-light>

<app-platform-management *ngIf="showPlatformManagementModal" (close)="closePlatformManagementModal()"
  (platformsUpdated)="onPlatformsUpdated($event)">
</app-platform-management>

<app-import-services *ngIf="showImportServicesModal" (close)="closeImportServicesModal()"
  (servicesImported)="onServicesImported($event)">
</app-import-services>

<app-category-selection *ngIf="showCategorySelectionModal"
  [serviceId]="selectedServiceForAction ? selectedServiceForAction.id : null" (close)="closeCategorySelectionModal()"
  (categorySelected)="onCategorySelectedForMove($event)">
</app-category-selection>

<app-new-category *ngIf="showNewCategoryModal" [categoryToEdit]="categoryToEdit" (close)="closeNewCategoryModal()"
  (categoryAdded)="onCategoryAdded($event)" (categoryUpdated)="onCategoryUpdated($event)">
</app-new-category>

<app-new-prices *ngIf="showNewPricesModal" [selectedServices]="selectionService.getSelectedServices()"
  (close)="closeNewPricesModal()" (pricesUpdated)="onPricesUpdated($event)">
</app-new-prices>

<app-new-special-prices
  *ngIf="showNewSpecialPricesModal"
  [selectedServices]="selectionService.getSelectedServices()"
  [selectedService]="selectedServiceForAction"
  [editMode]="!!specialPriceToEdit"
  [specialPriceToEdit]="specialPriceToEdit"
  (close)="closeNewSpecialPricesModal()"
  (specialPriceAdded)="onSpecialPriceAdded($event)">
</app-new-special-prices>

<app-special-prices-user
  *ngIf="showSpecialPricesUserModal"
  [selectedServices]="selectionService.getSelectedServices()"
  [selectedService]="selectedServiceForAction"
  (close)="closeSpecialPricesUserModal()"
  (specialPriceAdded)="onSpecialPriceAdded($event)">
</app-special-prices-user>

<app-special-prices-service
  *ngIf="showSpecialPricesServiceModal"
  [selectedService]="selectedServiceForAction"
  (close)="closeSpecialPricesServiceModal()"
  (specialPriceAdded)="onSpecialPriceAdded($event)">
</app-special-prices-service>

<app-resources *ngIf="showResourcesModal" (close)="closeResourcesModal()"
  (openNewService)="openNewServiceFromResources($event)">
</app-resources>

<!-- Delete Confirmation Modal -->
<app-delete-confirmation *ngIf="showDeleteConfirmation && serviceToDelete" [itemName]="serviceToDelete.name"
  [isLoading]="isDeleting" (close)="closeDeleteConfirmation()" (confirm)="confirmDeleteService()">
</app-delete-confirmation>

<!-- Bulk Delete Confirmation Modal -->
<app-delete-confirmation *ngIf="showBulkDeleteConfirmation"
  [itemName]="bulkDeleteMessage"
  [isLoading]="loadingBulkOperation"
  (close)="closeBulkDeleteConfirmation()"
  (confirm)="confirmBulkDelete()">
</app-delete-confirmation>

<!-- Category Delete Confirmation Modal -->
<app-delete-confirmation *ngIf="showCategoryDeleteConfirmation && categoryToDelete"
  [itemName]="categoryToDelete.name"
  [isLoading]="isCategoryDeleting"
  (close)="closeCategoryDeleteConfirmation()"
  (confirm)="confirmCategoryDelete()">
</app-delete-confirmation>