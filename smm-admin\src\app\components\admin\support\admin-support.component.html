<div class="layout-container py-6 px-6">

  <div class="mb-4">
    <h2 class="text-xl font-bold">{{ 'admin.support.support' | translate }}</h2>
  </div>
  <div class="mb-6">
    <div class="flex flex-nowrap overflow-x-auto -mb-px mt-4 pb-1 hide-scrollbar">
      <div *ngFor="let filter of statusFilters" class="mr-2 flex-shrink-0">
        <button (click)="toggleFilter(filter)"
          [ngClass]="{'text-[#3b82f6] border-b-2 !border-[#3b82f6] font-medium' : filter.active, 'text-gray-500 hover:border-b-2 hover:text-gray-700 hover:border-gray-300': !filter.active}"
          class="inline-block md:p-4 p-2 border-transparent rounded-t-lg whitespace-nowrap">
          {{ filter.label }}
        </button>
      </div>
    </div>
  </div>

  <!-- Search bar -->
  <div class="mb-6 flex w-full gap-4 items-stretch h-[46px] md:flex-row flex-col">
    <div class="flex-grow h-full md:mb-0 mb-2">
      <div class="relative h-full">
        <input type="text" [(ngModel)]="searchTerm" placeholder="{{ 'admin.support.search_by_username' | translate }}"
          class="w-full h-full px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#3b82f6]">
        <button (click)="onSearchButton()"
          class="absolute right-0 top-0 h-full px-4 bg-[#3b82f6] text-white rounded-r-lg hover:bg-[#2563eb] transition-colors duration-200">
          <fa-icon [icon]="['fas', 'search']"></fa-icon>
        </button>
      </div>
    </div>
    <button (click)="toggleFiltersPanel()"
      class="px-5 h-full text-sm font-medium text-gray-700 border border-gray-300 rounded-lg hover:bg-[#f0f7ff] focus:ring-2 focus:ring-[#3b82f6] flex items-center justify-center flex-shrink-0 transition-colors duration-200 md:w-auto w-full">
      <fa-icon [icon]="['fas', 'filter']" class="mr-2 text-[#3b82f6]"></fa-icon>
      {{ 'admin.support.filter' | translate }}
    </button>
  </div>

  <!-- Advanced filters (hidden by default) -->
  <div *ngIf="showFilters" class="mb-6 p-6 bg-white border border-gray-200 rounded-lg shadow-sm">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
      <!-- Date range -->
      <div>
        <label class="block mb-2 text-sm font-medium text-gray-700">{{ 'admin.support.date_range' | translate }}</label>
        <app-date-range-picker
          [containerClass]="'h-[46px]'"
          [(ngModel)]="dateRange"
          (dateRangeChanged)="onDateRangeChanged($event)">
        </app-date-range-picker>
      </div>
    </div>

    <div class="flex justify-end space-x-3">
      <button (click)="resetFilters()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200">
        {{ 'admin.support.reset' | translate }}
      </button>
      <button (click)="applyFilters()" class="px-4 py-2 text-sm font-medium text-white bg-[#3b82f6] rounded-lg hover:bg-[#2563eb] transition-colors duration-200">
        {{ 'admin.support.apply_filters' | translate }}
      </button>
    </div>
  </div>

  <!-- Global loading is now handled in the detail component -->

  <!-- Table View (for desktop) -->
  <div *ngIf="viewMode === 'table'" class="overflow-x-auto">
    <div class="inline-block min-w-full align-middle">
      <div class="overflow-hidden rounded-lg">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ 'admin.support.id' | translate }}
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ 'admin.support.subject' | translate }}
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ 'admin.support.status' | translate }}
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ 'admin.support.last_update' | translate }}
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ 'admin.support.actions' | translate }}
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr *ngFor="let ticket of tickets" class="hover:bg-[#f0f7ff] transition-colors duration-150 cursor-pointer" (click)="navigateTo(ticket.id)">
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                #{{ ticket.id }}
              </td>
              <td class="px-6 py-4 text-sm text-gray-700">
                <div class="font-semibold">{{ ticket.subject }}</div>
                <div class="text-sm text-gray-600 max-w-xs truncate">{{ ticket.description | slice:0:50 }}{{ ticket.description.length > 50 ? '...' : '' }}</div>
                <div class="font-semibold text-sm text-gray-700">
                  <a [routerLink]="['/panel/users']" [queryParams]="{search: ticket.created_by}" (click)="$event.stopPropagation()" class="text-blue-600 hover:underline">{{ ticket.created_by }}</a>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <app-ticket-status [status]="ticket.status"></app-ticket-status>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                {{ ticket.updated_at | date: 'short' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="relative">
                  <app-admin-menu
                    [menuItems]="getTicketMenuItems(ticket)"
                    (menuItemClicked)="onTicketMenuItemClick($event, ticket.id)">
                    <fa-icon [icon]="['fas', 'ellipsis']"></fa-icon>
                  </app-admin-menu>
                </div>
              </td>
            </tr>
            <tr *ngIf="tickets.length === 0">
              <td colspan="5" class="px-6 py-8 text-center text-sm text-gray-500">{{ 'admin.support.no_tickets' | translate }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Card View (for mobile) -->
  <div *ngIf="viewMode === 'card'" class="grid grid-cols-1 gap-4">
    <!-- Empty state for mobile -->
    <div *ngIf="tickets.length === 0" class="bg-white rounded-lg p-8 text-center text-gray-500">
      {{ 'admin.support.no_tickets' | translate }}
    </div>

    <!-- Mobile cards -->
    <div *ngFor="let ticket of tickets" class="mobile-card" (click)="navigateTo(ticket.id)">
      <!-- Card header with ID and status -->
      <div class="mobile-card-header">
        <span class="text-gray-700 font-medium">#{{ ticket.id }}</span>
        <app-ticket-status [status]="ticket.status"></app-ticket-status>
      </div>

      <!-- Card content -->
      <div class="mobile-card-content">
        <h3 class="font-semibold text-gray-800 mb-1">{{ ticket.subject }}</h3>
        <p class="text-sm text-gray-600 mb-2">{{ ticket.description | slice:0:100 }}{{ ticket.description.length > 100 ? '...' : '' }}</p>
        <div class="text-sm text-gray-700">
          <span class="font-medium">{{ 'admin.support.user' | translate }} </span>
          <a [routerLink]="['/panel/users']" [queryParams]="{search: ticket.created_by}" (click)="$event.stopPropagation()" class="text-blue-600 hover:underline">{{ ticket.created_by }}</a>
        </div>
        <div class="text-sm text-gray-700">
          <span class="font-medium">{{ 'admin.support.last_update_label' | translate }} </span>
          <span>{{ ticket.updated_at | date: 'short' }}</span>
        </div>
      </div>

      <!-- Card actions -->
      <div class="mobile-card-footer">
        <div class="relative">
          <app-admin-menu
            [menuItems]="getTicketMenuItems(ticket)"
            (menuItemClicked)="onTicketMenuItemClick($event, ticket.id)">
            <fa-icon [icon]="['fas', 'ellipsis']"></fa-icon>
          </app-admin-menu>
        </div>
      </div>
    </div>
  </div>

  <!-- Pagination -->
  <div *ngIf="pagination.totalPages > 1" class="flex flex-wrap justify-center mt-6 gap-2">
    <nav class="flex flex-col md:flex-row items-center justify-between w-full">
      <!-- Page info for mobile -->
      <div class="text-sm text-gray-700 mb-3 md:hidden w-full text-center">
        {{ 'admin.support.page' | translate }} {{ pagination.pageNumber + 1 }} {{ 'admin.support.of' | translate }} {{ pagination.totalPages }}
      </div>

      <div class="flex items-center space-x-2 w-full justify-center">
        <button [disabled]="pagination.pageNumber === 0"
          [ngClass]="pagination.pageNumber === 0 ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:bg-gray-100'"
          class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white"
          (click)="changePage(pagination.pageNumber - 1)">
          <fa-icon [icon]="['fas', 'chevron-left']" class="text-xs"></fa-icon>
        </button>

        <!-- Hide page numbers on mobile except current page -->
        <ng-container
          *ngFor="let page of [].constructor(pagination.totalPages > 5 ? 5 : pagination.totalPages); let i = index">
          <button *ngIf="(i < 3 || i >= pagination.totalPages - 2 || i === pagination.pageNumber) && (i === pagination.pageNumber || !viewMode.includes('card'))"
            [ngClass]="i === pagination.pageNumber ? 'bg-[#3b82f6] text-white border-[#3b82f6]' : 'text-gray-700 hover:bg-gray-100 border-gray-300'"
            class="px-3 py-2 border rounded-md text-sm font-medium"
            (click)="changePage(i)">
            {{ i + 1 }}
          </button>
          <span *ngIf="i === 3 && pagination.totalPages > 5 && !viewMode.includes('card')"
            class="px-2 py-2 text-gray-500 hidden sm:inline-block">...</span>
        </ng-container>

        <button [disabled]="pagination.pageNumber === pagination.totalPages - 1"
          [ngClass]="pagination.pageNumber === pagination.totalPages - 1 ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:bg-gray-100'"
          class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white"
          (click)="changePage(pagination.pageNumber + 1)">
          <fa-icon [icon]="['fas', 'chevron-right']" class="text-xs"></fa-icon>
        </button>
      </div>
    </nav>
  </div>
</div>



<!-- Ticket Detail Popup -->
<app-admin-support-detail
  *ngIf="selectedTicketId !== null"
  [ticketId]="selectedTicketId"
  (close)="closeTicketDetail()">
</app-admin-support-detail>
