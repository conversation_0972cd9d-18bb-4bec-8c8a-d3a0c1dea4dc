.overlay-black {
  @apply flex justify-center items-center fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 z-[var(--z-modal)];
}

.modal-container {
  @apply w-full max-w-md mx-auto;
}

.modal-content {
  @apply bg-white rounded-2xl shadow-lg overflow-hidden p-4;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.modal-header {
  @apply flex justify-between items-center;
  position: relative;
}

.modal-title {
  @apply text-xl font-semibold text-[var(--gray-800)];
}

.close-button {
  @apply p-1 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors;
  position: absolute;
  top: 0;
  right: 0;
}

.dropdown-container {
  @apply w-full my-3;
}

.category-dropdown-button {
  @apply rounded-lg border-2 border-[#30B0C7] bg-white text-[var(--gray-800)] cursor-pointer p-3;
}

.category-dropdown-container {
  @apply w-full;
}

/* Style for the dropdown list when open */
:host ::ng-deep .absolute.max-h-96 {
  @apply border-2 border-[#30B0C7] rounded-lg shadow-lg;
}

.loading-spinner {
  @apply flex justify-center items-center py-8;
}

.spinner {
  @apply w-8 h-8 border-4 border-[var(--primary)] border-t-transparent rounded-full animate-spin;
}

.empty-state {
  @apply flex flex-col items-center justify-center py-8 text-gray-500;
}

.save-button {
  @apply w-full bg-[var(--primary)] text-white font-medium py-3 px-4 rounded-lg hover:bg-[var(--primary-hover)]  transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed mt-4;
}
