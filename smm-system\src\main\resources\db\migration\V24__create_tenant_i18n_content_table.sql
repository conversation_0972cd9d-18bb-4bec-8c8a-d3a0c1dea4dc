-- Create tenant_i18n_content table for storing custom translations per tenant
CREATE TABLE tenant_i18n_content (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(36) NOT NULL,
    language_code VARCHAR(10) NOT NULL,
    translation_key VARCHAR(500) NOT NULL,
    translation_value TEXT,
    description VARCHAR(1000),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Unique constraint to prevent duplicate keys per tenant and language
    CONSTRAINT uk_tenant_i18n_content UNIQUE (tenant_id, language_code, translation_key)
);

-- Create indexes for better performance
CREATE INDEX idx_tenant_i18n_content_tenant_lang ON tenant_i18n_content(tenant_id, language_code);
CREATE INDEX idx_tenant_i18n_content_key ON tenant_i18n_content(translation_key);
CREATE INDEX idx_tenant_i18n_content_active ON tenant_i18n_content(is_active);

-- Add comments
COMMENT ON TABLE tenant_i18n_content IS 'Stores custom i18n translations for each tenant';
COMMENT ON COLUMN tenant_i18n_content.tenant_id IS 'Reference to tenant ID';
COMMENT ON COLUMN tenant_i18n_content.language_code IS 'Language code (e.g., en, vi, cn)';
COMMENT ON COLUMN tenant_i18n_content.translation_key IS 'Translation key in dot notation (e.g., nav.services)';
COMMENT ON COLUMN tenant_i18n_content.translation_value IS 'Translated text value';

COMMENT ON COLUMN tenant_i18n_content.description IS 'Optional description for this translation set';
