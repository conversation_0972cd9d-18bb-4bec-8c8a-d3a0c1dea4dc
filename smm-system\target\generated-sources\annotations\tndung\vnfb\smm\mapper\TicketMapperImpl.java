package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.dto.request.TicketReq;
import tndung.vnfb.smm.dto.response.SuperTicketRes;
import tndung.vnfb.smm.dto.response.TicketRes;
import tndung.vnfb.smm.entity.Ticket;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class TicketMapperImpl implements TicketMapper {

    @Override
    public Ticket toEntity(TicketReq dto) {
        if ( dto == null ) {
            return null;
        }

        Ticket ticket = new Ticket();

        ticket.setDescription( dto.getDescription() );
        ticket.setSubject( dto.getSubject() );

        return ticket;
    }

    @Override
    public TicketRes toRes(Ticket entity) {
        if ( entity == null ) {
            return null;
        }

        TicketRes ticketRes = new TicketRes();

        ticketRes.setCreatedAt( entity.getCreatedAt() );
        ticketRes.setDescription( entity.getDescription() );
        ticketRes.setId( entity.getId() );
        ticketRes.setStatus( entity.getStatus() );
        ticketRes.setSubject( entity.getSubject() );
        ticketRes.setUpdatedAt( entity.getUpdatedAt() );

        return ticketRes;
    }

    @Override
    public SuperTicketRes toSuperRes(Ticket entity) {
        if ( entity == null ) {
            return null;
        }

        SuperTicketRes superTicketRes = new SuperTicketRes();

        superTicketRes.setCreatedAt( entity.getCreatedAt() );
        superTicketRes.setDescription( entity.getDescription() );
        superTicketRes.setId( entity.getId() );
        superTicketRes.setStatus( entity.getStatus() );
        superTicketRes.setSubject( entity.getSubject() );
        superTicketRes.setUpdatedAt( entity.getUpdatedAt() );
        superTicketRes.setCreatedBy( entity.getCreatedBy() );

        return superTicketRes;
    }

    @Override
    public List<SuperTicketRes> toSuperRes(List<Ticket> entities) {
        if ( entities == null ) {
            return null;
        }

        List<SuperTicketRes> list = new ArrayList<SuperTicketRes>( entities.size() );
        for ( Ticket ticket : entities ) {
            list.add( toSuperRes( ticket ) );
        }

        return list;
    }

    @Override
    public List<TicketRes> toRes(List<Ticket> entities) {
        if ( entities == null ) {
            return null;
        }

        List<TicketRes> list = new ArrayList<TicketRes>( entities.size() );
        for ( Ticket ticket : entities ) {
            list.add( toRes( ticket ) );
        }

        return list;
    }

    @Override
    public void updateEntityFromDto(TicketReq dto, Ticket entity) {
        if ( dto == null ) {
            return;
        }

        entity.setDescription( dto.getDescription() );
        entity.setSubject( dto.getSubject() );
    }
}
