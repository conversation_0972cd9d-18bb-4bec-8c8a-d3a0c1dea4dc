import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from './config.service';

export interface TenantI18nContentReq {
  language_code: string;
  translations: { [key: string]: string };
  description?: string;
}

export interface TenantI18nContentRes {
  language_code: string;
  translations: { [key: string]: string };
  description?: string;
  last_modified?: string;
  modified_by?: string;
  total_keys: number;
  customized_keys: number;
}

@Injectable({
  providedIn: 'root'
})
export class TenantI18nService {

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {}

  /**
   * Get translations for a specific language
   */
  getI18nContent(languageCode: string): Observable<TenantI18nContentRes> {
    return this.http.get<TenantI18nContentRes>(`${this.configService.apiUrl}/tenant-settings/i18n/${languageCode}`);
  }

  /**
   * Update translations for a specific language
   */
  updateI18nContent(languageCode: string, request: TenantI18nContentReq): Observable<TenantI18nContentRes> {
    return this.http.put<TenantI18nContentRes>(`${this.configService.apiUrl}/tenant-settings/i18n/${languageCode}`, request);
  }

  /**
   * Delete translations for a specific language
   */
  deleteI18nContent(languageCode: string): Observable<void> {
    return this.http.delete<void>(`${this.configService.apiUrl}/tenant-settings/i18n/${languageCode}`);
  }

  /**
   * Get all available language codes for current tenant
   */
  getAvailableLanguageCodes(): Observable<string[]> {
    return this.http.get<string[]>(`${this.configService.apiUrl}/tenant-settings/i18n/languages`);
  }

  /**
   * Get default template for new language
   */
  getDefaultTemplate(): Observable<{ [key: string]: any }> {
    return this.http.get<{ [key: string]: any }>(`${this.configService.apiUrl}/tenant-settings/i18n/template`);
  }

  /**
   * Get template for specific language
   */
  getTemplateForLanguage(languageCode: string): Observable<{ [key: string]: any }> {
    return this.http.get<{ [key: string]: any }>(`${this.configService.apiUrl}/tenant-settings/i18n/template/${languageCode}`);
  }

  /**
   * Import translations from JSON
   */
  importTranslations(languageCode: string, translations: { [key: string]: string }): Observable<TenantI18nContentRes> {
    return this.http.post<TenantI18nContentRes>(`${this.configService.apiUrl}/tenant-settings/i18n/${languageCode}/import`, translations);
  }

  /**
   * Get tenant language settings
   */
  getLanguageSettings(): Observable<any> {
    return this.http.get<any>(`${this.configService.apiUrl}/tenant-settings/language`);
  }

  /**
   * Update tenant language settings
   */
  updateLanguageSettings(request: any): Observable<any> {
    return this.http.put<any>(`${this.configService.apiUrl}/tenant-settings/language`, request);
  }
}
