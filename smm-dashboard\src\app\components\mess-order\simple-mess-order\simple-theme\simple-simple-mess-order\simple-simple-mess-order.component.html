<!-- Simple Theme for Simple Mess Order - Clean & Minimal Design -->
<div class="simple-simple-mess-order-container" *ngIf="messOrderFormState$ | async as messOrderFormState">

  <!-- Header Section -->
  <div class="page-header">
    <h2 class="page-title">{{ 'mass_order.simple_order' | translate }}</h2>
    <p class="page-description">{{ 'simple_theme.mass_order.bulk_description' | translate }}</p>
  </div>

  <!-- Order Form -->
  <div class="form-container">
    <form [formGroup]="orderForm" (ngSubmit)="createOrder()" class="order-form">

      <!-- Form Fields -->
      <div class="form-grid">

        <!-- Category Selection -->
        <div class="form-group">
          <label class="form-label">{{ 'mass_order.list' | translate }}</label>
          <app-simple-icon-dropdown
            [options]="categories"
            [selectedOption]="selectedCategory"
            (selected)="onCategorySelected($event)"
            [customClassDropdown]="'dropdown-select'">
          </app-simple-icon-dropdown>
          <div *ngIf="orderForm.get('category')?.invalid && orderForm.get('category')?.touched" class="error-message">
            {{ 'mass_order.category_required' | translate }}
          </div>
        </div>

        <!-- Service Selection -->
        <div class="form-group">
          <label class="form-label">{{ 'service' | translate }}</label>
          <app-simple-service-dropdown
            [options]="services"
            [customClassDropdown]="'dropdown-select'"
            customClassButton="'dropdown-btn'"
            (selected)="onServiceSelected($event)">
          </app-simple-service-dropdown>
          <div *ngIf="orderForm.get('service')?.invalid && orderForm.get('service')?.touched" class="error-message">
            {{ 'mass_order.service_required' | translate }}
          </div>
        </div>

      </div>

      <!-- Content Section -->
      <div class="form-group full-width">
        <label class="form-label">{{ 'mass_order.content' | translate }}</label>
        <p class="form-description">{{ 'mass_order.enter_links_one_per_line' | translate }}</p>

        <textarea
          formControlName="content"
          class="form-textarea"
          placeholder="{{ 'mass_order.content_placeholder' | translate }}"
          rows="6">
        </textarea>

        <div class="content-info">
          <span class="link-counter">{{ 'mass_order.links_count' | translate }}: {{ getLinkCount() }}</span>
        </div>

        <div *ngIf="orderForm.get('content')?.invalid && orderForm.get('content')?.touched" class="error-message">
          <div *ngIf="orderForm.get('content')?.errors?.['required']">{{ 'mass_order.content_required' | translate }}</div>
          <div *ngIf="orderForm.get('content')?.errors?.['minlength']">{{ 'mass_order.content_min_length' | translate }}</div>
        </div>
      </div>

      <!-- Quantity Section -->
      <div class="form-group">
        <label class="form-label">{{ 'quantity' | translate }}</label>

        <input
          type="number"
          formControlName="quantity"
          class="form-input"
          placeholder="{{ 'mass_order.enter_quantity' | translate }}"
          min="1"
        />

        <div class="quantity-limits">
          Min: {{ selectedServiceObj?.min || 10 }} - Max: {{ selectedServiceObj?.max || 5000000 }}
        </div>

        <div *ngIf="orderForm.get('quantity')?.invalid && orderForm.get('quantity')?.touched" class="error-message">
          <div *ngIf="orderForm.get('quantity')?.errors?.['required']">{{ 'mass_order.quantity_required' | translate }}</div>
          <div *ngIf="orderForm.get('quantity')?.errors?.['min']">{{ 'mass_order.quantity_min' | translate }} {{ selectedServiceObj?.min || 10 }}</div>
          <div *ngIf="orderForm.get('quantity')?.errors?.['max']">{{ 'mass_order.quantity_max' | translate }} {{ selectedServiceObj?.max || 5000000 }}</div>
        </div>
      </div>

      <!-- Price and Submit Section -->
      <div class="form-group">
        <div class="price-section">
          <div class="price-card">
            <span class="price-label">{{ 'mass_order.total_price' | translate }}</span>
            <span class="price-amount">{{ price | currencyConvert }}</span>
          </div>
        </div>

        <button type="submit" class="submit-button" [disabled]="orderForm.invalid || isProcessing">
          <span *ngIf="!isProcessing">{{ 'mass_order.create_order' | translate }}</span>
          <span *ngIf="isProcessing">{{ 'mass_order.processing' | translate }}</span>
        </button>
      </div>

    </form>
  </div>

  <!-- Results Section -->
  <div *ngIf="showResults" class="results-container">
    <div class="results-header">
      <h3 class="results-title">{{ 'mass_order.order_results' | translate }}</h3>
      <div class="results-summary">
        <div class="summary-item success">
          <span class="summary-count">{{ successCount }}</span>
          <span class="summary-label">{{ 'simple_theme.mass_order.result_successful' | translate }}</span>
        </div>
        <div class="summary-item failed">
          <span class="summary-count">{{ failedCount }}</span>
          <span class="summary-label">{{ 'simple_theme.mass_order.result_failed' | translate }}</span>
        </div>
      </div>
    </div>

    <div class="results-list">
      <div *ngFor="let result of orderResults"
           class="result-item"
           [class.success]="result.success"
           [class.failed]="!result.success">
        <div class="result-content">
          <span class="result-link">{{ result.link }}</span>
          <span class="result-status" [class.success]="result.success" [class.failed]="!result.success">
            {{ result.success ? ('simple_theme.mass_order.result_successful' | translate) : ('simple_theme.mass_order.result_failed' | translate) }}
          </span>
        </div>
        <div *ngIf="!result.success" class="result-message">
          {{ result.message }}
        </div>
      </div>
    </div>
  </div>

</div>
