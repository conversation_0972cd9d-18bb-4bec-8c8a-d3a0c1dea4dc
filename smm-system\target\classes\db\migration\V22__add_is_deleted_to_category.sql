-- Add is_deleted column to category table for soft delete functionality
DO $$
BEGIN
    -- Check if the column exists
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'category' 
        AND column_name = 'is_deleted'
    ) THEN
        -- Add the column
        ALTER TABLE category ADD COLUMN is_deleted BOOLEAN NOT NULL DEFAULT FALSE;
        
        -- Create index for better performance on queries filtering by is_deleted
        CREATE INDEX IF NOT EXISTS idx_category_is_deleted ON category(is_deleted);
        
        -- Create composite index for tenant_id and is_deleted for better performance
        CREATE INDEX IF NOT EXISTS idx_category_tenant_is_deleted ON category(tenant_id, is_deleted);
        
        -- Add comment
        COMMENT ON COLUMN category.is_deleted IS 'Soft delete flag - true if category is deleted, false if active';
    END IF;
END $$;
