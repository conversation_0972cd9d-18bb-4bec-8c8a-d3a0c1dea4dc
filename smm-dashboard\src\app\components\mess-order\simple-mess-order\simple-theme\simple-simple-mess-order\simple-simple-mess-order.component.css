/* Simple Simple Mess Order Theme - Clean & Minimal Design */
.simple-simple-mess-order-container {
  padding: 1.5rem;
  background-color: #f8fafc;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Header Section */
.page-header {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  text-align: center;
}

.page-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.5rem 0;
}

.page-description {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
}

/* Form Container */
.form-container {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.order-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Form Grid */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

/* Form Groups */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-label {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.form-description {
  color: #6b7280;
  font-size: 0.75rem;
  margin: 0;
}

/* Form Inputs */
.form-input,
.form-textarea {
  width: 100%;
  padding: 0.625rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
  background: white;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

/* Dropdown Styles */
.dropdown-select {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  transition: border-color 0.2s ease;
}

.dropdown-select:focus-within {
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.dropdown-btn {
  padding: 0.625rem 0.75rem;
  font-size: 0.875rem;
}

/* Content Info */
.content-info {
  margin-top: 0.5rem;
}

.link-counter {
  font-size: 0.75rem;
  color: #6b7280;
}

/* Quantity Limits */
.quantity-limits {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

/* Price Section */
.price-section {
  margin-bottom: 1rem;
}

.price-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f7fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.price-label {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.price-amount {
  font-weight: 600;
  color: #4299e1;
  font-size: 1rem;
}

/* Submit Button */
.submit-button {
  width: 100%;
  padding: 0.75rem 1rem;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.submit-button:hover:not(:disabled) {
  background: #3182ce;
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Error Messages */
.error-message {
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

/* Results Section */
.results-container {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.results-header {
  margin-bottom: 1rem;
}

.results-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 1rem 0;
}

.results-summary {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
}

.summary-item.success {
  background: #d1fae5;
  color: #065f46;
}

.summary-item.failed {
  background: #fee2e2;
  color: #991b1b;
}

.summary-count {
  font-weight: 600;
}

.summary-label {
  font-weight: 500;
}

/* Results List */
.results-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
}

.result-item {
  padding: 0.75rem;
  border-bottom: 1px solid #e2e8f0;
  font-size: 0.875rem;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item.success {
  background: #f0fdf4;
}

.result-item.failed {
  background: #fef2f2;
}

.result-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.result-link {
  font-weight: 500;
  color: #374151;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 1rem;
}

.result-status {
  font-weight: 500;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.result-status.success {
  background: #10b981;
  color: white;
}

.result-status.failed {
  background: #ef4444;
  color: white;
}

.result-message {
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .simple-simple-mess-order-container {
    padding: 1rem;
  }

  .page-header,
  .form-container,
  .results-container {
    padding: 1rem;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .results-summary {
    flex-direction: column;
    gap: 0.5rem;
  }

  .result-content {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .result-link {
    margin-right: 0;
  }
}
