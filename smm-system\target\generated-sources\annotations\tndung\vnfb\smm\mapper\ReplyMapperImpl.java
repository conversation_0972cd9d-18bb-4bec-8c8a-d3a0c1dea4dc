package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.dto.request.ReplyReq;
import tndung.vnfb.smm.dto.response.ReplyRes;
import tndung.vnfb.smm.entity.Reply;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class ReplyMapperImpl implements ReplyMapper {

    @Override
    public Reply toEntity(ReplyReq req) {
        if ( req == null ) {
            return null;
        }

        Reply reply = new Reply();

        reply.setContent( req.getContent() );

        return reply;
    }

    @Override
    public ReplyRes toRes(Reply entity) {
        if ( entity == null ) {
            return null;
        }

        ReplyRes replyRes = new ReplyRes();

        replyRes.setContent( entity.getContent() );
        replyRes.setCreatedAt( entity.getCreatedAt() );
        replyRes.setRepliedBy( entity.getRepliedBy() );

        return replyRes;
    }

    @Override
    public List<ReplyRes> toRes(List<Reply> entities) {
        if ( entities == null ) {
            return null;
        }

        List<ReplyRes> list = new ArrayList<ReplyRes>( entities.size() );
        for ( Reply reply : entities ) {
            list.add( toRes( reply ) );
        }

        return list;
    }
}
