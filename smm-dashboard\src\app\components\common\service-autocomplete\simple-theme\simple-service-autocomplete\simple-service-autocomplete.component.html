<!-- Simple Theme Service Autocomplete -->
<div *ngIf="isVisible" class="simple-autocomplete-container">
  <div class="simple-autocomplete-list">
    <div *ngFor="let service of services; let i = index" 
         (click)="onSelectService(service)"
         class="simple-autocomplete-item"
         [ngClass]="{'simple-highlighted': i === highlightedIndex}">
      <div class="simple-service-content">
        <app-service-label [service]="service"></app-service-label>
      </div>
    </div>
    <div *ngIf="services.length === 0" class="simple-no-results">
      <i class="fas fa-search simple-search-icon"></i>
      <span>Không tìm thấy dịch vụ phù hợp</span>
    </div>
  </div>
</div>
