import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { RouterModule } from '@angular/router';

// Base component
import { BaseSignUpComponent } from '../../base-sign-up.component';

// Services
import { SignUpLogicService } from '../../services/sign-up-logic.service';

@Component({
  selector: 'app-simple-sign-up',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, TranslateModule, RouterModule],
  templateUrl: './simple-sign-up.component.html',
  styleUrl: './simple-sign-up.component.css'
})
export class SimpleSignUpComponent extends BaseSignUpComponent {
  constructor(signUpLogicService: SignUpLogicService) {
    super(signUpLogicService);
  }
}
