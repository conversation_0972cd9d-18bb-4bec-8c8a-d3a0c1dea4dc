<div class="simple-update-container">
  <div class="simple-header">
    <h1 class="simple-title">{{ 'update.title' | translate }}</h1>
  </div>

  <!-- Search and filter section -->
  <div class="simple-controls">
    <!-- Search box -->
    <div class="simple-search-wrapper">
      <app-simple-search-box
        #searchBox
        placeholder="{{ 'update.search' | translate }}"
        buttonText="{{ 'update.search' | translate }}"
        buttonPosition="right"
        containerClass="simple-search-container"
        inputClass="simple-search-input"
        buttonClass="simple-search-button"
        (searchEvent)="onSearch($event)"
      ></app-simple-search-box>
    </div>

    <!-- Filter dropdown -->
    <div class="simple-filter-wrapper">
      <app-lite-dropdown
        #filterDropdown
        [customClassButton]="'simple-filter-button'"
        customClassDropdown="simple-filter-dropdown"
        [options]="['update.all' | translate, 'update.price_increase' | translate, 'update.price_decrease' | translate, 'update.new' | translate, 'update.on' | translate, 'update.off' | translate]"
        (selected)="onFilterSelected($event)"
      ></app-lite-dropdown>
    </div>
  </div>

  <!-- Loading indicator -->
  <app-loading *ngIf="loading" [size]="'md'" class="simple-loading"></app-loading>

  <!-- Content area -->
  <div class="simple-content" *ngIf="!loading">
    
    <!-- Desktop table view -->
    <div class="simple-table-wrapper desktop-only">
      <table class="simple-table" *ngIf="updateLogs.length > 0">
        <thead class="simple-table-header">
          <tr>
            <th class="simple-th simple-th-service">{{ 'update.service' | translate }}</th>
            <th class="simple-th simple-th-time">{{ 'update.time' | translate }}</th>
            <th class="simple-th simple-th-status">{{ 'update.status' | translate }}</th>
          </tr>
        </thead>
        <tbody class="simple-table-body">
          <tr *ngFor="let log of updateLogs" class="simple-table-row">
            <td class="simple-td simple-td-service">
              <div class="simple-service-name">{{ log.service }}</div>
            </td>
            <td class="simple-td simple-td-time">
              <div class="simple-date">{{ formatDate(log.created_at).date }}</div>
              <div class="simple-time">{{ formatDate(log.created_at).time }}</div>
            </td>
            <td class="simple-td simple-td-status">
              <div class="simple-status-wrapper">
                <span [ngClass]="'simple-status-badge ' + getSimpleStatusClass(log.status)">
                  {{ 'update.' + statusMap[log.status] | translate }}
                  <span *ngIf="shouldShowPriceChange(log.status) && formatPriceChange(log.price_change_from, log.price_change_to)" class="simple-price-change">
                    {{ formatPriceChange(log.price_change_from, log.price_change_to) }}
                  </span>
                </span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- No data message for desktop -->
      <div *ngIf="updateLogs.length === 0" class="simple-no-data">
        {{ 'update.no_data' | translate }}
      </div>
    </div>

    <!-- Mobile card view -->
    <div class="simple-cards-wrapper mobile-only">
      <!-- No data message for mobile -->
      <div *ngIf="updateLogs.length === 0" class="simple-no-data">
        {{ 'update.no_data' | translate }}
      </div>

      <!-- Mobile cards -->
      <div *ngFor="let log of updateLogs" class="simple-card">
        <div class="simple-card-header">
          <div class="simple-card-service">{{ log.service }}</div>
        </div>
        
        <div class="simple-card-body">
          <div class="simple-card-time">
            <div class="simple-card-date">{{ formatDate(log.created_at).date }}</div>
            <div class="simple-card-time-value">{{ formatDate(log.created_at).time }}</div>
          </div>
          
          <div class="simple-card-status">
            <span [ngClass]="'simple-status-badge ' + getSimpleStatusClass(log.status)">
              {{ 'update.' + statusMap[log.status] | translate }}
              <span *ngIf="shouldShowPriceChange(log.status) && formatPriceChange(log.price_change_from, log.price_change_to)" class="simple-price-change">
                {{ formatPriceChange(log.price_change_from, log.price_change_to) }}
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Pagination -->
  <div *ngIf="!loading && updateLogs.length > 0" class="simple-pagination">
    <!-- Info text -->
    <div class="simple-pagination-info">
      {{ 'update.showing' | translate }} {{ currentPage * itemsPerPage + 1 }} {{ 'update.to' | translate }} {{ (currentPage + 1) * itemsPerPage > totalItems ? totalItems : (currentPage + 1) * itemsPerPage }} {{ 'update.of' | translate }} {{ totalItems }} {{ 'update.entries' | translate }}
    </div>

    <!-- Page controls -->
    <div class="simple-pagination-controls">
      <button
        (click)="prevPage()"
        [disabled]="currentPage === 0"
        class="simple-pagination-button simple-pagination-prev"
        [ngClass]="{'simple-pagination-disabled': currentPage === 0}"
      >
        ‹
      </button>

      <button
        *ngFor="let page of pages.slice(0, getVisiblePageCount())"
        (click)="goToPage(page)"
        [ngClass]="page === currentPage + 1 ? 'simple-pagination-button simple-pagination-active' : 'simple-pagination-button'"
      >
        {{ page }}
      </button>

      <span *ngIf="pages.length > getEllipsisThreshold()" class="simple-pagination-ellipsis">
        ...
      </span>

      <button
        *ngIf="pages.length > getEllipsisThreshold()"
        (click)="goToPage(pages[pages.length - 1])"
        [ngClass]="pages[pages.length - 1] === currentPage + 1 ? 'simple-pagination-button simple-pagination-active' : 'simple-pagination-button'"
      >
        {{ pages[pages.length - 1] }}
      </button>

      <button
        (click)="nextPage()"
        [disabled]="currentPage >= pages.length - 1"
        class="simple-pagination-button simple-pagination-next"
        [ngClass]="{'simple-pagination-disabled': currentPage >= pages.length - 1}"
      >
        ›
      </button>
    </div>

    <!-- Entries per page selector -->
    <div class="simple-pagination-size">
      <span class="simple-pagination-size-label">{{ 'update.show' | translate }}</span>
      <select
        [(ngModel)]="itemsPerPage"
        (change)="updatePagination()"
        class="simple-pagination-select"
      >
        <option [value]="10">10</option>
        <option [value]="25">25</option>
        <option [value]="50">50</option>
        <option [value]="100">100</option>
      </select>
    </div>
  </div>
</div>
