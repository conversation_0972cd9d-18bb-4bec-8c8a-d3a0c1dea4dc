import { Component, Input, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { Observable } from 'rxjs';

// Services
import { SidebarLogicService, SidebarState } from '../../services/sidebar-logic.service';

// Components
import { IconsModule } from '../../../../icons/icons.module';
import { SvgIconComponent } from '../../../common/svg-icon/svg-icon.component';

@Component({
  selector: 'app-simple-sidebar',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule,
    IconsModule,
    SvgIconComponent
  ],
  templateUrl: './simple-sidebar.component.html',
  styleUrls: ['./simple-sidebar.component.css']
})
export class SimpleSidebarComponent implements OnInit, OnD<PERSON>roy {
  @Input() isOpen: boolean = false;

  // Sidebar logic state
  sidebarState$: Observable<SidebarState>;

  constructor(private sidebarLogicService: SidebarLogicService) {
    this.sidebarState$ = this.sidebarLogicService.state$;
  }

  ngOnInit(): void {
    // SidebarLogicService handles all initialization
    // Pass the isOpen input to the service
    this.sidebarLogicService.setIsOpen(this.isOpen);
  }

  ngOnDestroy(): void {
    // SidebarLogicService is singleton, no cleanup needed
  }

  // Delegate methods to SidebarLogicService for template compatibility
  toggleSidebar(): void {
    this.sidebarLogicService.toggleSidebar();
  }

  navigateTo(link: string): void {
    this.sidebarLogicService.navigateTo(link);
  }

  handleMenuClick(event: MouseEvent, link: string): void {
    this.sidebarLogicService.handleMenuClick(event, link);
  }

  onLogoClick(): void {
    this.sidebarLogicService.onLogoClick();
  }
}
