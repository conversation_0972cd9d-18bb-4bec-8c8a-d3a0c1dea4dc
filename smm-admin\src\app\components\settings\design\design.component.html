<div class="design-settings">
  <div class="settings-header">
    <h1 class="settings-title">{{ 'settings.design.title' | translate }}</h1>
    <p class="settings-description">{{ 'settings.design.description' | translate }}</p>
  </div>

  <!-- Logo & Favicon Section -->
  <section class="settings-card">
    <div class="card-header">
      <h2 class="card-title">
        <fa-icon [icon]="['fas', 'image']" class="section-icon"></fa-icon>
        {{ 'settings.design.logo_favicon.title' | translate }}
      </h2>
    </div>

    <div class="card-content">
      <!-- Logo Upload -->
      <div class="upload-item">
        <div class="upload-item-header">
          <label class="upload-label">{{ 'settings.design.logo_favicon.logo' | translate }}</label>
          <div class="upload-format">.png or .jpg 320×128</div>
        </div>
        <div class="upload-container">
          <div class="preview-box light-bg">
            <span *ngIf="!logoPreviewUrl" class="preview-placeholder">NEWPANEL</span>
            <img *ngIf="logoPreviewUrl" [src]="logoPreviewUrl" alt="Logo preview" class="preview-image" />
          </div>
          <div class="upload-actions">
            <button class="upload-button">
              <fa-icon [icon]="['fas', 'upload']"></fa-icon>
              <span>{{ 'settings.design.logo_favicon.upload' | translate }}</span>
              <input type="file" (change)="onLogoFileSelected($event)" accept="image/png,image/jpeg" class="hidden-input" />
            </button>
            <div *ngIf="logoUploading" class="upload-progress">
              <div class="progress-bar" [style.width.%]="uploadProgress"></div>
            </div>
            <div *ngIf="logoUploadSuccess" class="upload-success">
              <fa-icon [icon]="['fas', 'check-circle']"></fa-icon>
              <span>{{ 'settings.design.logo_favicon.upload_successful' | translate }}</span>
            </div>
            <div *ngIf="logoUploadError" class="upload-error">
              <fa-icon [icon]="['fas', 'exclamation-circle']"></fa-icon>
              <span>{{ logoUploadError }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Favicon Upload -->
      <div class="upload-item">
        <div class="upload-item-header">
          <label class="upload-label">{{ 'settings.design.logo_favicon.favicon' | translate }}</label>
          <div class="upload-format">ico 16×16</div>
        </div>
        <div class="upload-container">
          <div class="favicon-container">
            <div class="favicon-preview">
              <div class="favicon-box">
                <span *ngIf="!faviconPreviewUrl" class="favicon-placeholder">ico</span>
                <img *ngIf="faviconPreviewUrl" [src]="faviconPreviewUrl" alt="Favicon preview" class="favicon-image" />
              </div>
              <div class="favicon-url-container">
                <input type="text" [value]="currentDomain" class="favicon-url" readonly />
                <div class="favicon-actions">
                  <button class="action-button" [title]="'settings.design.logo_favicon.copy_url' | translate" (click)="copyFaviconUrl()">
                    <fa-icon [icon]="['fas', 'copy']"></fa-icon>
                  </button>
                </div>
              </div>
            </div>
            <div class="upload-actions">
              <button class="upload-button">
                <fa-icon [icon]="['fas', 'upload']"></fa-icon>
                <span>{{ 'settings.design.logo_favicon.upload' | translate }}</span>
                <input type="file" (change)="onFaviconFileSelected($event)" accept="image/x-icon" class="hidden-input" />
              </button>
              <div *ngIf="faviconUploading" class="upload-progress">
                <div class="progress-bar" [style.width.%]="uploadProgress"></div>
              </div>
              <div *ngIf="faviconUploadSuccess" class="upload-success">
                <fa-icon [icon]="['fas', 'check-circle']"></fa-icon>
                <span>{{ 'settings.design.logo_favicon.upload_successful' | translate }}</span>
              </div>
              <div *ngIf="faviconUploadError" class="upload-error">
                <fa-icon [icon]="['fas', 'exclamation-circle']"></fa-icon>
                <span>{{ faviconUploadError }}</span>
              </div>
            </div>
            <div class="choose-favicon-library flex justify-center my-3">
              <button type="button"
                      class="upload-button"
                      (click)="openFaviconPopup()">
                <fa-icon [icon]="['fas', 'th-large']" class="text-lg"></fa-icon>
                <span>{{ 'settings.design.logo_favicon.choose_from_library' | translate }}</span>
              </button>
            </div>
            <!-- Popup chọn favicon -->
            <div *ngIf="showFaviconPopup" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" (click)="closeFaviconPopup()">
              <div class="bg-white rounded-xl shadow-lg p-6 max-w-2xl w-full relative" (click)="$event.stopPropagation()">
                <button class="absolute top-3 right-3 text-gray-500 hover:text-gray-700" (click)="closeFaviconPopup()">
                  <fa-icon [icon]="['fas', 'times']"></fa-icon>
                </button>
                <h3 class="text-lg font-semibold mb-4">{{ 'settings.design.logo_favicon.choose_favicon_from_library' | translate }}</h3>
                <div class="grid grid-cols-6 gap-4">
                  <img *ngFor="let icon of builtInFaviconIcons"
                       [src]="icon"
                       (click)="selectBuiltInFavicon(icon)"
                       [class.selected]="faviconPreviewUrl === icon"
                       style="width:40px;height:40px;cursor:pointer;border-radius:8px;border:2px solid #eee;transition:border 0.2s;"
                       [style.border-color]="faviconPreviewUrl === icon ? '#30B0C7' : '#eee'">
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Colors Section -->
  <section class="settings-card">
    <div class="card-header">
      <h2 class="card-title">
        <fa-icon [icon]="['fas', 'palette']" class="section-icon"></fa-icon>
        {{ 'settings.design.colors.title' | translate }}
      </h2>
    </div>

    <div class="card-content">
      <!-- Text on Buttons -->
      <!-- <div class="color-item">
        <label class="color-label">Text on buttons</label>
        <div class="color-options">
          <div *ngFor="let color of textButtonColors"
               class="color-option"
               [style.background-color]="color.value"
               [class.selected]="color.selected"
               (click)="selectTextButtonColor(color)">
            <fa-icon *ngIf="color.selected" [icon]="['fas', 'check']" class="check-icon"></fa-icon>
          </div>
        </div>
      </div> -->

      <!-- Primary Color -->
      <div class="color-item">
        <div class="color-header">
          <label class="color-label">{{ 'settings.design.colors.primary_color' | translate }}</label>
          <button class="custom-color-btn" (click)="toggleCustomColorInput()">
            <fa-icon [icon]="['fas', showCustomColorInput ? 'times' : 'plus']"></fa-icon>
            {{ showCustomColorInput ? ('settings.design.colors.cancel' | translate) : ('settings.design.colors.custom' | translate) }}
          </button>
        </div>

        <!-- Predefined Colors -->
        <div class="color-options" *ngIf="!showCustomColorInput">
          <div *ngFor="let color of primaryColors"
               class="color-option"
               [style.background-color]="color.value"
               [class.selected]="color.selected"
               (click)="selectPrimaryColor(color)">
            <fa-icon *ngIf="color.selected" [icon]="['fas', 'check']" class="check-icon"></fa-icon>
          </div>
        </div>

        <!-- Custom Color Input -->
        <div class="custom-color-input" *ngIf="showCustomColorInput">
          <input type="text"
                 [(ngModel)]="customPrimaryColor"
                 placeholder="var(--primary)"
                 class="hex-input"
                 pattern="^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$">
          <button class="apply-color-btn" (click)="applyCustomColor()">
            <fa-icon [icon]="['fas', 'check']"></fa-icon>
            {{ 'settings.design.colors.apply' | translate }}
          </button>
        </div>

        <!-- Color Preview -->
        <div class="color-preview">
          <div class="preview-swatch" [style.background-color]="getSelectedPrimaryColor()"></div>
          <span class="preview-value">{{ getSelectedPrimaryColor() }}</span>
        </div>
      </div>

      <!-- Preview Section (moved below color section) -->
      <div class="preview-container mt-6">
        <h3 class="text-lg font-medium mb-4">{{ 'settings.design.colors.preview' | translate }}</h3>
        <div class="preview-item">
          <span class="preview-label">{{ 'settings.design.colors.active_link' | translate }}</span>
          <a href="javascript:void(0)" class="active-link-preview"
             [ngStyle]="{'color': getSelectedPrimaryColor()}">
            {{ 'settings.design.colors.link_text' | translate }}
          </a>
        </div>
        <div class="preview-item">
          <span class="preview-label">{{ 'settings.design.colors.button' | translate }}</span>
          <button class="button-preview"
                  [ngStyle]="{
                    'background-color': getSelectedPrimaryColor(),
                    'color': getSelectedTextButtonColor()
                  }">
            <fa-icon [icon]="['fas', 'shopping-cart']"></fa-icon>
            {{ 'settings.design.colors.button_text' | translate }}
          </button>
        </div>
        <div class="preview-item">
          <span class="preview-label">{{ 'settings.design.colors.icon' | translate }}</span>
          <div class="icon-preview"
               [ngStyle]="{
                 'background-color': getSelectedPrimaryColor(),
                 'color': getSelectedTextButtonColor()
               }">
            <fa-icon [icon]="['fas', 'shopping-cart']"></fa-icon>
          </div>
        </div>
        <div class="preview-item">
          <span class="preview-label">{{ 'settings.design.colors.primary_button' | translate }}</span>
          <button class="primary-button-preview"
                  [ngStyle]="{
                    'background-color': getSelectedPrimaryColor(),
                    'color': '#FFFFFF'
                  }">
            <fa-icon [icon]="['fas', 'check']"></fa-icon>
            {{ 'settings.design.colors.primary_button' | translate }}
          </button>
        </div>
        <div class="preview-item">
          <span class="preview-label">{{ 'settings.design.colors.primary_elements' | translate }}</span>
          <div class="primary-elements-preview">
            <div class="element" [ngStyle]="{'background-color': getSelectedPrimaryColor()}"></div>
            <div class="element" [ngStyle]="{'background-color': getSelectedPrimaryColor(), 'opacity': 0.8}"></div>
            <div class="element" [ngStyle]="{'background-color': getSelectedPrimaryColor(), 'opacity': 0.6}"></div>
            <div class="element" [ngStyle]="{'background-color': getSelectedPrimaryColor(), 'opacity': 0.4}"></div>
            <div class="element" [ngStyle]="{'background-color': getSelectedPrimaryColor(), 'opacity': 0.2}"></div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Header Style Section -->
  <section class="settings-card">
    <div class="card-header">
      <h2 class="card-title">
        <fa-icon [icon]="['fas', 'window-maximize']" class="section-icon"></fa-icon>
        {{ 'settings.design.header_style.title' | translate }}
      </h2>
    </div>

    <div class="card-content">
      <div class="header-style-options">
        <div *ngFor="let option of headerStyleOptions"
             class="header-style-option"
             [class.selected]="option.selected"
             (click)="selectHeaderStyle(option)">
          <div class="header-style-preview" [ngClass]="option.id" [class.active-preview]="option.id === getPreviewHeaderStyle()">
            <!-- Standard style preview -->
            <div *ngIf="option.id === 'standard'" class="preview-header default-header shadow-sm rounded">
              <div class="preview-left">
                <span class="preview-nav"></span>
                <span class="preview-nav"></span>
              </div>
              <div class="preview-right">
                <span class="preview-balance green"></span>
                <span class="preview-icon"></span>
                <span class="preview-icon"></span>
              </div>
            </div>

            <!-- Compact style preview -->
            <div *ngIf="option.id === 'compact'" class="preview-header compact-header">
              <div class="preview-left">

                <span class="preview-nav small uppercase"></span>
                <span class="preview-nav small uppercase"></span>
              </div>
              <div class="preview-right">
                <span class="preview-balance small bordered"></span>
                <span class="preview-icon small"></span>
                <span class="preview-icon small"></span>
              </div>
            </div>

            <!-- Modern style preview -->
            <div *ngIf="option.id === 'modern'" class="preview-header-container">
              <div class="preview-header modern-header shadow-sm rounded">
                <div class="preview-left">

                  <span class="preview-button"></span>
                  <span class="preview-nav small"></span>
                </div>
                <div class="preview-right">
                  <span class="preview-balance blue"></span>
                  <span class="preview-icon"></span>
                  <span class="preview-icon"></span>
                </div>
              </div>
            </div>

            <!-- Minimal style preview -->
            <div *ngIf="option.id === 'minimal'" class="preview-header minimal-header">
              <div class="preview-left">
                <span class="preview-nav small underlined"></span>
                <span class="preview-nav small"></span>
                <span class="preview-nav small"></span>
              </div>
              <div class="preview-right">
                <span class="preview-balance small bordered-left"></span>
                <span class="preview-icon small"></span>
                <span class="preview-icon small"></span>
              </div>
            </div>
          </div>
          <div class="header-style-label">{{ option.label }}</div>
          <div class="header-style-check" *ngIf="option.selected">
            <fa-icon [icon]="['fas', 'check']"></fa-icon>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Sidebar Style Section -->
  <section class="settings-card">
    <div class="card-header">
      <h2 class="card-title">
        <fa-icon [icon]="['fas', 'columns']" class="section-icon"></fa-icon>
        {{ 'settings.design.sidebar_style.title' | translate }}
      </h2>
    </div>

    <div class="card-content">
      <div class="header-style-options">
        <div *ngFor="let option of sidebarStyleOptions"
             class="header-style-option"
             [class.selected]="option.selected"
             (click)="selectSidebarStyle(option)">
          <div class="header-style-preview" [ngClass]="option.id" [class.active-preview]="option.id === getPreviewSidebarStyle()">
            <!-- Standard sidebar style preview -->
            <div *ngIf="option.id === 'standard'" class="preview-sidebar default-sidebar">
              <div class="preview-sidebar-section">
                <div class="preview-sidebar-title"></div>
                <div class="preview-sidebar-item"></div>
                <div class="preview-sidebar-item active"></div>
                <div class="preview-sidebar-item"></div>
              </div>
            </div>

            <!-- Compact sidebar style preview -->
            <div *ngIf="option.id === 'compact'" class="preview-sidebar compact-sidebar">
              <div class="preview-sidebar-section">
                <div class="preview-sidebar-title small"></div>
                <div class="preview-sidebar-item small"></div>
                <div class="preview-sidebar-item small active"></div>
                <div class="preview-sidebar-item small"></div>
              </div>
            </div>

            <!-- Modern sidebar style preview -->
            <div *ngIf="option.id === 'modern'" class="preview-sidebar modern-sidebar">
              <div class="preview-sidebar-section">
                <div class="preview-sidebar-title"></div>
                <div class="preview-sidebar-item"></div>
                <div class="preview-sidebar-item active-indicator"></div>
                <div class="preview-sidebar-item"></div>
              </div>
            </div>

            <!-- Minimal sidebar style preview -->
            <div *ngIf="option.id === 'minimal'" class="preview-sidebar minimal-sidebar">
              <div class="preview-sidebar-section">
                <div class="preview-sidebar-title small"></div>
                <div class="preview-sidebar-item small"></div>
                <div class="preview-sidebar-item small active"></div>
                <div class="preview-sidebar-item small"></div>
              </div>
            </div>

            <!-- Card sidebar style preview -->
            <div *ngIf="option.id === 'card'" class="preview-sidebar card-sidebar">
              <div class="preview-sidebar-grid">
                <div class="preview-sidebar-card"></div>
                <div class="preview-sidebar-card active"></div>
                <div class="preview-sidebar-card"></div>
                <div class="preview-sidebar-card"></div>
              </div>
            </div>
          </div>
          <div class="header-style-label">{{ option.label }}</div>
          <div class="header-style-check" *ngIf="option.selected">
            <fa-icon [icon]="['fas', 'check']"></fa-icon>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Landing Section -->
  <section class="settings-card">
    <div class="card-header">
      <h2 class="card-title">
        <fa-icon [icon]="['fas', 'home']" class="section-icon"></fa-icon>
        {{ 'settings.design.landing_page.title' | translate }}
      </h2>
    </div>

    <div class="card-content">
      <div class="landing-settings">
        <div class="landing-options">
          <div class="landing-type-selection">
            <label class="landing-label">{{ 'settings.design.landing_page.template' | translate }}</label>
            <div class="landing-types">
              <div *ngFor="let type of landingTypes"
                   class="landing-type"
                   [class.selected]="type.selected"
                   (click)="selectLandingType(type)">
                <span>{{ type.name }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="landing-preview-wrapper">
          <label class="landing-label">{{ 'settings.design.landing_page.preview' | translate }}</label>
          <div class="landing-preview">
            <div class="landing-preview-container">
              <div class="landing-header">
                <div class="landing-logo">NEWPANEL</div>
                <div class="landing-actions">
                  <span>{{ 'settings.design.landing_page.new_order' | translate }}</span>
                  <span>{{ 'settings.design.landing_page.services' | translate }}</span>
                </div>
              </div>
              <div class="landing-content">
                <h3 class="landing-title">{{ 'settings.design.landing_page.most_comfortable' | translate }}</h3>
              </div>
            </div>
            <div class="landing-scroll-indicator"></div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Save Button -->
  <div class="save-section">
    <button class="save-button" (click)="saveSettings()">
      <fa-icon [icon]="['fas', 'save']"></fa-icon>
      {{ 'settings.design.save_changes' | translate }}
    </button>
  </div>
</div>
