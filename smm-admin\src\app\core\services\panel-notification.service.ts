import { Injectable, ApplicationRef } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, interval, Subscription } from 'rxjs';
import { filter, take } from 'rxjs/operators';
import { ConfigService } from './config.service';
import { PanelNotificationRes, PanelNotificationReq } from '../../model/response/panel-notification-res.model';


@Injectable({
  providedIn: 'root'
})
export class PanelNotificationService {
  private readonly apiUrl: string;
  private unreadCountSubject = new BehaviorSubject<number>(0);
  public unreadCount$ = this.unreadCountSubject.asObservable();
  private pollingSubscription?: Subscription;

  constructor(
    private http: HttpClient,
    private configService: ConfigService,
    private appRef: ApplicationRef
  ) {
    this.apiUrl = `${this.configService.apiUrl}/panel-notifications`;

    // Wait for application to stabilize before starting polling
    this.appRef.isStable.pipe(
      filter(stable => stable),
      take(1)
    ).subscribe(() => {
      // Initial load
      this.refreshUnreadCount();

      // Start polling after app is stable
      this.startPolling();
    });
  }

  private startPolling(): void {
    // Poll for unread count every 30 seconds
    this.pollingSubscription = interval(30000).subscribe(() => {
      this.refreshUnreadCount();
    });
  }

  public stopPolling(): void {
    if (this.pollingSubscription) {
      this.pollingSubscription.unsubscribe();
      this.pollingSubscription = undefined;
    }
  }

  createNotification(req: PanelNotificationReq): Observable<PanelNotificationRes> {
    return this.http.post<PanelNotificationRes>(this.apiUrl, req);
  }

  getNotifications(page: number = 0, size: number = 20): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}?page=${page}&size=${size}`);
  }

  getUnreadNotifications(): Observable<PanelNotificationRes[]> {
    return this.http.get<PanelNotificationRes[]>(`${this.apiUrl}/unread`);
  }

  getUnreadCount(): Observable<number> {
    return this.http.get<number>(`${this.apiUrl}/unread/count`);
  }

  markAsRead(id: number): Observable<string> {
    return this.http.post<string>(`${this.apiUrl}/${id}/read`, {});
  }

  markAllAsRead(): Observable<string> {
    return this.http.post<string>(`${this.apiUrl}/read-all`, {});
  }

  private refreshUnreadCount(): void {
    this.getUnreadCount().subscribe({
      next: (response) => {
     
          this.unreadCountSubject.next(response);
        
      },
      error: (error) => {
        console.error('Error fetching unread count:', error);
      }
    });
  }

  // Method to manually refresh unread count (e.g., after marking as read)
  public updateUnreadCount(): void {
    this.refreshUnreadCount();
  }

  // Cleanup method for proper subscription management
  public ngOnDestroy(): void {
    this.stopPolling();
  }
}
