/* Simple theme specific styles for layout auth */
.simple-header {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

/* Language dropdown animation */
.language-dropdown {
  animation: fadeInDown 0.2s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Logo hover effect */
.logo-container:hover img {
  transform: scale(1.05);
  transition: transform 0.2s ease-in-out;
}

/* Button hover effects */
.simple-btn {
  transition: all 0.2s ease-in-out;
}

.simple-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Footer styling */
.simple-footer {
  background: linear-gradient(to right, #f8fafc, #f1f5f9);
}

/* Social icons hover effect */
.social-icon {
  transition: all 0.2s ease-in-out;
}

.social-icon:hover {
  transform: translateY(-2px);
  color: #3b82f6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .simple-header {
    padding: 0.5rem 1rem;
  }
  
  .language-dropdown {
    right: 0;
    left: auto;
    min-width: 200px;
  }
}
