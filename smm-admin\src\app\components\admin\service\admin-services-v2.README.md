# Admin Services V2 Component

This is a complete recreation of the admin-service component with full functionality for managing services in the SMM admin panel.

## Features

### Core Service Management
- ✅ Add new services (Manual and Provider types)
- ✅ Edit existing services
- ✅ Delete services with confirmation
- ✅ Duplicate services
- ✅ Enable/disable services
- ✅ Service status management

### Bulk Operations
- ✅ Select all/individual services
- ✅ Bulk enable/disable services
- ✅ Bulk price changes
- ✅ Bulk special pricing

### Category Management
- ✅ Create new categories
- ✅ Edit categories
- ✅ Move services between categories
- ✅ Category visibility toggle
- ✅ Platform association for categories

### Search & Filtering
- ✅ Search services by name
- ✅ Filter by category
- ✅ Reset filters
- ✅ Real-time filtering

### Drag & Drop
- ✅ Reorder services within categories
- ✅ Move services between categories
- ✅ Reorder categories
- ✅ Visual feedback during drag operations

### Platform Management
- ✅ Add new platforms
- ✅ Platform management modal
- ✅ Associate platforms with categories
- ✅ Platform dropdown selection

### Special Pricing
- ✅ Service-specific special prices
- ✅ User-specific special prices
- ✅ Bulk special pricing
- ✅ Edit special prices

### Import/Export
- ✅ Import services from providers
- ✅ Service import wizard
- ✅ Bulk service operations

### Mobile Responsive
- ✅ Table view for desktop
- ✅ Card view for mobile
- ✅ Responsive design
- ✅ Touch-friendly interactions

### User Interface
- ✅ Context menus for services and categories
- ✅ Loading states and indicators
- ✅ Toast notifications
- ✅ Modal dialogs
- ✅ Confirmation dialogs

## Usage

### Accessing the Component
The component is available at `/panel/services-v2` route.

### Component Structure
```
admin-services-v2/
├── admin-services-v2.component.ts    # Main component logic
├── admin-services-v2.component.html  # Template
├── admin-services-v2.component.css   # Styles
└── admin-services-v2.README.md       # This documentation
```

### Key Dependencies
- `UIStateService` - Manages modal and UI states
- `SelectionService` - Handles service selection
- `FilterService` - Manages search and filtering
- `AdminServiceService` - API calls for services
- `PlatformService` - Platform management
- `ServiceManagementService` - Service operations
- `ToastService` - Notifications

### Modal Components Used
- `NewServiceComponent` - Add/edit services
- `AddPlatformLightComponent` - Quick platform addition
- `PlatformManagementComponent` - Full platform management
- `CategorySelectionComponent` - Move services between categories
- `NewCategoryComponent` - Add/edit categories
- `NewPricesComponent` - Bulk price changes
- `NewSpecialPricesComponent` - Special pricing
- `ImportServicesComponent` - Service import
- `ResourcesComponent` - Service type selection
- `DeleteConfirmationComponent` - Deletion confirmation

## API Integration

The component integrates with the following API endpoints:
- `GET /services` - Load services
- `POST /services` - Create service
- `PUT /services/{id}` - Update service
- `DELETE /services/{id}` - Delete service
- `PUT /services/{id}/active` - Activate service
- `PUT /services/{id}/deactivate` - Deactivate service
- `GET /platforms` - Load platforms
- `POST /platforms` - Create platform
- `GET /categories` - Load categories
- `POST /categories` - Create category

## Styling

The component uses Tailwind CSS classes and custom CSS variables:
- `--primary` - Primary color
- `--primary-hover` - Primary hover color
- `--primary-active` - Primary active color

## Event Handling

### Service Events
- `onServiceAdded` - Service created/updated
- `onServiceDeleted` - Service deleted
- `onServiceStatusChanged` - Service enabled/disabled

### Category Events
- `onCategoryAdded` - Category created
- `onCategoryUpdated` - Category updated
- `onCategorySelectedForMove` - Service moved to category

### Platform Events
- `onPlatformAdded` - Platform created
- `onPlatformsUpdated` - Platforms updated

### Bulk Events
- `onBulkOperationComplete` - Bulk operation finished

## Customization

### Adding New Menu Items
To add new menu items to service or category menus, modify the respective methods:
- `getServiceMenuItems()` - Service context menu
- `getCategoryMenuItems()` - Category context menu
- `getBulkActionMenuItems()` - Bulk action menu

### Adding New Modals
1. Import the modal component
2. Add modal state to component
3. Add modal trigger method
4. Add modal close method
5. Add modal to template

### Extending Functionality
The component is designed to be extensible. New features can be added by:
1. Adding new methods to the component
2. Extending the UI state service
3. Adding new modal components
4. Updating the template

## Performance Considerations

- Uses OnPush change detection strategy where possible
- Implements virtual scrolling for large service lists
- Lazy loads modal components
- Optimizes drag and drop operations
- Caches API responses where appropriate

## Testing

To test the component:
1. Navigate to `/panel/services-v2`
2. Test all CRUD operations
3. Test bulk operations
4. Test drag and drop functionality
5. Test mobile responsiveness
6. Test error handling

## Migration from V1

To migrate from the original admin-service component:
1. Update route to use `AdminServicesV2Component`
2. Verify all existing functionality works
3. Test integration with existing services
4. Update any custom modifications

## Support

For issues or questions about this component, please refer to the main project documentation or contact the development team.
