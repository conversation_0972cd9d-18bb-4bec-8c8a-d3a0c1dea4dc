package tndung.vnfb.smm.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomLanguageRes {

    private Long id;
    private String languageCode;
    private String languageName;
    private String flagClass;
    private String description;
    private boolean isActive;
    private String tenantId;
    private OffsetDateTime createdAt;
    private OffsetDateTime updatedAt;
}
