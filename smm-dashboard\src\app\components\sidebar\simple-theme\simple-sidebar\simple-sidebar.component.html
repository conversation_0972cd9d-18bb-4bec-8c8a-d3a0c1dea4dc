<!-- Simple Sidebar Theme - Completely New Design -->
<div class="modern-sidebar" [class.open]="isOpen" *ngIf="sidebarState$ | async as sidebarState">
  <div class="sidebar-wrapper">

    <!-- Header Section -->
    <div class="sidebar-header">
      <div class="logo-area" (click)="onLogoClick()">
        <div class="logo-icon">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
          </svg>
        </div>
        <div class="logo-text">
          <span class="logo-title">SMM</span>
          <span class="logo-subtitle">Dashboard</span>
        </div>
      </div>


    </div>

    <!-- Navigation Menu -->
    <nav class="nav-container">
      <div *ngFor="let section of sidebarState.menuSections" class="nav-section">

        <!-- Section Header -->
        <div class="section-header">
          <span class="section-title">{{ section.title | translate }}</span>
          <div class="section-line"></div>
        </div>

        <!-- Menu Items -->
        <div class="menu-items">
          <div *ngFor="let item of section.items"
               class="nav-item"
               [class.active]="item.isActive">
            <a [routerLink]="item.link"
               class="nav-link"
               (click)="handleMenuClick($event, item.link)">

              <!-- Icon Container -->
              <div class="icon-container">
                <app-svg-icon
                  [iconName]="item.icon"
                  [color]="item.isActive ? '#ffffff' : '#94a3b8'">
                </app-svg-icon>
              </div>

              <!-- Content -->
              <div class="nav-content">
                <span class="nav-label">{{ item.label | translate }}</span>
                <div class="nav-description">Manage {{ item.label | translate | lowercase }}</div>
              </div>

              <!-- Arrow Indicator -->
              <div class="nav-arrow">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                </svg>
              </div>

              <!-- Active Glow -->
              <div *ngIf="item.isActive" class="active-glow"></div>
            </a>
          </div>
        </div>
      </div>
    </nav>

    <!-- Footer Section -->


  </div>
</div>
