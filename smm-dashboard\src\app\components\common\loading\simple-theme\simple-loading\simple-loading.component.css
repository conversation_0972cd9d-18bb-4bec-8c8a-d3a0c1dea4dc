/* Simple Theme Loading Styles */

.simple-loading-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 50;
}

.simple-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(102, 126, 234, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.simple-loading-overlay.simple-transparent {
  background: transparent;
}

.simple-loading-inline {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
}

.simple-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.simple-loading-spinner {
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #ffffff;
  animation: simple-spin 1s linear infinite;
}

.simple-loading-spinner.simple-sm {
  width: 1.25rem;
  height: 1.25rem;
  border-width: 2px;
}

.simple-loading-spinner.simple-md {
  width: 2rem;
  height: 2rem;
}

.simple-loading-spinner.simple-lg {
  width: 3rem;
  height: 3rem;
  border-width: 4px;
}

.simple-loading-message {
  color: #ffffff;
  font-size: 0.9rem;
  font-weight: 500;
  text-align: center;
  max-width: 250px;
}

@keyframes simple-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
