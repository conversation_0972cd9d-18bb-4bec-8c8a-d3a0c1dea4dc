


      <!-- Simple Favorite Toggle -->
     
        <label *ngIf="selectedService" class="favorite-checkbox-label">
          <input
            type="checkbox"
            [checked]="isFavorite"
            [disabled]="isLoading"
            (change)="onCheckboxChange($event)"
            class="favorite-checkbox">
          <fa-icon [icon]='["fas", "heart"]'
                   class="heart-icon"
                   [class.active]="isFavorite"></fa-icon>
          <span class="favorite-text">{{ 'favorite' | translate }}</span>
        </label>
 


