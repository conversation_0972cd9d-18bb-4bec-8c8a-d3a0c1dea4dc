<div class="simple-lite-dropdown" [ngClass]="dropdownCustomClassDropdown">
  <!-- Dropdown Button -->
  <button 
    type="button"
    (click)="toggleDropdown($event)"
    class="simple-dropdown-button"
    [ngClass]="dropdownCustomClassButton"
  >
    <span class="simple-dropdown-text">
      {{ dropdownSelectedOption || dropdownPlaceholder }}
    </span>
    <fa-icon 
      icon="chevron-down" 
      [size]="dropdownIconSize" 
      class="simple-dropdown-icon"
      [ngClass]="{'simple-dropdown-icon-open': isOpen}"
    ></fa-icon>
  </button>

  <!-- Dropdown Menu -->
  <div 
    *ngIf="isOpen" 
    class="simple-dropdown-menu lite-dropdown-menu-container"
  >
    <div class="simple-dropdown-content">
      <button
        *ngFor="let option of dropdownOptions"
        type="button"
        (click)="selectOption(option, $event)"
        class="simple-dropdown-option"
        [ngClass]="{'simple-dropdown-option-selected': option === dropdownSelectedOption}"
      >
        <span class="simple-option-text">{{ option }}</span>
        <fa-icon 
          *ngIf="option === dropdownSelectedOption"
          icon="check" 
          class="simple-option-check"
        ></fa-icon>
      </button>
    </div>
  </div>
</div>
