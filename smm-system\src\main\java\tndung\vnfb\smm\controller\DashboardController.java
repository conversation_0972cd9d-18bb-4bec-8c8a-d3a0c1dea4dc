package tndung.vnfb.smm.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import tndung.vnfb.smm.anotation.TenantCheck;
import tndung.vnfb.smm.dto.response.ActionLogRes;
import tndung.vnfb.smm.dto.response.DashboardRes;
import tndung.vnfb.smm.dto.response.TopServiceDTO;
import tndung.vnfb.smm.service.ActionLogService;
import tndung.vnfb.smm.service.DashboardService;
import tndung.vnfb.smm.service.TopServiceService;

import java.time.LocalDate;
import java.util.List;


@RestController
@RequestMapping("/v1/dashboard")
@RequiredArgsConstructor
public class DashboardController {
    private final DashboardService dashboardService;
    private final TopServiceService topServiceService;
    private final ActionLogService actionLogService;

    @GetMapping("/stats")
    @PreAuthorize("hasAnyRole('ROLE_PANEL',  'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ResponseEntity<DashboardRes> getDashboardStats(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

        DashboardRes dashboardStats = dashboardService.getDashboardStats(startDate, endDate);
        return ResponseEntity.ok(dashboardStats);
    }

    @GetMapping("/services/top")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ResponseEntity<List<TopServiceDTO>> getTopServices(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

        List<TopServiceDTO> topService = topServiceService.getTopOrderedServices(startDate, endDate);
        return ResponseEntity.ok(topService);
    }

    @GetMapping("/activities/latest")
    @PreAuthorize("hasAnyRole('ROLE_PANEL',  'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ResponseEntity<List<ActionLogRes>> getLastActivities(@RequestParam(required = false) Integer count) {
        List<ActionLogRes> latestLogs = actionLogService.getLatestLogs(count);
        return ResponseEntity.ok(latestLogs);
    }
}
