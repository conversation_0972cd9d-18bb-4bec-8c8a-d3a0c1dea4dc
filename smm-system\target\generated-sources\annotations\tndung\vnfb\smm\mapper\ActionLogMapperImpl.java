package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.dto.response.ActionLogRes;
import tndung.vnfb.smm.dto.response.ActionLogRes.ActionLogResBuilder;
import tndung.vnfb.smm.entity.ActionLog;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class ActionLogMapperImpl implements ActionLogMapper {

    @Override
    public ActionLogRes toRes(ActionLog entity) {
        if ( entity == null ) {
            return null;
        }

        ActionLogResBuilder actionLogRes = ActionLogRes.builder();

        actionLogRes.details( entity.getDetails() );
        actionLogRes.entityId( entity.getEntityId() );
        actionLogRes.entityName( entity.getEntityName() );
        actionLogRes.id( entity.getId() );
        actionLogRes.ipAddress( entity.getIpAddress() );
        actionLogRes.operation( entity.getOperation() );
        actionLogRes.username( entity.getUsername() );

        return actionLogRes.build();
    }

    @Override
    public List<ActionLogRes> toRes(List<ActionLog> entities) {
        if ( entities == null ) {
            return null;
        }

        List<ActionLogRes> list = new ArrayList<ActionLogRes>( entities.size() );
        for ( ActionLog actionLog : entities ) {
            list.add( toRes( actionLog ) );
        }

        return list;
    }
}
