import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { Observable } from 'rxjs';

// Services
import { MessOrderFormLogicService, MessOrderFormState } from '../../../services/mess-order-form-logic.service';

// Components
import { SimpleIconDropdownComponent } from '../../../../common/icon-dropdown/simple/simple-icon-dropdown.component';
import { SimpleServiceDropdownComponent } from '../../../../common/service-dropdown/simple/simple-service-dropdown.component';

// Pipes
import { CurrencyConvertPipe } from '../../../../../core/pipes/currency-convert.pipe';

@Component({
  selector: 'app-simple-simple-mess-order',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    SimpleIconDropdownComponent,
    SimpleServiceDropdownComponent,
    CurrencyConvertPipe
  ],
  templateUrl: './simple-simple-mess-order.component.html',
  styleUrl: './simple-simple-mess-order.component.css'
})
export class SimpleSimpleMessOrderComponent implements OnInit, OnDestroy {
  // Mess order form logic state
  messOrderFormState$: Observable<MessOrderFormState>;

  constructor(private messOrderFormLogicService: MessOrderFormLogicService) {
    this.messOrderFormState$ = this.messOrderFormLogicService.state$;
  }

  ngOnInit(): void {
    // MessOrderFormLogicService handles all initialization
  }

  ngOnDestroy(): void {
    // MessOrderFormLogicService is singleton, no cleanup needed
  }

  // Delegate methods to MessOrderFormLogicService for template compatibility
  createOrder(): void {
    this.messOrderFormLogicService.createSimpleOrder();
  }

  onCategorySelected(category: any): void {
    this.messOrderFormLogicService.onCategorySelected(category);
  }

  onServiceSelected(service: any): void {
    this.messOrderFormLogicService.onServiceSelected(service);
  }

  decreaseQuantity(): void {
    this.messOrderFormLogicService.decreaseQuantity();
  }

  increaseQuantity(): void {
    this.messOrderFormLogicService.increaseQuantity();
  }

  // Getter methods for template compatibility
  get orderForm() {
    return this.messOrderFormLogicService.orderForm;
  }

  get categories() {
    return this.messOrderFormLogicService.categories;
  }

  get services() {
    return this.messOrderFormLogicService.services;
  }

  get selectedCategory() {
    return this.messOrderFormLogicService.selectedCategory;
  }

  get selectedServiceObj() {
    return this.messOrderFormLogicService.selectedServiceObj;
  }

  get isProcessing() {
    return this.messOrderFormLogicService.isProcessing;
  }

  get successCount() {
    return this.messOrderFormLogicService.successCount;
  }

  get failedCount() {
    return this.messOrderFormLogicService.failedCount;
  }

  get showResults() {
    return this.messOrderFormLogicService.showResults;
  }

  get orderResults() {
    return this.messOrderFormLogicService.orderResults;
  }

  get price() {
    return this.messOrderFormLogicService.price;
  }

  get selectedService() {
    return this.messOrderFormLogicService.selectedService;
  }

  // Method to get link count from content
  getLinkCount(): number {
    const content = this.orderForm.get('content')?.value || '';
    if (!content || content.trim() === '') {
      return 1;
    }

    // Split by newlines and filter out empty lines
    const lines = content.split('\n').filter((line: string) => line.trim() !== '');
    return Math.max(1, lines.length);
  }

  // Helper method for form validation
  markFormGroupTouched(formGroup: any): void {
    Object.values(formGroup.controls).forEach((control: any) => {
      control.markAsTouched();
      if (control.controls) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
