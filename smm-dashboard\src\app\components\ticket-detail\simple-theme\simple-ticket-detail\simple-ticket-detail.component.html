<div class="simple-ticket-detail-container">
  
  <!-- Header Section -->
  <div class="ticket-header">
    <div class="header-content">
      <div class="header-icon">
        <fa-icon [icon]='["fas", "ticket-alt"]'></fa-icon>
      </div>
      <div class="header-text">
        <h1 class="header-title">{{ 'ticket.ticket_details' | translate }}</h1>
        <p class="header-subtitle" *ngIf="(state$ | async)?.ticket">
          #{{ (state$ | async)?.ticket?.id }} - {{ (state$ | async)?.ticket?.subject }}
        </p>
      </div>
      <div class="status-badge" *ngIf="(state$ | async)?.ticket">
        <ng-container *ngIf="state$ | async as state">
          <fa-icon [icon]='["fas", $any(getStatusIcon(state.ticket?.status))]'
                   [style.color]="getStatusColor(state.ticket?.status)"></fa-icon>
          <span [style.color]="getStatusColor(state.ticket?.status)">
            {{ state.ticket?.status }}
          </span>
        </ng-container>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="main-content">
    
    <!-- Loading State -->
    <div *ngIf="(state$ | async)?.isLoading" class="loading-container">
      <app-loading [size]="'lg'"></app-loading>
    </div>

    <!-- API Timeout Error -->
    <div *ngIf="(state$ | async)?.apiTimeoutError && !(state$ | async)?.isLoading" class="error-alert">
      <div class="error-content">
        <fa-icon [icon]='["fas", "exclamation-triangle"]' class="error-icon"></fa-icon>
        <div class="error-text">
          <h3>{{ 'ticket.error' | translate }}</h3>
          <p>{{ 'ticket.api_timeout_load' | translate }}</p>
        </div>
        <button (click)="dismissApiTimeoutError()" class="error-close">
          <fa-icon [icon]='["fas", "times"]'></fa-icon>
        </button>
      </div>
    </div>

    <!-- Ticket Content -->
    <div *ngIf="!(state$ | async)?.isLoading && (state$ | async)?.ticket" class="ticket-content">
      
      <!-- Status Banner -->
      <div class="status-banner" [ngClass]="'status-' + (state$ | async)?.ticket?.status?.toLowerCase()">
        <ng-container *ngIf="state$ | async as state">
          <div class="status-banner-content">
            <fa-icon [icon]='["fas", $any(getStatusIcon(state.ticket?.status))]' class="status-icon"></fa-icon>
            <div class="status-text">
              <h3>{{ 'ticket.' + (state.ticket?.status?.toLowerCase() || '') | translate }}</h3>
              <p>{{ 'ticket.' + (state.ticket?.status?.toLowerCase() || '') + '_message' | translate }}</p>
            </div>
          </div>
        </ng-container>
      </div>

      <!-- Chat Area -->
      <div #chatContainer class="chat-area">
        <div class="chat-header">
          <h3>{{ 'ticket.conversation' | translate }}</h3>
          <span class="message-count" *ngIf="(state$ | async)?.ticket?.replies">
            {{ (state$ | async)?.ticket?.replies?.length || 0 }} {{ 'ticket.messages' | translate }}
          </span>
        </div>

        <div class="messages-container">
          <!-- Initial Ticket Message -->
          <div class="message user-message">
            <div class="message-avatar">
              <fa-icon [icon]='["fas", "user"]'></fa-icon>
            </div>
            <div class="message-content">
              <div class="message-header">
                <span class="message-author">{{ 'ticket.you' | translate }}</span>
                <span class="message-time">{{ (state$ | async)?.ticket?.created_at | date: 'short' }}</span>
              </div>
              <div class="message-body">
                <h4 class="message-subject">{{ (state$ | async)?.ticket?.subject }}</h4>
                <p class="message-text">{{ (state$ | async)?.ticket?.description }}</p>
              </div>
            </div>
          </div>

          <!-- Replies -->
          <ng-container *ngIf="(state$ | async)?.ticket?.replies && ((state$ | async)?.ticket?.replies?.length || 0) > 0">
            <div *ngFor="let reply of (state$ | async)?.ticket?.replies; let i = index" 
                 class="message"
                 [ngClass]="reply.replied_by === (state$ | async)?.firstRepliedBy ? 'support-message' : 'user-message'">
              
              <div class="message-avatar">
                <fa-icon *ngIf="reply.replied_by === (state$ | async)?.firstRepliedBy" [icon]='["fas", "headset"]'></fa-icon>
                <fa-icon *ngIf="reply.replied_by !== (state$ | async)?.firstRepliedBy" [icon]='["fas", "user"]'></fa-icon>
              </div>
              
              <div class="message-content">
                <div class="message-header">
                  <span class="message-author">{{ reply.replied_by }}</span>
                  <span class="message-time">{{ reply.created_at | date: 'short' }}</span>
                </div>
                <div class="message-body">
                  <p class="message-text">{{ reply.content }}</p>
                </div>
              </div>
            </div>
          </ng-container>

          <!-- Empty State -->
          <div *ngIf="!(state$ | async)?.ticket?.replies || (state$ | async)?.ticket?.replies?.length === 0" class="empty-messages">
            <fa-icon [icon]='["fas", "comments"]' class="empty-icon"></fa-icon>
            <p>{{ 'ticket.no_replies_yet' | translate }}</p>
          </div>
        </div>
      </div>

      <!-- Reply Form -->
      <div class="reply-form">
        <!-- API Timeout Error for Reply -->
        <div *ngIf="(state$ | async)?.apiTimeoutError" class="reply-error">
          <fa-icon [icon]='["fas", "exclamation-triangle"]'></fa-icon>
          <span>{{ 'ticket.api_timeout' | translate }}</span>
          <button (click)="dismissApiTimeoutError()">
            <fa-icon [icon]='["fas", "times"]'></fa-icon>
          </button>
        </div>

        <!-- Closed Ticket Notice -->
        <div *ngIf="isTicketClosed()" class="closed-notice">
          <fa-icon [icon]='["fas", "lock"]'></fa-icon>
          <span>{{ 'ticket.closed_message' | translate }}</span>
        </div>

        <!-- Reply Input -->
        <div class="reply-input-container">
          <div class="input-header">
            <h4>{{ 'ticket.your_reply' | translate }}</h4>
            <div *ngIf="!isTicketClosed()" class="countdown">
              <fa-icon [icon]='["fas", "clock"]'></fa-icon>
              <span>{{ (state$ | async)?.timeUntilReset }}s</span>
            </div>
          </div>
          
          <textarea
            [value]="(state$ | async)?.replyMessage"
            (input)="updateReplyMessage($any($event.target).value)"
            class="reply-textarea"
            [class.disabled]="isTicketClosed()"
            rows="6"
            placeholder="{{ 'ticket.type_message' | translate }}"
            [disabled]="isTicketClosed()">
          </textarea>
          
          <button 
            (click)="sendReply()" 
            class="reply-button"
            [class.disabled]="(state$ | async)?.isLoading || isTicketClosed()"
            [disabled]="(state$ | async)?.isLoading || isTicketClosed()">
            
            <fa-icon *ngIf="!(state$ | async)?.isLoading" [icon]='["fas", "paper-plane"]'></fa-icon>
            <div *ngIf="(state$ | async)?.isLoading" class="loading-spinner"></div>
            
            <span *ngIf="!(state$ | async)?.isLoading">{{ 'ticket.send_reply' | translate }}</span>
            <span *ngIf="(state$ | async)?.isLoading">{{ 'ticket.sending' | translate }}...</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Ticket Info Sidebar -->
    <div *ngIf="!(state$ | async)?.isLoading && (state$ | async)?.ticket" class="ticket-info">
      <div class="info-header">
        <h3>{{ 'ticket.ticket_information' | translate }}</h3>
      </div>
      
      <div class="info-content">
        <div class="info-item">
          <label>{{ 'ticket.ticket_id' | translate }}</label>
          <span class="ticket-id">#{{ (state$ | async)?.ticket?.id }}</span>
        </div>
        
        <div class="info-item">
          <label>{{ 'ticket.status' | translate }}</label>
          <ng-container *ngIf="state$ | async as state">
            <span class="status-value" [style.color]="getStatusColor(state.ticket?.status)">
              {{ state.ticket?.status }}
            </span>
          </ng-container>
        </div>
        
        <div class="info-item">
          <label>{{ 'ticket.created_at' | translate }}</label>
          <span>{{ (state$ | async)?.ticket?.created_at | date: 'medium' }}</span>
        </div>
        
        <div class="info-item">
          <label>{{ 'ticket.updated_at' | translate }}</label>
          <span>{{ (state$ | async)?.ticket?.updated_at | date: 'medium' }}</span>
        </div>
        
        <div class="info-item">
          <label>{{ 'ticket.response_time' | translate }}</label>
          <span>0 - 1H</span>
        </div>
        
        <div class="info-note">
          <fa-icon [icon]='["fas", "info-circle"]'></fa-icon>
          <p>{{ 'ticket.response_note' | translate }}</p>
        </div>
      </div>
    </div>
  </div>

</div>
