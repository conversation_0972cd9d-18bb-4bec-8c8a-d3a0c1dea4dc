import { Injectable, OnD<PERSON>roy, inject } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Clipboard } from '@angular/cdk/clipboard';
import { Order } from '../../../../model/g-service';

export interface OrderLabelState {
  order: Order;
  showCopyId: boolean;
}

export interface OrderLabelInputs {
  order: Order;
  showCopyId: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class OrderLabelLogicService implements OnDestroy {
  private stateSubject = new BehaviorSubject<OrderLabelState>({
    order: {} as Order,
    showCopyId: false
  });

  public state$ = this.stateSubject.asObservable();
  private clipboard = inject(Clipboard);

  constructor() {}

  updateInputs(inputs: OrderLabelInputs): void {
    this.updateState(inputs);
  }

  copyId(): void {
    const currentState = this.stateSubject.value;
    if (currentState.order?.id) {
      this.clipboard.copy(currentState.order.id);
    }
  }

  private updateState(partialState: Partial<OrderLabelState>): void {
    const currentState = this.stateSubject.value;
    this.stateSubject.next({ ...currentState, ...partialState });
  }

  ngOnDestroy(): void {
    this.destroy();
  }

  destroy(): void {
    // Cleanup if needed
  }
}
