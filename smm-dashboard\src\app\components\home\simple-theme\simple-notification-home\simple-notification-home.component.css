.simple-notification-container {
  position: relative;
  border-radius: 0.5rem;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid rgba(30, 41, 59, 0.1);
}

/* Notification Header */
.notification-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.notification-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #1e293b;
}

.notification-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  color: #1e293b;
}

/* Notification Content */
.notification-content {
  font-size: 0.875rem;
  line-height: 1.625;
  padding-right: 2rem; /* Space for dismiss button */
  color: #64748b;
}

/* Global styles for content HTML */
.notification-content p {
  margin-bottom: 0.5rem;
}

.notification-content p:last-child {
  margin-bottom: 0;
}

.notification-content a {
  color: #1e293b;
  text-decoration: underline;
}

.notification-content a:hover {
  color: #0f172a;
}

.notification-content strong {
  font-weight: 600;
}

.notification-content ul,
.notification-content ol {
  margin-left: 1rem;
  margin-bottom: 0.5rem;
}

.notification-content li {
  margin-bottom: 0.25rem;
}

/* Dismiss Button */
.dismiss-btn {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  padding: 0.25rem;
  border-radius: 9999px;
  transition: colors 200ms;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
}

.dismiss-btn:hover {
  color: #64748b;
  background: rgba(30, 41, 59, 0.05);
}

.dismiss-btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(30, 41, 59, 0.3);
}

/* Variants for different notification types */
.simple-notification-container.success {
  background-color: #f0fdf4;
  border-color: #bbf7d0;
}

.simple-notification-container.success .notification-icon {
  color: #dc2626;
}

.simple-notification-container.success .notification-title {
  color: #166534;
}

.simple-notification-container.success .notification-content {
  color: #15803d;
}

.simple-notification-container.success .dismiss-btn {
  color: #4ade80;
}

.simple-notification-container.success .dismiss-btn:hover {
  color: #16a34a;
  background-color: #dcfce7;
}

.simple-notification-container.warning {
  background-color: #fffbeb;
  border-color: #fde68a;
}

.simple-notification-container.warning .notification-icon {
  color: #d97706;
}

.simple-notification-container.warning .notification-title {
  color: #92400e;
}

.simple-notification-container.warning .notification-content {
  color: #b45309;
}

.simple-notification-container.warning .dismiss-btn {
  color: #fbbf24;
}

.simple-notification-container.warning .dismiss-btn:hover {
  color: #d97706;
  background-color: #fef3c7;
}

.simple-notification-container.error {
  background-color: #fef2f2;
  border-color: #fecaca;
}

.simple-notification-container.error .notification-icon {
  color: #dc2626;
}

.simple-notification-container.error .notification-title {
  color: #991b1b;
}

.simple-notification-container.error .notification-content {
  color: #b91c1c;
}

.simple-notification-container.error .dismiss-btn {
  color: #f87171;
}

.simple-notification-container.error .dismiss-btn:hover {
  color: #dc2626;
  background-color: #fee2e2;
}

/* Responsive Design */
@media (max-width: 640px) {
  .simple-notification-container {
    padding: 0.75rem;
  }

  .notification-content {
    padding-right: 1.5rem;
  }

  .dismiss-btn {
    top: 0.5rem;
    right: 0.5rem;
  }
}
