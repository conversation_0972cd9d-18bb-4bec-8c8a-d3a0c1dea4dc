<div class="promo-container">
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-xl font-bold">{{ 'promo_codes.title' | translate }}</h2>
  </div>

  <!-- Button to create new promo code -->
  <div class="mb-6">
    <button
      (click)="openCreateModal()"
      class="flex items-center gap-2 bg-[var(--primary)] text-white px-4 py-3 rounded-lg hover:bg-[var(--primary-hover)] transition-colors">
      <fa-icon [icon]="['fas', 'plus']"></fa-icon>
      <span>{{ 'promo_codes.create_promo_code' | translate }}</span>
    </button>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="loading" class="flex justify-center items-center py-8">
    <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-cyan-500"></div>
  </div>

  <!-- Desktop Promo codes table (hidden on mobile) -->
  <div *ngIf="!loading" class="table-container bg-white shadow-sm hidden md:block">
    <div class="overflow-x-auto">
      <table>
        <thead>
          <tr>
            <th scope="col">
              {{ 'promo_codes.code' | translate }}
            </th>
            <th scope="col">
              {{ 'promo_codes.type' | translate }}
            </th>
            <th scope="col">
              {{ 'promo_codes.amount' | translate }}
            </th>
            <th scope="col">
              {{ 'promo_codes.activations' | translate }}
            </th>
            <th scope="col">
              {{ 'promo_codes.status' | translate }}
            </th>
            <th scope="col">
              {{ 'promo_codes.created_at' | translate }}
            </th>
            <th scope="col">
              {{ 'promo_codes.actions' | translate }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let voucher of vouchers">
            <td>
              <div style="display: flex; align-items: center;">
                <span style="font-weight: 500; color: #111827;">{{ voucher.code }}</span>
                <button
                  (click)="copyToClipboard(voucher.code)"
                  style="margin-left: 8px; color: #9ca3af; transition: color 0.2s;"
                  onmouseover="this.style.color='#4b5563'"
                  onmouseout="this.style.color='#9ca3af'">
                  <fa-icon [icon]="['fas', 'copy']"></fa-icon>
                </button>
              </div>
            </td>
            <td>
              <span style="padding: 4px 8px; font-size: 12px; font-weight: 500; border-radius: 2px;"
                [ngStyle]="{
                  'background-color': voucher.type === 'Top_Up' ? '#dbeafe' : '#f3e8ff',
                  'color': voucher.type === 'Top_Up' ? '#1e40af' : '#6b21a8',
                  'border': voucher.type === 'Top_Up' ? '1px solid #bfdbfe' : '1px solid #e9d5ff'
                }">
                {{ voucher.type === 'Top_Up' ? ('promo_codes.top_up_balance' | translate) : ('promo_codes.discount_for_order' | translate) }}
              </span>
            </td>
            <td>
              {{ getFormattedDiscount(voucher) }}
            </td>
            <td>
              {{ voucher.usage_count }} of {{ voucher.usage_limit }}
            </td>
            <td>
              <span style="padding: 4px 8px; font-size: 12px; font-weight: 500; border-radius: 2px;"
                [ngStyle]="{
                  'background-color': voucher.usage_count < voucher.usage_limit ? '#dcfce7' : '#f3f4f6',
                  'color': voucher.usage_count < voucher.usage_limit ? '#166534' : '#4b5563',
                  'border': voucher.usage_count < voucher.usage_limit ? '1px solid #bbf7d0' : '1px solid #e5e7eb'
                }">
                {{ voucher.usage_count < voucher.usage_limit ? ('promo_codes.active' | translate) : ('promo_codes.used' | translate) }}
              </span>
            </td>
            <td>
              {{ voucher.created_at ? (voucher.created_at | date:'mediumDate') : 'N/A' }}
            </td>
            <td>
              <button
                (click)="deleteVoucher(voucher)"
                style="color: #9ca3af; transition: color 0.2s;"
                onmouseover="this.style.color='#ef4444'"
                onmouseout="this.style.color='#9ca3af'">
                <fa-icon [icon]="['fas', 'trash']"></fa-icon>
              </button>
            </td>
          </tr>

          <!-- Empty state for desktop -->
          <tr *ngIf="vouchers.length === 0">
            <td colspan="7" style="padding: 32px 24px; text-align: center; color: #6b7280;">
              {{ 'promo_codes.no_promo_codes' | translate }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Mobile Card View (visible only on mobile) -->
  <div *ngIf="!loading" class="block md:hidden">
    <!-- Empty state for mobile -->
    <div *ngIf="vouchers.length === 0" class="bg-white rounded-lg p-8 text-center text-gray-500">
      {{ 'promo_codes.no_promo_codes' | translate }}
    </div>

    <!-- Mobile cards -->
    <div *ngFor="let voucher of vouchers" class="bg-white rounded-lg shadow-sm mb-4 p-4">
      <!-- Header with code and copy button -->
      <div class="flex justify-between items-center mb-3">
        <div class="flex items-center">
          <span class="font-medium text-gray-900">{{ voucher.code }}</span>
          <button
            (click)="copyToClipboard(voucher.code)"
            class="ml-2 text-gray-400 hover:text-gray-600">
            <fa-icon [icon]="['fas', 'copy']"></fa-icon>
          </button>
        </div>
        <button
          (click)="deleteVoucher(voucher)"
          class="text-gray-400 hover:text-red-500">
          <fa-icon [icon]="['fas', 'trash']"></fa-icon>
        </button>
      </div>

      <!-- Card body with details -->
      <div class="grid grid-cols-2 gap-3">
        <!-- Type -->
        <div class="flex flex-col">
          <span class="text-xs text-gray-500 mb-1">{{ 'promo_codes.type' | translate }}</span>
          <span class="inline-block"
            style="padding: 4px 8px; font-size: 12px; font-weight: 500; border-radius: 2px;"
            [ngStyle]="{
              'background-color': voucher.type === 'Top_Up' ? '#dbeafe' : '#f3e8ff',
              'color': voucher.type === 'Top_Up' ? '#1e40af' : '#6b21a8',
              'border': voucher.type === 'Top_Up' ? '1px solid #bfdbfe' : '1px solid #e9d5ff'
            }">
            {{ voucher.type === 'Top_Up' ? ('promo_codes.top_up_balance' | translate) : ('promo_codes.discount_for_order' | translate) }}
          </span>
        </div>

        <!-- Amount -->
        <div class="flex flex-col">
          <span class="text-xs text-gray-500 mb-1">{{ 'promo_codes.amount' | translate }}</span>
          <span class="font-medium">{{ getFormattedDiscount(voucher) }}</span>
        </div>

        <!-- Activations -->
        <div class="flex flex-col">
          <span class="text-xs text-gray-500 mb-1">{{ 'promo_codes.activations' | translate }}</span>
          <span class="font-medium">{{ voucher.usage_count }} of {{ voucher.usage_limit }}</span>
        </div>

        <!-- Status -->
        <div class="flex flex-col">
          <span class="text-xs text-gray-500 mb-1">{{ 'promo_codes.status' | translate }}</span>
          <span class="inline-block"
            style="padding: 4px 8px; font-size: 12px; font-weight: 500; border-radius: 2px;"
            [ngStyle]="{
              'background-color': voucher.usage_count < voucher.usage_limit ? '#dcfce7' : '#f3f4f6',
              'color': voucher.usage_count < voucher.usage_limit ? '#166534' : '#4b5563',
              'border': voucher.usage_count < voucher.usage_limit ? '1px solid #bbf7d0' : '1px solid #e5e7eb'
            }">
            {{ voucher.usage_count < voucher.usage_limit ? ('promo_codes.active' | translate) : ('promo_codes.used' | translate) }}
          </span>
        </div>

        <!-- Created at -->
        <div class="flex flex-col col-span-2">
          <span class="text-xs text-gray-500 mb-1">{{ 'promo_codes.created_at' | translate }}</span>
          <span class="font-medium">{{ voucher.created_at ? (voucher.created_at | date:'mediumDate') : 'N/A' }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Create Promo Code Modal -->
  <app-create-promo-code
    *ngIf="showCreateModal"
    (close)="closeCreateModal()"
    (voucherCreated)="onVoucherCreated($event)">
  </app-create-promo-code>

  <!-- Delete Confirmation Modal -->
  <app-delete-confirmation
    *ngIf="showDeleteConfirmation && voucherToDelete"
    [itemName]="voucherToDelete.code"
    [isLoading]="isDeleting"
    (close)="closeDeleteConfirmation()"
    (confirm)="confirmDeleteVoucher()">
  </app-delete-confirmation>
</div>
