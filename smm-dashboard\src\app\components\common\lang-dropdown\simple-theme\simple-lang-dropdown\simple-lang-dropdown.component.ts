import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Host<PERSON><PERSON><PERSON>, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Observable } from 'rxjs';

// Services
import { LangDropdownLogicService, LangDropdownState } from '../../services/lang-dropdown-logic.service';

// Icons
import { IconsModule } from '../../../../../icons/icons.module';

@Component({
  selector: 'app-simple-lang-dropdown',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconsModule
  ],
  templateUrl: './simple-lang-dropdown.component.html',
  styleUrl: './simple-lang-dropdown.component.css'
})
export class SimpleLangDropdownComponent implements OnInit, OnDestroy {
  // Lang dropdown logic state
  langDropdownState$: Observable<LangDropdownState>;

  // Reference to the dropdown container
  @ViewChild('dropdownContainer', { static: false }) dropdownContainer!: ElementRef;

  constructor(private langDropdownLogicService: LangDropdownLogicService) {
    this.langDropdownState$ = this.langDropdownLogicService.state$;
  }

  ngOnInit(): void {
    // LangDropdownLogicService handles all initialization
  }

  ngOnDestroy(): void {
    // LangDropdownLogicService is singleton, no cleanup needed
  }

  // Delegate methods to LangDropdownLogicService for template compatibility
  toggleDropdown(): void {
    this.langDropdownLogicService.toggleDropdown();
  }

  closeDropdown(): void {
    this.langDropdownLogicService.closeDropdown();
  }

  async selectLanguage(lang: string): Promise<void> {
    await this.langDropdownLogicService.selectLanguage(lang);
  }

  updateDropdownPosition(): void {
    this.langDropdownLogicService.updateDropdownPosition();
  }

  @HostListener('window:scroll', ['$event'])
  onWindowScroll(): void {
    this.langDropdownLogicService.onWindowScroll();
  }

  @HostListener('document:click', ['$event'])
  onClickOutside(event: Event): void {
    const dropdownElement = this.dropdownContainer?.nativeElement;
    this.langDropdownLogicService.onClickOutside(event, dropdownElement);
  }
}
