import { Injectable, OnDestroy } from '@angular/core';
import { ApiKeyService } from '../../core/services/api-key.service';
import { ConfigService } from '../../core/services/config.service';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';

export interface ApiService {
  id: string;
  name: string;
  parameters: { name: string; description: string }[];
  codeExample: string;
}

@Injectable()
export class ApiDocLogicService implements OnDestroy {
  apiDetails = {
    url: '',
    key: '••••••••••••••••',
    method: 'Post',
    contentType: 'application/x-www-form-urlencoded',
    responsive: 'Json'
  };

  isLoadingApiKey = false;
  isGeneratingApiKey = false;
  private subscriptions: Subscription[] = [];

  // Default parameters shown initially
  parameters = [
    { name: 'key', description: 'API Key' },
    { name: 'action', description: '"services"' }
  ];

  // Default code example shown initially
  codeExample = `[
  {
      "service": 1,
      "name": "Youtube views",
      "type": "Default",
      "category": "Youtube",
      "rate": "2.5",
      "min": "200",
      "max": "10000",
      "refill": true
  },
  {
      "service": 2,
      "name": "Facebook comments",
      "type": "Custom Comments",
      "category": "Facebook",
      "rate": "4",
      "min": "10",
      "max": "1500",
      "refill": false
  }
]`;

  // Service IDs that will be displayed in the sidebar
  services = [
    'services',
    'add',
    'status',
    'multistatus',
    'refill',
    'refills',
    'refill_status',
    'refills_status',
    'balance'
  ];

  // Currently selected service
  selectedService: string = 'services';

  // All service data with parameters and code examples
  serviceData: { [key: string]: ApiService } = {
    'services': {
      id: 'services',
      name: 'Services',
      parameters: [
        { name: 'key', description: 'API Key' },
        { name: 'action', description: '"services"' }
      ],
      codeExample: `[
  {
      "service": 1,
      "name": "Youtube views",
      "type": "Default",
      "category": "Youtube",
      "rate": "2.5",
      "min": "200",
      "max": "10000",
      "refill": true
  },
  {
      "service": 2,
      "name": "Facebook comments",
      "type": "Custom Comments",
      "category": "Facebook",
      "rate": "4",
      "min": "10",
      "max": "1500",
      "refill": false
  }
]`
    },
    'add': {
      id: 'add',
      name: 'Add order',
      parameters: [
        { name: 'key', description: 'API Key' },
        { name: 'action', description: '"add"' },
        { name: 'service', description: 'Service ID' },
        { name: 'link', description: 'Link' },
        { name: 'quantity', description: 'Needed quantity' },
        { name: 'list', description: 'Suggest video list or Keyword search list' },
        { name: 'suggest', description: 'Suggest video list' },
        { name: 'search', description: 'Keyword search list' },
        { name: 'comments', description: 'Comment list separated by \\n' }
      ],
      codeExample: `{
  "order": 99999
}`
    },
    'status': {
      id: 'status',
      name: 'Order status',
      parameters: [
        { name: 'key', description: 'API Key' },
        { name: 'action', description: '"status"' },
        { name: 'order', description: 'Order ID' }
      ],
      codeExample: `{
  "charge": "2.5",
  "start_count": "168",
  "status": "Completed",
  "remains": "-2"
}`
    },
    'multistatus': {
      id: 'multistatus',
      name: 'Multiple orders status',
      parameters: [
        { name: 'key', description: 'API Key' },
        { name: 'action', description: '"status"' },
        { name: 'orders', description: 'Order IDs separated by comma (E.g: 123,456,789) (Limit 100)' }
      ],
      codeExample: `{
  "123": {
      "charge": "0.27819",
      "start_count": "3572",
      "status": "Partial",
      "remains": "157"
  },
  "456": {
      "error": "Incorrect order ID"
  },
  "789": {
      "charge": "1.44219",
      "start_count": "234",
      "status": "In progress",
      "remains": "10"
  }
}`
    },
    'refill': {
      id: 'refill',
      name: 'Create refill',
      parameters: [
        { name: 'key', description: 'API Key' },
        { name: 'action', description: '"refill"' },
        { name: 'order', description: 'Order ID' }
      ],
      codeExample: `{
  "refill": "1"
}`
    },
    'refills': {
      id: 'refills',
      name: 'Create multiple refill',
      parameters: [
        { name: 'key', description: 'API Key' },
        { name: 'action', description: '"refill"' },
        { name: 'orders', description: 'Order IDs separated by comma (E.g: 123,456,789) (Limit 100)' }
      ],
      codeExample: `{
  "refill": "1"
}`
    },
    'refill_status': {
      id: 'refill_status',
      name: 'Refill status',
      parameters: [
        { name: 'key', description: 'API Key' },
        { name: 'action', description: '"refill_status"' },
        { name: 'refill', description: 'Refill ID' }
      ],
      codeExample: `{
  "status": "Completed"
}`
    },
    'refills_status': {
      id: 'refills_status',
      name: 'Multiple refill status',
      parameters: [
        { name: 'key', description: 'API Key' },
        { name: 'action', description: '"refill_status"' },
        { name: 'refills', description: 'Refill IDs separated by comma (E.g: 123,456,789) (Limit 100)' }
      ],
      codeExample: `[
  {
      "refill": 1,
      "status": "Completed"
  },
  {
      "refill": 2,
      "status": "Rejected"
  },
  {
      "refill": 3,
      "status": {
          "error": "Incorrect refill ID"
      }
  }
]`
    },
    'balance': {
      id: 'balance',
      name: 'Balance',
      parameters: [
        { name: 'key', description: 'API Key' },
        { name: 'action', description: '"balance"' }
      ],
      codeExample: `{
  "balance": "68.6868",
  "currency": "USD"
}`
    }
  };

  constructor(
    private apiKeyService: ApiKeyService,
    private configService: ConfigService
  ) {}

  initialize(): void {
    // Set the API URL from the tenant configuration
    this.apiDetails.url = this.configService.apiUrl?.replace('/v1', '/v2') || '';
    this.loadApiKey();
  }

  ngOnDestroy(): void {
    // Clean up subscriptions to prevent memory leaks
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Load the user's API key
   */
  loadApiKey(): void {
    this.isLoadingApiKey = true;
    const sub = this.apiKeyService.getApiKey()
      .pipe(finalize(() => this.isLoadingApiKey = false))
      .subscribe({
        next: (response) => {
          if (response && response.api_key) {
            // Mask the API key for display
            this.apiDetails.key = response.api_key;
          }
        },
        error: (error) => {
          console.error('Error loading API key:', error);
        }
      });

    this.subscriptions.push(sub);
  }

  /**
   * Generate a new API key
   */
  generateApiKey(): void {
    if (this.isGeneratingApiKey) return;

    this.isGeneratingApiKey = true;
    const sub = this.apiKeyService.generateApiKey()
      .pipe(finalize(() => this.isGeneratingApiKey = false))
      .subscribe({
        next: (response) => {
          if (response && response.api_key) {
            this.apiDetails.key = response.api_key;
          }
        },
        error: (error) => {
          console.error('Error generating API key:', error);
        }
      });

    this.subscriptions.push(sub);
  }

  /**
   * Selects a service and updates the displayed parameters and code example
   * @param serviceId The ID of the service to select
   */
  selectService(serviceId: string): void {
    this.selectedService = serviceId;
    const service = this.serviceData[serviceId];
    if (service) {
      this.parameters = service.parameters;
      this.codeExample = service.codeExample;
    }
  }
}
