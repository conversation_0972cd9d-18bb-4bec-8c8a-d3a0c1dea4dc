package tndung.vnfb.smm.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import tndung.vnfb.smm.anotation.TenantCheck;
import tndung.vnfb.smm.dto.ApiResponseEntity;
import tndung.vnfb.smm.dto.request.PlatformReq;
import tndung.vnfb.smm.dto.response.platform.PlatformRes;
import tndung.vnfb.smm.dto.response.platform.SuperPlatformRes;
import tndung.vnfb.smm.service.PlatformService;

import java.util.List;



@RestController
@RequestMapping("/v1/platforms")
@RequiredArgsConstructor
public class PlatformController {

    private final PlatformService platformService;

    @PostMapping()
    @PreAuthorize("hasAnyRole('ROLE_PANEL','ROLE_ADMIN_PANEL' )")
    @TenantCheck
    public ApiResponseEntity<PlatformRes> add(@RequestBody PlatformReq req) {
        return ApiResponseEntity.success(platformService.add(req));
    }


    @PutMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_PANEL','ROLE_ADMIN_PANEL' )")
    @TenantCheck
    public ApiResponseEntity<PlatformRes> edit(@PathVariable Long id,@RequestBody PlatformReq req) {
        return ApiResponseEntity.success(platformService.edit(id, req));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_PANEL','ROLE_ADMIN_PANEL' )")
    @TenantCheck
    public ApiResponseEntity<String> delete(@PathVariable Long id) {
        platformService.delete(id);
        return ApiResponseEntity.success();
    }

    @GetMapping()
    @PreAuthorize("hasAnyRole('ROLE_USER', 'ROLE_PANEL','ROLE_ADMIN_PANEL' )")
    public ApiResponseEntity<List<PlatformRes>> getAll() {
        return ApiResponseEntity.success(platformService.getAll());
    }

    @GetMapping("/super")
    @PreAuthorize("hasAnyRole('ROLE_PANEL','ROLE_ADMIN_PANEL' )")
    @TenantCheck
    public ApiResponseEntity<List<SuperPlatformRes>> getSuperAll() {
        return ApiResponseEntity.success(platformService.getSuperAll());
    }

    @PatchMapping("/reorder")
    @PreAuthorize("hasAnyRole('ROLE_PANEL','ROLE_ADMIN_PANEL' )")
    @TenantCheck
    public ApiResponseEntity<String> reorder(@RequestParam Long id1,
                                            @RequestParam Long id2) {
        platformService.reorder(id1, id2);
        return ApiResponseEntity.success();
    }


}
