import { Injectable, ElementRef, Renderer2 } from '@angular/core';
import { BehaviorSubject, Subscription } from 'rxjs';
import { IconBaseModel } from '../../../model/base-model';
import { DropdownService } from '../../../core/services/dropdown.service';

export interface IconDropdownState {
  options: IconBaseModel[];
  selectedOption: IconBaseModel | undefined;
  isOpen: boolean;
  placeholder: string;
  iconSize: number;
}

@Injectable()
export class IconDropdownLogicService {
  private subscriptions: Subscription[] = [];

  // State management
  private _state$ = new BehaviorSubject<IconDropdownState>({
    options: [],
    selectedOption: undefined,
    isOpen: false,
    placeholder: 'Chọn một tùy chọn',
    iconSize: 20
  });

  public readonly state$ = this._state$.asObservable();

  // Dropdown management
  private dropdownMenuElement: HTMLElement | null = null;
  private justOpened = false;
  private dropdownId: string = '';
  private closeAllSubscription: Subscription | undefined;

  constructor(
    private dropdownService: DropdownService
  ) {
    this.initialize();
  }

  // Get current state
  get currentState(): IconDropdownState {
    return this._state$.value;
  }

  // Initialize service
  private initialize(): void {
    // Subscribe to close all dropdowns
    this.closeAllSubscription = this.dropdownService.closeAllDropdowns.subscribe(() => {
      if (this.currentState.isOpen) {
        this.updateState({ isOpen: false });
        this.removeDropdownFromDOM();
        document.body.classList.remove('icon-dropdown-open');
      }
    });

    if (this.closeAllSubscription) {
      this.subscriptions.push(this.closeAllSubscription);
    }
  }

  // Update state helper
  private updateState(updates: Partial<IconDropdownState>): void {
    const currentState = this._state$.value;
    this._state$.next({ ...currentState, ...updates });
  }

  // Initialize dropdown with options
  initializeDropdown(options: IconBaseModel[], selectedOption?: IconBaseModel, placeholder?: string, iconSize?: number): void {
    const dropdownId = `icon-dropdown-${Math.random().toString(36).substring(2, 9)}`;
    this.dropdownId = dropdownId;

    this.updateState({
      options,
      selectedOption: selectedOption, // Only use explicitly provided selectedOption
      placeholder: placeholder || 'Chọn một tùy chọn',
      iconSize: iconSize || 20
    });
  }

  // Update options
  updateOptions(options: IconBaseModel[]): void {
    const currentState = this.currentState;
    let newSelectedOption = currentState.selectedOption;

    // Check if current selected option still exists in new options
    if (newSelectedOption && newSelectedOption.id) {
      const existingOption = options.find(o => o.id === newSelectedOption!.id);
      if (!existingOption) {
        newSelectedOption = undefined; // Clear selection if option no longer exists
      }
    }

    this.updateState({
      options,
      selectedOption: newSelectedOption
    });
  }

  // Update selected option
  updateSelectedOption(option: IconBaseModel): void {
    this.updateState({ selectedOption: option });
  }

  // Toggle dropdown
  toggleDropdown(elementRef: ElementRef, renderer: Renderer2, event?: MouseEvent): void {
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }

    const newIsOpen = !this.currentState.isOpen;
    this.updateState({ isOpen: newIsOpen });

    if (newIsOpen) {
      this.dropdownService.openDropdown(this.dropdownId, this);
      this.justOpened = true;

      setTimeout(() => {
        this.updateDropdownPosition(elementRef, renderer);
        document.body.classList.add('icon-dropdown-open');

        const dropdownMenuElement = elementRef.nativeElement.querySelector('.dropdown-menu-container');
        if (dropdownMenuElement) {
          void dropdownMenuElement.offsetHeight;
        }
      }, 0);
    } else {
      this.removeDropdownFromDOM();
      document.body.classList.remove('icon-dropdown-open');
      this.dropdownService.closeDropdown();
    }
  }

  // Select option
  selectOption(option: IconBaseModel, event?: MouseEvent): IconBaseModel {
    if (event) {
      event.stopPropagation();
    }

    this.updateState({
      selectedOption: option,
      isOpen: false
    });

    this.removeDropdownFromDOM();
    document.body.classList.remove('icon-dropdown-open');
    this.dropdownService.closeDropdown();

    return option;
  }

  // Handle document click
  handleDocumentClick(elementRef: ElementRef, event: MouseEvent): void {
    if (this.justOpened) {
      this.justOpened = false;
      return;
    }

    const target = event.target as HTMLElement;
    const isInsideDropdown = elementRef.nativeElement.contains(target);
    const isInsideDropdownMenu = this.dropdownMenuElement && this.dropdownMenuElement.contains(target);

    if (!isInsideDropdown && !isInsideDropdownMenu) {
      this.updateState({ isOpen: false });
      this.removeDropdownFromDOM();
      document.body.classList.remove('icon-dropdown-open');
    }
  }

  // Update dropdown position
  updateDropdownPosition(elementRef: ElementRef, renderer: Renderer2): void {
    if (!this.currentState.isOpen) return;

    const buttonElement = elementRef.nativeElement.querySelector('button');
    if (!buttonElement) return;

    const dropdownMenuElement = elementRef.nativeElement.querySelector('.dropdown-menu-container');
    if (!dropdownMenuElement) return;

    this.dropdownMenuElement = dropdownMenuElement;

    const buttonRect = buttonElement.getBoundingClientRect();
    const top = buttonRect.bottom;
    const left = buttonRect.left;
    const width = buttonRect.width;

    renderer.setStyle(dropdownMenuElement, 'position', 'fixed');
    renderer.setStyle(dropdownMenuElement, 'top', `${top}px`);
    renderer.setStyle(dropdownMenuElement, 'left', `${left}px`);
    renderer.setStyle(dropdownMenuElement, 'width', `${width}px`);
    renderer.setStyle(dropdownMenuElement, 'z-index', '99999');

    const dropdownHeight = dropdownMenuElement.offsetHeight;
    const viewportHeight = window.innerHeight;

    if (top + dropdownHeight > viewportHeight) {
      const newTop = buttonRect.top - dropdownHeight;
      if (newTop >= 0) {
        renderer.setStyle(dropdownMenuElement, 'top', `${newTop}px`);
      } else {
        renderer.setStyle(dropdownMenuElement, 'top', '10px');
        renderer.setStyle(dropdownMenuElement, 'max-height', `${viewportHeight - 20}px`);
        renderer.setStyle(dropdownMenuElement, 'overflow-y', 'auto');
      }
    }
  }

  // Remove dropdown from DOM
  private removeDropdownFromDOM(): void {
    this.dropdownMenuElement = null;
  }

  // Clean up
  destroy(): void {
    this.removeDropdownFromDOM();
    document.body.classList.remove('icon-dropdown-open');
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.subscriptions = [];
  }
}
