package tndung.vnfb.smm.repository.tenant;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import tndung.vnfb.smm.entity.TenantI18nContent;

import java.util.List;
import java.util.Optional;

@Repository
public interface TenantI18nContentRepository extends TenantAwareRepository<TenantI18nContent, Long> {

    /**
     * Find all translations for a specific language and tenant
     */
    @Query("SELECT t FROM TenantI18nContent t WHERE t.tenantId = :tenantId AND t.languageCode = :languageCode")
    List<TenantI18nContent> findByTenantIdAndLanguageCode(@Param("tenantId") String tenantId, @Param("languageCode") String languageCode);

    /**
     * Find specific translation by key, language and tenant
     */
    @Query("SELECT t FROM TenantI18nContent t WHERE t.tenantId = :tenantId AND t.languageCode = :languageCode AND t.translationKey = :key")
    Optional<TenantI18nContent> findByTenantIdAndLanguageCodeAndKey(@Param("tenantId") String tenantId,
                                                                    @Param("languageCode") String languageCode,
                                                                    @Param("key") String key);

    /**
     * Find all translations for a tenant (all languages)
     */
    @Query("SELECT t FROM TenantI18nContent t WHERE t.tenantId = :tenantId ORDER BY t.languageCode, t.translationKey")
    List<TenantI18nContent> findByTenantId(@Param("tenantId") String tenantId);

    /**
     * Find all available language codes for a tenant
     */
    @Query("SELECT DISTINCT t.languageCode FROM TenantI18nContent t WHERE t.tenantId = :tenantId ORDER BY t.languageCode")
    List<String> findDistinctLanguageCodesByTenantId(@Param("tenantId") String tenantId);

    /**
     * Delete all translations for a specific language and tenant (physical delete)
     */
    @Modifying
    @Query("DELETE FROM TenantI18nContent t WHERE t.tenantId = :tenantId AND t.languageCode = :languageCode")
    void deleteByTenantIdAndLanguageCode(@Param("tenantId") String tenantId, @Param("languageCode") String languageCode);

    /**
     * Check if translation exists
     */
    @Query("SELECT COUNT(t) > 0 FROM TenantI18nContent t WHERE t.tenantId = :tenantId AND t.languageCode = :languageCode AND t.translationKey = :key")
    boolean existsByTenantIdAndLanguageCodeAndKey(@Param("tenantId") String tenantId,
                                                  @Param("languageCode") String languageCode,
                                                  @Param("key") String key);
}
