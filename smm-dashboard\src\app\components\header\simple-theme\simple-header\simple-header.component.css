/* Simple Header Theme - Modern Design */
.simple-header-modern {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  box-shadow: 0 4px 20px rgba(30, 41, 59, 0.25);
  margin-bottom: 0;
  position: relative;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.simple-header-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(59, 130, 246, 0.05) 0%, transparent 100%);
  pointer-events: none;
}

.header-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  max-width: 100%;
  position: relative;
  z-index: 1;
}

/* Left Section */
.header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.mobile-toggle-btn {
  display: none;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  cursor: pointer;
  padding: 0.75rem;
  border-radius: 12px;
  color: white;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.mobile-toggle-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.toggle-icon {
  width: 1.25rem;
  height: 1.25rem;
}



/* Center Navigation */
.center-nav {
  flex: 1;
  display: flex;
  justify-content: center;
  margin: 0 2rem;
}

.nav-pills {
  display: flex;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.15);
  padding: 0.5rem;
  border-radius: 16px;
  backdrop-filter: blur(10px);
}

.nav-pill {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.nav-pill::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

.nav-pill:hover::before {
  left: 100%;
}

.nav-pill:hover {
  color: white;
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.nav-pill.active {
  background: rgba(255, 255, 255, 0.25);
  color: white;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.nav-icon {
  width: 1rem;
  height: 1rem;
}

/* Right Section */
.header-right {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.balance-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.75rem 1rem;
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.balance-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: #fbbf24;
}

.balance-info {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.balance-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.balance-value {
  font-size: 0.875rem;
  font-weight: 700;
  color: white;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.action-buttons > * {
  flex-shrink: 0;
}



/* Mobile Responsive */
@media (max-width: 768px) {
  .mobile-toggle-btn {
    display: block;
  }

  .center-nav {
    display: none;
  }

  .header-wrapper {
    padding: 0.75rem 1rem;
  }

  .header-right {
    gap: 0.75rem;
  }

  .balance-card {
    padding: 0.5rem 0.75rem;
  }

  .balance-label {
    font-size: 0.625rem;
  }

  .balance-value {
    font-size: 0.75rem;
  }

  .action-buttons {
    gap: 0.5rem;
  }
}

/* Tablet Responsive */
@media (max-width: 1024px) {
  .center-nav {
    margin: 0 1rem;
  }

  .nav-pills {
    gap: 0.25rem;
  }

  .nav-pill {
    padding: 0.625rem 0.75rem;
    font-size: 0.8125rem;
  }

  .header-right {
    gap: 1rem;
  }
}

/* Focus States */
.mobile-toggle-btn:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.nav-pill:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.action-btn:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}
