import { Component, EventEmitter, Output, OnInit } from '@angular/core';
import { NewTicketLogicService } from './new-ticket.service';

@Component({
  template: ''
})
export abstract class BaseNewTicketComponent implements OnInit {
  @Output() close = new EventEmitter<void>();
  @Output() dismiss = new EventEmitter<void>();
  @Output() ticketCreated = new EventEmitter<any>();

  constructor(protected newTicketLogic: NewTicketLogicService) {}

  ngOnInit(): void {
    this.newTicketLogic.initializeOptions();
  }

  // Expose service properties
  get selectedForm(): string | undefined { return this.newTicketLogic.selectedForm; }
  get formOptions() { return this.newTicketLogic.formOptions; }
  get translatedOptions(): string[] { return this.newTicketLogic.translatedOptions; }
  get orderOptions(): string[] { return this.newTicketLogic.orderOptions; }
  get paymentOptions(): string[] { return this.newTicketLogic.paymentOptions; }
  get requestOptions(): string[] { return this.newTicketLogic.requestOptions; }
  get pointOptions(): string[] { return this.newTicketLogic.pointOptions; }
  get orderId(): string { return this.newTicketLogic.orderId; }
  set orderId(value: string) { this.newTicketLogic.orderId = value; }
  get selectedOrderOption(): string { return this.newTicketLogic.selectedOrderOption; }
  get selectedPaymentOption(): string { return this.newTicketLogic.selectedPaymentOption; }
  get selectedRequestOption(): string { return this.newTicketLogic.selectedRequestOption; }
  get selectedPointOption(): string { return this.newTicketLogic.selectedPointOption; }
  get message(): string { return this.newTicketLogic.message; }
  set message(value: string) { this.newTicketLogic.message = value; }
  get isSubmitting(): boolean { return this.newTicketLogic.isSubmitting; }
  get submitError(): string | null { return this.newTicketLogic.submitError; }

  // Expose service methods
  onClose(): void {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent): void {
    // Kiểm tra nếu click vào chính overlay, không phải bên trong modal
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }

  onDismiss(): void {
    this.dismiss.emit();
  }

  selectOption(value: string): void {
    this.newTicketLogic.selectOption(value);
  }

  selectOrderOption(value: string): void {
    this.newTicketLogic.selectOrderOption(value);
  }

  selectPaymentOption(value: string): void {
    this.newTicketLogic.selectPaymentOption(value);
  }

  selectRequestOption(value: string): void {
    this.newTicketLogic.selectRequestOption(value);
  }

  selectPointOption(value: string): void {
    this.newTicketLogic.selectPointOption(value);
  }

  onSubmit(): void {
    this.newTicketLogic.onSubmit((response) => {
      // Emit the ticketCreated event with the new ticket data
      this.ticketCreated.emit(response);
      this.onClose();
    });
  }
}
