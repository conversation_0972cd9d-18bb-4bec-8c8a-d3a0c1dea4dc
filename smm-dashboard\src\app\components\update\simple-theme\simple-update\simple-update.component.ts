import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { UpdateBaseComponent } from '../../update-base.component';
import { UpdateLogicService } from '../../update.service';
import { SimpleSearchBoxComponent } from '../../../common/search-box/simple-theme/simple-search-box/simple-search-box.component';
import { LiteDropdownComponent } from '../../../common/lite-dropdown/lite-dropdown.component';
import { LoadingComponent } from '../../../common/loading/loading.component';

@Component({
  selector: 'app-simple-update',
  standalone: true,
  imports: [
    FormsModule,
    CommonModule,
    SimpleSearchBoxComponent,
    LiteDropdownComponent,
    TranslateModule,
    LoadingComponent
  ],
  templateUrl: './simple-update.component.html',
  styleUrls: ['./simple-update.component.css'],
  providers: [UpdateLogicService]
})
export class SimpleUpdateComponent extends UpdateBaseComponent {

  getSimpleStatusClass(status: string): string {
    const displayStatus = this.statusMap[status] || status.toLowerCase();

    switch (displayStatus) {
      case 'price_increase':
        return 'simple-status-increase';
      case 'price_decrease':
        return 'simple-status-decrease';
      case 'new':
        return 'simple-status-new';
      case 'on':
        return 'simple-status-on';
      case 'off':
        return 'simple-status-off';
      default:
        return 'simple-status-default';
    }
  }
}
