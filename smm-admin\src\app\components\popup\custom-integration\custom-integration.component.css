/* Overlay and container styles */
.overlay-black {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.layout-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  max-width: 350px;
  width: 50%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.close-button {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  color: #6b7280;
  transition: all 0.2s;
}

.close-button:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.icon-container {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

.header-icon {
  font-size: 48px;
  color: #3b82f6;
}

.header-section {
  text-align: center;
  margin-bottom: 24px;
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
}

.description {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

/* Form styles */
.form-container {
  width: 100%;
}

.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.form-input,
.form-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
  box-sizing: border-box;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.field-error {
  margin-top: 4px;
  font-size: 12px;
  color: #ef4444;
}

.error-message {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 20px;
  font-size: 14px;
}

/* Icon selection styles */
.upload-icon-section {
  margin-bottom: 16px;
}

.upload-icon-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.upload-icon-btn:hover:not(:disabled) {
  background-color: #e5e7eb;
}

.upload-icon-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.icon-selector {
  position: relative;
}

.selected-icon {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  background-color: white;
  transition: border-color 0.2s;
}

.selected-icon:hover {
  border-color: #9ca3af;
}

.icon-display {
  display: flex;
  align-items: center;
}

.no-icon-text,
.icon-name {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #374151;
}

.folder-icon {
  margin-right: 8px;
  color: #6b7280;
}

.uploaded-icon-preview {
  width: 20px;
  height: 20px;
  object-fit: contain;
  margin-right: 8px;
}

.dropdown-arrow {
  color: #6b7280;
}

/* Icon grid styles */
.icon-grid-container {
  position: absolute;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  z-index: 10001;
  max-height: 280px;
  overflow-y: auto;
}

.icon-grid-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.icon-grid-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.icon-grid-close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #6b7280;
  transition: all 0.2s;
}

.icon-grid-close-btn:hover {
  background-color: #e5e7eb;
  color: #374151;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(35px, 1fr));
  gap: 6px;
  padding: 12px;
  max-height: 180px;
  overflow-y: auto;
}

.icon-grid-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 35px;
  height: 35px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  background-color: white;
}

.icon-grid-item:hover {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.icon-grid-item.selected {
  border-color: #3b82f6;
  background-color: #dbeafe;
}

.uploaded-icon-grid {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.no-icon-grid {
  color: #6b7280;
}

/* Submit button */
.save-button {
  width: 100%;
  padding: 12px 24px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 8px;
}

.save-button:hover:not(:disabled) {
  background-color: #2563eb;
}

.save-button:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

/* Utility classes */
.mr-2 {
  margin-right: 8px;
}

.gap-4 {
  gap: 16px;
}

/* Mobile responsive styles */
@media (max-width: 768px) {
  .layout-container {
    max-width: 95%;
    padding: 20px;
    margin: 10px;
  }

  .header-section {
    margin-bottom: 20px;
  }

  .title {
    font-size: 20px;
  }

  .description {
    font-size: 13px;
  }

  .form-group {
    margin-bottom: 14px;
  }

  .icon-grid-container {
    max-height: 240px;
  }

  .icon-grid {
    grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
    gap: 4px;
    padding: 10px;
    max-height: 160px;
  }

  .icon-grid-item {
    width: 32px;
    height: 32px;
  }

  .uploaded-icon-grid {
    width: 18px;
    height: 18px;
  }
}
