package tndung.vnfb.smm.repository.tenant;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import tndung.vnfb.smm.entity.TenantCustomLanguage;

import java.util.List;
import java.util.Optional;

@Repository
public interface TenantCustomLanguageRepository extends TenantAwareRepository<TenantCustomLanguage, Long> {

    /**
     * Find all custom languages for a specific tenant
     */
    List<TenantCustomLanguage> findByTenantIdOrderByLanguageNameAsc(String tenantId);

    /**
     * Find active custom languages for a specific tenant
     */
    List<TenantCustomLanguage> findByTenantIdAndIsActiveTrueOrderByLanguageNameAsc(String tenantId);

    /**
     * Find custom language by tenant and language code
     */
    Optional<TenantCustomLanguage> findByTenantIdAndLanguageCode(String tenantId, String languageCode);

    /**
     * Check if language code exists for tenant
     */
    boolean existsByTenantIdAndLanguageCode(String tenantId, String languageCode);

    /**
     * Delete custom language by tenant and language code
     */
    void deleteByTenantIdAndLanguageCode(String tenantId, String languageCode);

    /**
     * Get all language codes for a tenant (active only)
     */
    @Query("SELECT tcl.languageCode FROM TenantCustomLanguage tcl WHERE tcl.tenantId = :tenantId AND tcl.isActive = true")
    List<String> findLanguageCodesByTenantId(@Param("tenantId") String tenantId);
}
