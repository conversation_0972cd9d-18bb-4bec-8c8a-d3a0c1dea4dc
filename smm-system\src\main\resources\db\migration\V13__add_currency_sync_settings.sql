-- Add currency sync settings to tenant table
ALTER TABLE tenant ADD COLUMN IF NOT EXISTS currency_sync_enabled BOOLEAN DEFAULT true;
ALTER TABLE tenant ADD COLUMN IF NOT EXISTS sync_with_payment BOOLEAN DEFAULT false;
ALTER TABLE tenant ADD COLUMN IF NOT EXISTS last_currency_sync TIMESTAMP;

-- Add sync_enabled field to currency table for individual currency sync control
ALTER TABLE currency ADD COLUMN IF NOT EXISTS sync_enabled BOOLEAN DEFAULT true;
ALTER TABLE currency ADD COLUMN IF NOT EXISTS last_sync TIMESTAMP;

-- Update existing tenants
UPDATE tenant SET currency_sync_enabled = true WHERE currency_sync_enabled IS NULL;
UPDATE tenant SET sync_with_payment = false WHERE sync_with_payment IS NULL;

-- Update existing currencies
UPDATE currency SET sync_enabled = true WHERE sync_enabled IS NULL;
