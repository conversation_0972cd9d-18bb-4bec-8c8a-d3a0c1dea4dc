import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface SearchBoxState {
  searchValue: string;
  placeholder: string;
  buttonText: string;
  buttonIcon: 'search' | 'edit' | 'none';
  buttonPosition: 'left' | 'right';
  showButtonText: boolean;
  containerClass: string;
  inputClass: string;
  buttonClass: string;
}

@Injectable()
export class SearchBoxLogicService {
  private stateSubject = new BehaviorSubject<SearchBoxState>({
    searchValue: '',
    placeholder: 'Tìm kiếm',
    buttonText: 'Tìm kiếm',
    buttonIcon: 'none',
    buttonPosition: 'right',
    showButtonText: true,
    containerClass: '',
    inputClass: 'bg-white',
    buttonClass: 'bg-cyan-500 text-white font-medium'
  });

  state$ = this.stateSubject.asObservable();

  updateState(updates: Partial<SearchBoxState>): void {
    const currentState = this.stateSubject.value;
    this.stateSubject.next({ ...currentState, ...updates });
  }

  updateSearchValue(value: string): void {
    this.updateState({ searchValue: value });
  }

  getSearchValue(): string {
    return this.stateSubject.value.searchValue;
  }

  clearSearch(): void {
    this.updateState({ searchValue: '' });
  }

  setPlaceholder(placeholder: string): void {
    this.updateState({ placeholder });
  }

  setButtonText(buttonText: string): void {
    this.updateState({ buttonText });
  }

  setButtonIcon(buttonIcon: 'search' | 'edit' | 'none'): void {
    this.updateState({ buttonIcon });
  }

  setButtonPosition(buttonPosition: 'left' | 'right'): void {
    this.updateState({ buttonPosition });
  }

  setShowButtonText(showButtonText: boolean): void {
    this.updateState({ showButtonText });
  }

  setContainerClass(containerClass: string): void {
    this.updateState({ containerClass });
  }

  setInputClass(inputClass: string): void {
    this.updateState({ inputClass });
  }

  setButtonClass(buttonClass: string): void {
    this.updateState({ buttonClass });
  }
}
