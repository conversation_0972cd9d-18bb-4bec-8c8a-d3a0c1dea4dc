package tndung.vnfb.smm.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tndung.vnfb.smm.config.TenantContext;
import tndung.vnfb.smm.dto.request.TenantI18nContentReq;
import tndung.vnfb.smm.dto.response.TenantI18nContentRes;
import tndung.vnfb.smm.entity.TenantI18nContent;
import tndung.vnfb.smm.repository.tenant.TenantI18nContentRepository;
import tndung.vnfb.smm.service.TenantI18nContentService;

import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class TenantI18nContentServiceImpl implements TenantI18nContentService {

    private final TenantI18nContentRepository repository;
    private final ObjectMapper objectMapper;

    @Override
    public TenantI18nContentRes getI18nContent(String languageCode) {
        String tenantId = TenantContext.getCurrentTenant();
        log.debug("Getting i18n content for tenant: {} and language: {}", tenantId, languageCode);

        List<TenantI18nContent> contents = repository.findByTenantIdAndLanguageCode(tenantId, languageCode);

        Map<String, String> translations = contents.stream()
                .collect(Collectors.toMap(
                    TenantI18nContent::getTranslationKey,
                    TenantI18nContent::getTranslationValue,
                    (existing, replacement) -> existing
                ));

        // Get default template to calculate statistics
        Map<String, Object> defaultTemplate = getDefaultTemplate();
        int totalKeys = flattenMap(defaultTemplate).size();
        int customizedKeys = translations.size();

        OffsetDateTime lastModified = contents.stream()
                .map(TenantI18nContent::getUpdatedAt)
                .filter(Objects::nonNull)
                .max(OffsetDateTime::compareTo)
                .orElse(null);

        return new TenantI18nContentRes(
                languageCode,
                translations,
                "Custom translations for " + languageCode,
                lastModified,
                totalKeys,
                customizedKeys
        );
    }

    @Override
    @Transactional
    public TenantI18nContentRes updateI18nContent(String languageCode, TenantI18nContentReq request) {
        String tenantId = TenantContext.getCurrentTenant();
        log.debug("Updating i18n content for tenant: {} and language: {}", tenantId, languageCode);

        // Delete existing translations for this language (physical delete)
        repository.deleteByTenantIdAndLanguageCode(tenantId, languageCode);

        // Flatten the nested translations map
        Map<String, String> flatTranslations = flattenMap(request.getTranslations());

        // Save new translations
        List<TenantI18nContent> newContents = flatTranslations.entrySet().stream()
                .map(entry -> {
                    TenantI18nContent content = new TenantI18nContent(
                            languageCode,
                            entry.getKey(),
                            entry.getValue()
                    );
                    content.setDescription(request.getDescription());
                    return content;
                })
                .collect(Collectors.toList());

        repository.saveAll(newContents);

        log.info("Updated {} translations for tenant: {} and language: {}",
                flatTranslations.size(), tenantId, languageCode);

        return getI18nContent(languageCode);
    }

    @Override
    public List<String> getAvailableLanguageCodes() {
        String tenantId = TenantContext.getCurrentTenant();
        return repository.findDistinctLanguageCodesByTenantId(tenantId);
    }

    @Override
    public Map<String, Object> getDashboardTranslations(String languageCode) {
        String tenantId = TenantContext.getCurrentTenant();
        if (tenantId == null) {
            tenantId = TenantContext.getSiteTenant();
        }
        log.debug("Getting dashboard translations for tenant: {} and language: {}", tenantId, languageCode);

        if (tenantId == null) {
            log.warn("No tenant context available, returning default template");
            return getDefaultTemplate();
        }

        List<TenantI18nContent> contents = repository.findByTenantIdAndLanguageCode(tenantId, languageCode);

        if (contents.isEmpty()) {
            // Return default template if no custom translations
            return getDefaultTemplate();
        }

        // Convert flat translations back to nested structure
        Map<String, String> flatTranslations = contents.stream()
                .collect(Collectors.toMap(
                    TenantI18nContent::getTranslationKey,
                    TenantI18nContent::getTranslationValue
                ));

        return unflattenMap(flatTranslations);
    }

    @Override
    @Transactional
    public void deleteI18nContent(String languageCode) {
        String tenantId = TenantContext.getCurrentTenant();
        log.debug("Deleting i18n content for tenant: {} and language: {}", tenantId, languageCode);

        repository.deleteByTenantIdAndLanguageCode(tenantId, languageCode);

        log.info("Deleted translations for tenant: {} and language: {}", tenantId, languageCode);
    }

    @Override
    public Map<String, Object> getDefaultTemplate() {
        return getTemplateForLanguage("en");
    }

    /**
     * Get template for specific language, fallback to English if not found
     */
    public Map<String, Object> getTemplateForLanguage(String languageCode) {
        try {
            // Try to get template for specific language first
            ClassPathResource resource = new ClassPathResource("static/i18n-template/" + languageCode + ".json");

            if (!resource.exists()) {
                log.warn("Template for language {} not found, trying English fallback", languageCode);
                resource = new ClassPathResource("static/i18n-template/en.json");
            }

            if (!resource.exists()) {
                log.warn("Default template en.json not found, returning empty map");
                return new HashMap<>();
            }

            TypeReference<Map<String, Object>> typeRef = new TypeReference<Map<String, Object>>() {};
            Map<String, Object> template = objectMapper.readValue(resource.getInputStream(), typeRef);

            log.info("Loaded template for language: {} (file: {})", languageCode, resource.getFilename());
            return template;
        } catch (IOException e) {
            log.error("Error reading template for language: {}", languageCode, e);
            return new HashMap<>();
        }
    }

    @Override
    @Transactional
    public TenantI18nContentRes importTranslations(String languageCode, Map<String, Object> translations) {
        TenantI18nContentReq request = new TenantI18nContentReq();
        request.setLanguageCode(languageCode);
        request.setTranslations(translations);
        request.setDescription("Imported translations for " + languageCode);

        return updateI18nContent(languageCode, request);
    }

    /**
     * Flatten nested map to dot notation keys
     */
    private Map<String, String> flattenMap(Map<String, Object> map) {
        Map<String, String> result = new HashMap<>();
        flattenMapRecursive(map, "", result);
        return result;
    }

    @SuppressWarnings("unchecked")
    private void flattenMapRecursive(Map<String, Object> map, String prefix, Map<String, String> result) {
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = prefix.isEmpty() ? entry.getKey() : prefix + "." + entry.getKey();
            Object value = entry.getValue();

            if (value instanceof Map) {
                Map<String, Object> nestedMap = (Map<String, Object>) value;
                flattenMapRecursive(nestedMap, key, result);
            } else if (value != null) {
                result.put(key, value.toString());
            }
        }
    }

    /**
     * Convert flat dot notation keys back to nested structure
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> unflattenMap(Map<String, String> flatMap) {
        Map<String, Object> result = new HashMap<>();

        for (Map.Entry<String, String> entry : flatMap.entrySet()) {
            String[] keys = entry.getKey().split("\\.");
            Map<String, Object> current = result;

            for (int i = 0; i < keys.length - 1; i++) {
                current = (Map<String, Object>) current.computeIfAbsent(keys[i], k -> new HashMap<String, Object>());
            }

            current.put(keys[keys.length - 1], entry.getValue());
        }

        return result;
    }
}
