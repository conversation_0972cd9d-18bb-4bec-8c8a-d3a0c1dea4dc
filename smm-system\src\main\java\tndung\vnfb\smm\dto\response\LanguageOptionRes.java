package tndung.vnfb.smm.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LanguageOptionRes {

    private String code;
    private String name;
    private String flag;
    private String type; // "predefined" or "custom"
    private boolean isActive;
    private String description;

    // Constructor for predefined languages
    public LanguageOptionRes(String code, String name, String flag) {
        this.code = code;
        this.name = name;
        this.flag = flag;
        this.type = "predefined";
        this.isActive = true;
        this.description = null;
    }

    // Constructor for custom languages
    public LanguageOptionRes(String code, String name, String flag, boolean isActive, String description) {
        this.code = code;
        this.name = name;
        this.flag = flag;
        this.type = "custom";
        this.isActive = isActive;
        this.description = description;
    }
}
