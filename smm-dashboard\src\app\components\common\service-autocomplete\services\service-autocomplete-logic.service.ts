import { Injectable, <PERSON><PERSON><PERSON>roy, <PERSON>ementRef, HostListener } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { SuperGeneralSvRes } from '../../../../model/response/super-general-sv.model';

export interface ServiceAutocompleteState {
  services: SuperGeneralSvRes[];
  isVisible: boolean;
  highlightedIndex: number;
}

export interface ServiceAutocompleteInputs {
  services: SuperGeneralSvRes[];
  isVisible: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class ServiceAutocompleteLogicService implements OnDestroy {
  private stateSubject = new BehaviorSubject<ServiceAutocompleteState>({
    services: [],
    isVisible: false,
    highlightedIndex: -1
  });

  public state$ = this.stateSubject.asObservable();
  private selectedCallback?: (service: SuperGeneralSvRes) => void;
  private visibilityCallback?: (visible: boolean) => void;

  constructor() {
    this.setupDocumentListeners();
  }

  updateInputs(inputs: ServiceAutocompleteInputs): void {
    this.updateState(inputs);
  }

  updateServices(services: SuperGeneralSvRes[]): void {
    this.updateState({ 
      services,
      highlightedIndex: -1 // Reset highlighted index when services change
    });
  }

  setupEventHandlers(
    selectedCallback: (service: SuperGeneralSvRes) => void,
    visibilityCallback: (visible: boolean) => void
  ): void {
    this.selectedCallback = selectedCallback;
    this.visibilityCallback = visibilityCallback;
  }

  selectService(service: SuperGeneralSvRes): void {
    if (this.selectedCallback) {
      this.selectedCallback(service);
    }
    this.updateState({ 
      isVisible: false,
      highlightedIndex: -1 
    });
    if (this.visibilityCallback) {
      this.visibilityCallback(false);
    }
  }

  getHighlightedIndex(): number {
    return this.stateSubject.value.highlightedIndex;
  }

  private setupDocumentListeners(): void {
    document.addEventListener('click', this.onDocumentClick.bind(this));
    document.addEventListener('keydown', this.onKeyDown.bind(this));
  }

  private onDocumentClick(event: MouseEvent): void {
    // This will be handled by the component's HostListener
    // Keep this for potential future use
  }

  private onKeyDown(event: KeyboardEvent): void {
    const currentState = this.stateSubject.value;
    
    // Only handle keyboard events when the autocomplete is visible
    if (!currentState.isVisible || currentState.services.length === 0) {
      return;
    }

    // Handle arrow down
    if (event.key === 'ArrowDown') {
      const newIndex = Math.min(currentState.highlightedIndex + 1, currentState.services.length - 1);
      this.updateState({ highlightedIndex: newIndex });
      event.preventDefault();
    }

    // Handle arrow up
    if (event.key === 'ArrowUp') {
      const newIndex = Math.max(currentState.highlightedIndex - 1, 0);
      this.updateState({ highlightedIndex: newIndex });
      event.preventDefault();
    }

    // Handle Enter key
    if (event.key === 'Enter' && currentState.highlightedIndex >= 0) {
      this.selectService(currentState.services[currentState.highlightedIndex]);
      event.preventDefault();
    }

    // Handle Escape key
    if (event.key === 'Escape') {
      this.updateState({ isVisible: false });
      if (this.visibilityCallback) {
        this.visibilityCallback(false);
      }
      event.preventDefault();
    }
  }

  private updateState(partialState: Partial<ServiceAutocompleteState>): void {
    const currentState = this.stateSubject.value;
    this.stateSubject.next({ ...currentState, ...partialState });
  }

  ngOnDestroy(): void {
    this.destroy();
  }

  destroy(): void {
    document.removeEventListener('click', this.onDocumentClick.bind(this));
    document.removeEventListener('keydown', this.onKeyDown.bind(this));
  }
}
