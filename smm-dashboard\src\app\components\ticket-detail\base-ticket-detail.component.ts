import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>nit, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ElementRef, AfterViewChecked } from '@angular/core';
import { Observable } from 'rxjs';
import { TicketDetailLogicService, TicketDetailState } from './services/ticket-detail-logic.service';

@Component({
  template: ''
})
export abstract class BaseTicketDetailComponent implements OnInit, OnDestroy, AfterViewChecked {
  // State from service
  state$: Observable<TicketDetailState>;
  
  @ViewChild('chatContainer') private chatContainer: ElementRef | undefined;
  private shouldScrollToBottom: boolean = false;

  constructor(protected ticketDetailLogicService: TicketDetailLogicService) {
    this.state$ = this.ticketDetailLogicService.state$;
  }

  ngOnInit(): void {
    // Initialize the service
    this.ticketDetailLogicService.initialize();
  }

  ngOnDestroy(): void {
    // Cleanup is handled by the service
    this.ticketDetailLogicService.destroy();
  }

  ngAfterViewChecked(): void {
    if (this.shouldScrollToBottom) {
      this.scrollToBottom();
      this.shouldScrollToBottom = false;
    }
  }

  private scrollToBottom(): void {
    if (this.chatContainer) {
      try {
        this.chatContainer.nativeElement.scrollTop = this.chatContainer.nativeElement.scrollHeight;
      } catch (err) {
        console.error('Error scrolling to bottom:', err);
      }
    }
  }

  // Delegate methods to TicketDetailLogicService for template compatibility
  loadTicketDetails(): void {
    this.ticketDetailLogicService.loadTicketDetails();
    this.shouldScrollToBottom = true;
  }

  sendReply(): void {
    this.ticketDetailLogicService.sendReply();
  }

  isTicketClosed(): boolean {
    return this.ticketDetailLogicService.isTicketClosed();
  }

  updateReplyMessage(message: string): void {
    this.ticketDetailLogicService.updateReplyMessage(message);
  }

  dismissApiTimeoutError(): void {
    this.ticketDetailLogicService.dismissApiTimeoutError();
  }
}
