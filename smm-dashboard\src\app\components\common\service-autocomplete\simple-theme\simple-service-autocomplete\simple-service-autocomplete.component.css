/* Simple Theme Service Autocomplete Styles */
.simple-autocomplete-container {
  position: absolute;
  width: 100%;
  z-index: 50;
  margin-top: 4px;
}

.simple-autocomplete-list {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  max-height: 24rem;
  overflow-y: auto;
}

.simple-autocomplete-item {
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.simple-autocomplete-item:last-child {
  border-bottom: none;
}

.simple-autocomplete-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(4px);
}

.simple-autocomplete-item.simple-highlighted {
  background: rgba(255, 255, 255, 0.2);
  transform: translateX(4px);
}

.simple-service-content {
  color: white;
}

.simple-no-results {
  padding: 2rem 1rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.simple-search-icon {
  font-size: 1.5rem;
  opacity: 0.6;
}

/* Custom scrollbar for the list */
.simple-autocomplete-list::-webkit-scrollbar {
  width: 6px;
}

.simple-autocomplete-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.simple-autocomplete-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.simple-autocomplete-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
