import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { BehaviorSubject, Subscription } from 'rxjs';
import { take, map } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';

// Services
import { UserService } from '../../../core/services/user.service';
import { ToastService } from '../../../core/services/toast.service';
import { LanguageService } from '../../../core/services/language.service';
import { TenantSettingsService } from '../../../core/services/tenant-settings.service';
import { TenantCurrencyService } from '../../../core/services/tenant-currency.service';
import { CurrencyService } from '../../../core/services/currency.service';
import { ThemeService, LayoutTheme } from '../../../core/services/theme.service';
import { NotifyType } from '../../../constant/notify-type';

// Models
import { UserRes } from '../../../model/response/user-res.model';
import { LoginHistoryItem } from '../../../model/response/login-history.model';
import { CurrencyReq } from '../../../model/request/currency-req.model';
import { EditUserReq } from '../../../model/request/edit-user-req.model';

export interface ProfileState {
  // User data
  user: UserRes | undefined;
  formattedBalance: string;
  
  // Forms
  profileForm: FormGroup;
  passwordForm: FormGroup;
  
  // UI state
  isLoading: boolean;
  isPasswordLoading: boolean;
  isVerifying: boolean;
  isLoadingHistory: boolean;
  passwordError: string | null;
  profileError: string | null;
  
  // Tab navigation
  activeTab: 'account' | 'security' | 'settings' | 'history';
  
  // Password visibility toggles
  showCurrentPassword: boolean;
  showNewPassword: boolean;
  showConfirmPassword: boolean;
  
  // Pagination for login history
  currentPage: number;
  totalPages: number;
  itemsPerPage: number;
  
  // Security options
  is2FAEnabled: boolean;

  // MFA popup states
  showMfaSettings: boolean;
  showMfaDisabled: boolean;
  
  // Login history
  loginHistory: LoginHistoryItem[];
  
  // Currency options
  currencyOptions: string[];
  selectedCurrency: string;
  
  // Theme management
  currentTheme: LayoutTheme;

  // Language management
  currentLanguageCode: string;
  currentLanguageName: string;
}

@Injectable({
  providedIn: 'root'
})
export class ProfileLogicService {
  private subscriptions: Subscription[] = [];

  // State management
  private _state$ = new BehaviorSubject<ProfileState>({
    user: undefined,
    formattedBalance: '',
    profileForm: this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      phone: ['']
    }),
    passwordForm: this.fb.group({
      currentPassword: ['', Validators.required],
      newPassword: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', Validators.required]
    }, { validators: this.passwordMatchValidator }),
    isLoading: false,
    isPasswordLoading: false,
    isVerifying: false,
    isLoadingHistory: false,
    passwordError: null,
    profileError: null,
    activeTab: 'account',
    showCurrentPassword: false,
    showNewPassword: false,
    showConfirmPassword: false,
    currentPage: 1,
    totalPages: 1,
    itemsPerPage: 10,
    is2FAEnabled: false,
    showMfaSettings: false,
    showMfaDisabled: false,
    loginHistory: [],
    currencyOptions: [],
    selectedCurrency: '',
    currentTheme: LayoutTheme.DEFAULT,
    currentLanguageCode: 'vi',
    currentLanguageName: 'Tiếng Việt'
  });

  // Public state observable
  public readonly state$ = this._state$.asObservable();

  // Current theme observable
  public readonly currentTheme$ = this._state$.pipe(
    map(state => state.currentTheme)
  );

  // Current state getter
  private get currentState(): ProfileState {
    return this._state$.value;
  }

  // Predefined languages list (same as lang-dropdown)
  private readonly allLanguages = [
    { flag: 'fi fi-vn', code: 'vi', name: 'Tiếng Việt' },
    { flag: 'fi fi-us', code: 'en', name: 'English' },
    { flag: 'fi fi-cn', code: 'cn', name: '中文' },
    { flag: 'fi fi-fr', code: 'fr', name: 'Français' },
    { flag: 'fi fi-es', code: 'es', name: 'Español' },
    { flag: 'fi fi-de', code: 'de', name: 'Deutsch' },
    { flag: 'fi fi-jp', code: 'ja', name: '日本語' },
    { flag: 'fi fi-kr', code: 'ko', name: '한국어' },
    { flag: 'fi fi-ru', code: 'ru', name: 'Русский' },
    { flag: 'fi fi-pt', code: 'pt', name: 'Português' },
    { flag: 'fi fi-it', code: 'it', name: 'Italiano' },
    { flag: 'fi fi-nl', code: 'nl', name: 'Nederlands' },
    { flag: 'fi fi-pl', code: 'pl', name: 'Polski' },
    { flag: 'fi fi-tr', code: 'tr', name: 'Türkçe' },
    { flag: 'fi fi-ar', code: 'ar', name: 'العربية' },
    { flag: 'fi fi-th', code: 'th', name: 'ไทย' },
    { flag: 'fi fi-id', code: 'id', name: 'Bahasa Indonesia' },
    { flag: 'fi fi-my', code: 'ms', name: 'Bahasa Malaysia' },
    { flag: 'fi fi-ph', code: 'tl', name: 'Filipino' },
    { flag: 'fi fi-in', code: 'hi', name: 'हिन्दी' }
  ];

  constructor(
    private fb: FormBuilder,
    private userService: UserService,
    private toastService: ToastService,
    private languageService: LanguageService,
    private tenantSettingsService: TenantSettingsService,
    private tenantCurrencyService: TenantCurrencyService,
    private currencyService: CurrencyService,
    private themeService: ThemeService,
    private route: ActivatedRoute,
    private router: Router,
    private translate: TranslateService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.initialize();
  }

  private initialize(): void {
    // Subscribe to theme changes
    const themeSub = this.themeService.currentLayoutTheme$.subscribe((theme: LayoutTheme) => {
      this.updateState({ currentTheme: theme });
    });
    this.subscriptions.push(themeSub);

    // Subscribe to route query parameters for tab navigation
    const routeSub = this.route.queryParams.subscribe(params => {
      if (params['tab']) {
        const tabParam = params['tab'];
        if (tabParam === 'account' || tabParam === 'security' ||
            tabParam === 'settings' || tabParam === 'history') {
          this.updateState({ activeTab: tabParam as 'account' | 'security' | 'settings' | 'history' });
        }
      }
    });
    this.subscriptions.push(routeSub);

    // Load currency options
    this.loadCurrencyOptions();

    // Load current language
    this.loadCurrentLanguage();

    // Subscribe to user data
    const userSub = this.userService.user$.subscribe(user => {
      this.updateState({ user });

      if (user) {
        // Populate form with user data
        this.currentState.profileForm.patchValue({
          email: user.email || '',
          phone: user.phone || ''
        });

        const formattedBalance = this.currencyService.formatPrice(user.balance);
        this.updateState({ formattedBalance });

        // Set currency based on user's preferred currency
        if (user.preferred_currency) {
          const currencyCode = user.preferred_currency.code;
          const matchingCurrency = this.currentState.currencyOptions.find(option =>
            option.startsWith(currencyCode)
          );

          if (matchingCurrency) {
            this.updateState({ selectedCurrency: matchingCurrency });
          }
        }

        // Load login history and security settings
        this.loadLoginHistory();
        this.loadSecuritySettings();
      } else {
        // If user is not loaded yet, trigger a fetch
        this.userService.get$.next();
      }
    });
    this.subscriptions.push(userSub);
  }

  private updateState(partialState: Partial<ProfileState>): void {
    this._state$.next({ ...this.currentState, ...partialState });
  }

  // Public getters for template access
  get user(): UserRes | undefined {
    return this.currentState.user;
  }

  get formattedBalance(): string {
    return this.currentState.formattedBalance;
  }

  get profileForm(): FormGroup {
    return this.currentState.profileForm;
  }

  get passwordForm(): FormGroup {
    return this.currentState.passwordForm;
  }

  get isLoading(): boolean {
    return this.currentState.isLoading;
  }

  get isPasswordLoading(): boolean {
    return this.currentState.isPasswordLoading;
  }

  get isVerifying(): boolean {
    return this.currentState.isVerifying;
  }

  get isLoadingHistory(): boolean {
    return this.currentState.isLoadingHistory;
  }

  get passwordError(): string | null {
    return this.currentState.passwordError;
  }

  get profileError(): string | null {
    return this.currentState.profileError;
  }

  get activeTab(): 'account' | 'security' | 'settings' | 'history' {
    return this.currentState.activeTab;
  }

  get showCurrentPassword(): boolean {
    return this.currentState.showCurrentPassword;
  }

  get showNewPassword(): boolean {
    return this.currentState.showNewPassword;
  }

  get showConfirmPassword(): boolean {
    return this.currentState.showConfirmPassword;
  }

  get currentPage(): number {
    return this.currentState.currentPage;
  }

  get totalPages(): number {
    return this.currentState.totalPages;
  }

  get itemsPerPage(): number {
    return this.currentState.itemsPerPage;
  }

  get is2FAEnabled(): boolean {
    return this.currentState.is2FAEnabled;
  }

  get showMfaSettings(): boolean {
    return this.currentState.showMfaSettings;
  }

  get showMfaDisabled(): boolean {
    return this.currentState.showMfaDisabled;
  }

  get loginHistory(): LoginHistoryItem[] {
    return this.currentState.loginHistory;
  }

  get currencyOptions(): string[] {
    return this.currentState.currencyOptions;
  }

  get selectedCurrency(): string {
    return this.currentState.selectedCurrency;
  }

  get currentLanguageCode(): string {
    return this.currentState.currentLanguageCode;
  }

  get currentLanguageName(): string {
    return this.currentState.currentLanguageName;
  }

  // Public methods for component interaction
  setActiveTab(tab: 'account' | 'security' | 'settings' | 'history'): void {
    this.updateState({ activeTab: tab });
  }

  togglePasswordVisibility(field: 'current' | 'new' | 'confirm'): void {
    const updates: Partial<ProfileState> = {};
    switch (field) {
      case 'current':
        updates.showCurrentPassword = !this.currentState.showCurrentPassword;
        break;
      case 'new':
        updates.showNewPassword = !this.currentState.showNewPassword;
        break;
      case 'confirm':
        updates.showConfirmPassword = !this.currentState.showConfirmPassword;
        break;
    }
    this.updateState(updates);
  }

  private loadCurrencyOptions(): void {
    // Load currency options from tenant currency service
    this.tenantCurrencyService.getAvailableCurrencies().subscribe({
      next: (currencies: any[]) => {
        const options = currencies.map((currency: any) => {
          return `${currency.code} - ${currency.name}`;
        });
        this.updateState({ currencyOptions: options });
      },
      error: (error: any) => {
        console.error('Error loading currency options:', error);
        // Fallback to default options
        const options = ['USD - US Dollar', 'EUR - Euro', 'VND - Vietnamese Dong'];
        this.updateState({ currencyOptions: options });
      }
    });
  }

  private loadCurrentLanguage(): void {
    if (!isPlatformBrowser(this.platformId)) return;

    // Get current language from localStorage and TranslateService
    const currentLang = localStorage.getItem('language') || this.translate.currentLang || 'vi';
    const languageObj = this.allLanguages.find(l => l.code === currentLang);

    if (languageObj) {
      this.updateState({
        currentLanguageCode: languageObj.code,
        currentLanguageName: languageObj.name
      });
    }

    // Subscribe to translate service language changes
    const languageSubscription = this.translate.onLangChange.subscribe((event: any) => {
      const languageObj = this.allLanguages.find(l => l.code === event.lang);
      if (languageObj) {
        this.updateState({
          currentLanguageCode: languageObj.code,
          currentLanguageName: languageObj.name
        });
      }
    });
    this.subscriptions.push(languageSubscription);
  }

  private loadLoginHistory(): void {
    this.updateState({ isLoadingHistory: true });

    // Load login history from user service
    this.userService.getLoginHistory().subscribe({
      next: (response: any) => {
        const loginHistory = Array.isArray(response) ? response : response.content || [];
        this.updateState({
          loginHistory,
          isLoadingHistory: false
        });
      },
      error: (error: any) => {
        console.error('Error loading login history:', error);
        this.updateState({
          loginHistory: [],
          isLoadingHistory: false
        });
      }
    });
  }

  private loadSecuritySettings(): void {
    // Get MFA status from user data
    const user = this.currentState.user;
    const is2FAEnabled = user?.mfa_enabled || false;
    this.updateState({ is2FAEnabled });
  }

  getAvatarPath(): string {
    const user = this.currentState.user;
    if (user?.avatar) {
      return `assets/images/${user.avatar}.png`;
    }
    return 'assets/images/default-avatar.png';
  }

  // Password match validator
  private passwordMatchValidator(group: FormGroup): { [key: string]: any } | null {
    const newPassword = group.get('newPassword');
    const confirmPassword = group.get('confirmPassword');

    if (newPassword && confirmPassword && newPassword.value !== confirmPassword.value) {
      return { passwordMismatch: true };
    }
    return null;
  }

  // Profile update functionality
  updateProfile(): void {
    const profileForm = this.currentState.profileForm;

    if (profileForm.invalid) {
      // Mark all fields as touched to show validation errors
      Object.keys(profileForm.controls).forEach(key => {
        const control = profileForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.updateState({ isLoading: true, profileError: null });

    const profileData = {
      email: profileForm.value.email,
      phone: profileForm.value.phone
    };

    this.userService.updateInfo(profileData).subscribe({
      next: (updatedUser: any) => {
        this.updateState({ isLoading: false });
        this.toastService.showToast('Profile updated successfully', NotifyType.SUCCESS);
        // Update user data in state
        this.updateState({ user: updatedUser });
      },
      error: (err: any) => {
        this.updateState({ isLoading: false });
        console.error('Profile update error:', err);

        let errorMessage = 'Failed to update profile. Please try again.';
        if (err && err.message) {
          errorMessage = err.message;
        }

        this.updateState({ profileError: errorMessage });
        this.toastService.showToast(errorMessage, NotifyType.ERROR);
      }
    });
  }

  // Password change functionality
  changePassword(): void {
    const passwordForm = this.currentState.passwordForm;

    if (passwordForm.invalid) {
      // Mark all fields as touched to show validation errors
      Object.keys(passwordForm.controls).forEach(key => {
        const control = passwordForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.updateState({ isPasswordLoading: true, passwordError: null });

    const passwordData = {
      old_password: passwordForm.value.currentPassword,
      new_password: passwordForm.value.newPassword
    };

    this.userService.changePass(passwordData).subscribe({
      next: () => {
        this.updateState({ isPasswordLoading: false });
        this.toastService.showToast('Password updated successfully', NotifyType.SUCCESS);
        // Reset form
        passwordForm.reset();
      },
      error: (err: any) => {
        this.updateState({ isPasswordLoading: false });
        console.error('Password update error:', err);

        // Set error message based on the response
        let errorMessage = 'Failed to update password. Please try again.';
        if (err && err.message) {
          errorMessage = err.message;
        }

        this.updateState({ passwordError: errorMessage });
        this.toastService.showToast(errorMessage, NotifyType.ERROR);
      }
    });
  }

  // MFA-related methods
  toggle2FA(): void {
    if (this.currentState.is2FAEnabled) {
      this.updateState({ showMfaDisabled: true });
    } else {
      this.updateState({ showMfaSettings: true });
    }
  }

  closeMfaSettings(): void {
    this.updateState({ showMfaSettings: false });
  }

  closeMfaDisabled(): void {
    this.updateState({ showMfaDisabled: false });
  }

  onMfaEnabled(): void {
    this.updateState({
      showMfaSettings: false,
      is2FAEnabled: true
    });
    // Refresh user data to get updated MFA status
    this.userService.get$.next();
  }

  onMfaDisabled(): void {
    this.updateState({
      showMfaDisabled: false,
      is2FAEnabled: false
    });
    // Refresh user data to get updated MFA status
    this.userService.get$.next();
  }

  // Currency change functionality
  changeCurrency(currencyOption: string): void {
    // Extract currency code from option (e.g., "USD - US Dollar" -> "USD")
    const currencyCode = currencyOption.split(' - ')[0];

    this.userService.setCurrency({ code: currencyCode }).subscribe({
      next: (updatedUser: any) => {
        this.updateState({
          selectedCurrency: currencyOption,
          user: updatedUser
        });
        this.toastService.showToast('Currency updated successfully', NotifyType.SUCCESS);

        // Update formatted balance with new currency
        const formattedBalance = this.currencyService.formatPrice(updatedUser.balance);
        this.updateState({ formattedBalance });
      },
      error: (err: any) => {
        console.error('Currency update error:', err);
        this.toastService.showToast('Failed to update currency', NotifyType.ERROR);
      }
    });
  }

  // Language change functionality
  changeLanguage(languageCode: string): void {
    this.languageService.changeLanguage(languageCode).then(() => {
      this.toastService.showToast('Language updated successfully', NotifyType.SUCCESS);
    }).catch((err: any) => {
      console.error('Language update error:', err);
      this.toastService.showToast('Failed to update language', NotifyType.ERROR);
    });
  }

  // Navigation methods
  navigateToTab(tab: 'account' | 'security' | 'settings' | 'history'): void {
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { tab },
      queryParamsHandling: 'merge'
    });
  }

  // Refresh data methods
  refreshLoginHistory(): void {
    this.loadLoginHistory();
  }

  refreshUserData(): void {
    this.userService.get$.next();
  }

  destroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.subscriptions = [];
  }
}
