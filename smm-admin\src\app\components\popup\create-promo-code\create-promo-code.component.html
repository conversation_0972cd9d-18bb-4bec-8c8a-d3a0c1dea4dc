<div class="overlay-black" (click)="onClose()">
  <div class="modal-container" (click)="$event.stopPropagation()">
    <div class="modal-content">
      <!-- Header -->
      <div class="modal-header">
        <h2 class="modal-title">{{ 'promo_codes.create_promo_code' | translate }}</h2>
        <button class="close-button" (click)="onClose()">
          <fa-icon [icon]="['fas', 'times']" class="text-lg"></fa-icon>
        </button>
      </div>

      <!-- Error message -->
      <div *ngIf="errorMessage" class="error-message">
        <fa-icon [icon]="['fas', 'exclamation-circle']" class="mr-2"></fa-icon>
        {{ errorMessage }}
      </div>

      <!-- Form -->
      <form (ngSubmit)="onSubmit()" class="promo-form">
        <!-- Promo code type -->
        <div class="form-group">
          <label class="form-label">{{ 'promo_codes.promo_code_type' | translate }}</label>
          <app-lite-dropdown
            [options]="promoCodeTypes"
            [selectedOption]="selectedType"
            (selected)="onSelectType($event)"
            customClassButton="bg-[#f5f7fc] rounded-lg"
            customClassDropdown="w-full">
          </app-lite-dropdown>
        </div>

        <!-- Promo code -->
        <div class="form-group">
          <label class="form-label">{{ 'promo_codes.promo_code' | translate }}</label>
          <div class="relative">
            <input
              type="text"
              [(ngModel)]="promoCode"
              name="promoCode"
              class="form-input pr-20"
              placeholder="{{ 'promo_codes.example_code' | translate }}">
            <button
              type="button"
              (click)="generateRandomCode()"
              class="random-button">
              <fa-icon [icon]="['fas', 'random']"></fa-icon>
              <span class="ml-1 text-xs">{{ 'promo_codes.random' | translate }}</span>
            </button>
          </div>
        </div>

        <!-- Promo code amount -->
        <div class="form-group">
          <label class="form-label">{{ 'promo_codes.promo_code_amount' | translate }}</label>
          <div class="relative">
            <input
              type="number"
              [(ngModel)]="discountValue"
              name="discountValue"
              class="form-input"
              [placeholder]="isDiscountType() ? ('promo_codes.enter_percentage' | translate) : ('promo_codes.enter_amount' | translate)"
              min="0"
              [max]="isDiscountType() ? 100 : null"
              [step]="isDiscountType() ? 1 : 0.01">
            <span class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500">
              {{ isDiscountType() ? '%' : '$' }}
            </span>
          </div>
        </div>

        <!-- Activations count -->
        <div class="form-group">
          <label class="form-label">{{ 'promo_codes.activations_count' | translate }}</label>
          <input
            type="number"
            [(ngModel)]="activationsCount"
            name="activationsCount"
            class="form-input"
            placeholder="{{ 'promo_codes.enter_activations' | translate }}"
            min="1">
        </div>

        <!-- Submit button -->
        <button
          type="submit"
          class="save-button"
          [disabled]="isSubmitting">
          {{ isSubmitting ? ('promo_codes.creating_promo' | translate) : ('promo_codes.create_promo' | translate) }}
        </button>
      </form>
    </div>
  </div>
</div>
