package tndung.vnfb.smm.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import tndung.vnfb.smm.anotation.TenantCheck;
import tndung.vnfb.smm.dto.ApiResponseEntity;
import tndung.vnfb.smm.dto.PanelNotificationReq;
import tndung.vnfb.smm.dto.PanelNotificationRes;

import tndung.vnfb.smm.service.PanelNotificationService;

import java.util.List;

@RestController
@RequestMapping("/v1/panel-notifications")
@RequiredArgsConstructor
public class PanelNotificationController {
    
    private final PanelNotificationService panelNotificationService;
    
    @PostMapping
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<PanelNotificationRes> createNotification(@RequestBody PanelNotificationReq req) {
        PanelNotificationRes notification = panelNotificationService.createNotification(req);
        return ApiResponseEntity.success(notification);
    }
    
    @GetMapping
    @PreAuthorize("hasAnyRole('ROLE_PANEL',  'ROLE_ADMIN_PANEL', 'ROLE_USER')")
    @TenantCheck
    public ApiResponseEntity<Page<PanelNotificationRes>> getNotifications(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<PanelNotificationRes> notifications = panelNotificationService.getNotifications(pageable);
        return ApiResponseEntity.success(notifications);
    }
    
    @GetMapping("/unread")
    @PreAuthorize("hasAnyRole('ROLE_PANEL',  'ROLE_ADMIN_PANEL', 'ROLE_USER')")
    @TenantCheck
    public ApiResponseEntity<List<PanelNotificationRes>> getUnreadNotifications() {
        List<PanelNotificationRes> notifications = panelNotificationService.getUnreadNotifications();
        return ApiResponseEntity.success(notifications);
    }
    
    @GetMapping("/unread/count")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL', 'ROLE_USER')")
    @TenantCheck
    public ApiResponseEntity<Long> getUnreadCount() {
        long count = panelNotificationService.getUnreadCount();
        return ApiResponseEntity.success(count);
    }
    
    @PostMapping("/{id}/read")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL', 'ROLE_USER')")
    @TenantCheck
    public ApiResponseEntity<String> markAsRead(@PathVariable Long id) {
        panelNotificationService.markAsRead(id);
        return ApiResponseEntity.success("Notification marked as read");
    }
    
    @PostMapping("/read-all")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL', 'ROLE_USER')")
    @TenantCheck
    public ApiResponseEntity<String> markAllAsRead() {
        panelNotificationService.markAllAsRead();
        return ApiResponseEntity.success("All notifications marked as read");
    }
}
