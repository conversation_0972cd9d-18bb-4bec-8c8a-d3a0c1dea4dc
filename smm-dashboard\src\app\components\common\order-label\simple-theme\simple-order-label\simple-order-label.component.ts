import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Order } from '../../../../../model/g-service';
import { OrderLabelState } from '../../services/order-label-logic.service';

@Component({
  selector: 'app-simple-order-label',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './simple-order-label.component.html',
  styleUrl: './simple-order-label.component.css'
})
export class SimpleOrderLabelComponent {
  @Input() orderLabelState: OrderLabelState | null = null;
  @Input() order: Order | Order = {} as Order;
  @Input() showCopyId: boolean = false;
  @Output() copyIdClicked = new EventEmitter<void>();

  onCopyId(): void {
    this.copyIdClicked.emit();
  }
}
