<div class="simple-new-order-container" *ngIf="state$ | async as state">
  
  <!-- Header Actions -->
  <div class="action-buttons">
    <button
      class="action-btn"
      [class.active]="state.activeButton === 'new_order'"
      (click)="setActiveButton('new_order')">
      {{ 'new_order' | translate }}
    </button>

    <button
      class="action-btn"
      [class.active]="state.activeButton === 'favorite'"
      (click)="setActiveButton('favorite')">
      {{ 'favorite' | translate }}
    </button>

    <button
      class="action-btn"
      [class.active]="state.activeButton === 'auto_subscription'"
      (click)="setActiveButton('auto_subscription')">
      {{ 'auto_subscription' | translate }}
    </button>
  </div>

  <!-- Order Success Component -->
  <app-order-success 
    *ngIf="state.isOrderSuccess" 
    [order]="state.createdOrder" 
    (newOrder)="resetOrderState()">
  </app-order-success>

  <!-- Search -->
  <div class="search-section">
    <div class="search-input-wrapper">
      <fa-icon [icon]='["fas", "search"]' class="search-icon"></fa-icon>
      <input
        placeholder="{{ 'simple_theme.search_placeholder' | translate }}"
        (input)="onSearchInput($event)"
        (click)="onSearchClick()"
        (keydown)="handleSearchKeydown($event)"
        [ngModel]="state.searchTerm"
        class="search-input" />

      <!-- Autocomplete Results -->
      <app-service-autocomplete
        [services]="state.searchResults"
        [isVisible]="state.showAutocomplete"
        (selected)="onServiceSelected($event)"
        (visibilityChange)="onAutocompleteVisibilityChange($event)">
      </app-service-autocomplete>
    </div>
  </div>

  <!-- Category -->
  <div class="form-section" *ngIf="state.activeButton !== 'favorite'">
    <h3 class="section-title">{{ 'classification' | translate }}</h3>

    <app-simple-icon-dropdown
      #categoryDropdown
      *ngIf="state.categories.length > 0"
      [options]="state.categories"
      [selectedOption]="state.selectedCategory"
      (selected)="onCategorySelected($event)">
    </app-simple-icon-dropdown>

    <div *ngIf="state.categories.length === 0" class="empty-state">
      <p>{{ 'no_services_available' | translate }}</p>
    </div>
  </div>

  <!-- Service -->
  <div class="form-section">
    <h3 class="section-title">
      <ng-container *ngIf="state.activeButton === 'favorite'">{{ 'favorite_services' | translate }}</ng-container>
      <ng-container *ngIf="state.activeButton !== 'favorite'">{{ 'service' | translate }}</ng-container>
    </h3>

    <!-- Service dropdown for normal mode -->
    <ng-container *ngIf="state.activeButton !== 'favorite' && state.services.length > 0">
      <app-simple-service-dropdown
        #serviceDropdown
        [options]="state.services"
        [selectedOption]="state.selectedService"
        (selected)="onServiceDropdownSelected($event)">
      </app-simple-service-dropdown>
    </ng-container>

    <!-- Service dropdown for favorites mode -->
    <ng-container *ngIf="state.activeButton === 'favorite' && state.favoriteServices.length > 0">
      <app-simple-service-dropdown
        #serviceDropdown
        [options]="state.favoriteServices"
        [selectedOption]="state.selectedService"
        (selected)="onServiceDropdownSelected($event)">
      </app-simple-service-dropdown>
    </ng-container>

    <!-- Empty states -->
    <div *ngIf="state.activeButton === 'favorite' && state.favoriteServices.length === 0" class="empty-state">
      <p>{{ 'no_favorite_services' | translate }}</p>
    </div>

    <div *ngIf="state.activeButton !== 'favorite' && state.services.length === 0" class="empty-state">
      <p>{{ 'no_services_available' | translate }}</p>
    </div>
  </div>

  <!-- Link -->
  <div class="form-section" *ngIf="shouldShowFormFields(state)">
    <h3 class="section-title">{{ 'link' | translate }}</h3>
    <input 
      [ngModel]="state.formData.link" 
      (ngModelChange)="updateFormData('link', $event)"
      class="form-input" 
      [placeholder]="state.selectedService?.sample_link || ''" />
  </div>

  <!-- Comments -->
  <div class="form-section" *ngIf="shouldShowFormFields(state) && state.selectedService?.type === 'Custom Comments'">
    <h3 class="section-title">{{ 'comments' | translate }}</h3>
    <textarea
      [ngModel]="state.formData.comments"
      (ngModelChange)="updateFormData('comments', $event); validateQuantityAndCalculateFee()"
      class="form-textarea"
      [class.error]="state.quantityError"
      placeholder="{{ 'enter_comments_one_per_line' | translate }}">
    </textarea>
    <p class="form-help">{{ 'each_line_is_one_comment' | translate }}</p>
    <p class="form-help">{{ 'current_comments_count' | translate }}: {{ getCommentsArray().length }}</p>
    <p *ngIf="state.quantityError" class="error-text">{{ state.quantityError }}</p>
  </div>

  <!-- Quantity -->
  <div class="form-section" *ngIf="shouldShowFormFields(state) && state.selectedService?.type !== 'Custom Comments'">
    <h3 class="section-title">{{ 'quantity' | translate }}</h3>
    <input
      type="number"
      [ngModel]="state.formData.quantity"
      (ngModelChange)="updateFormData('quantity', $event); validateQuantityAndCalculateFee()"
      class="form-input"
      [class.error]="state.quantityError"
      [min]="state.selectedService?.min || 10"
      [max]="state.selectedService?.max || 5000000" />
    <p class="form-help">Min: {{ state.selectedService?.min || 10 }} - Max: {{ state.selectedService?.max || 5000000 }}</p>
    <p *ngIf="state.quantityError" class="error-text">{{ state.quantityError }}</p>
  </div>

  <!-- Quantity display for Comment type services -->
  <div class="form-section" *ngIf="shouldShowFormFields(state) && state.selectedService?.type === 'Custom Comments'">
    <h3 class="section-title">{{ 'quantity' | translate }}</h3>
    <input
      type="number"
      [ngModel]="state.formData.quantity"
      class="form-input disabled"
      readonly
      disabled />
    <p class="form-help">{{ 'quantity_based_on_comments' | translate }}</p>
  </div>

  <!-- Voucher Code -->
  <div class="form-section" *ngIf="shouldShowFormFields(state)">
    <h3 class="section-title">{{ 'voucher_code' | translate }}</h3>
    <div class="voucher-input-group">
      <input
        [ngModel]="state.formData.voucher_code"
        (ngModelChange)="updateFormData('voucher_code', $event)"
        class="form-input"
        [class.error]="state.voucherError"
        [class.success]="state.voucherApplied"
        placeholder="{{ 'enter_voucher_code' | translate }}" />
      
      <button
        *ngIf="state.formData.voucher_code && !state.voucherApplied"
        (click)="validateVoucherCode()"
        class="voucher-btn apply"
        [disabled]="state.isValidatingVoucher">
        <span *ngIf="!state.isValidatingVoucher">{{ 'apply' | translate }}</span>
        <span *ngIf="state.isValidatingVoucher">...</span>
      </button>
      
      <button
        *ngIf="state.formData.voucher_code && state.voucherApplied"
        (click)="clearVoucherCode()"
        class="voucher-btn clear"
        title="{{ 'clear' | translate }}">
        <fa-icon [icon]='["fas", "times"]'></fa-icon>
      </button>
    </div>
    
    <p *ngIf="state.voucherError" class="error-text">{{ state.voucherError }}</p>
    <p *ngIf="state.voucherApplied" class="success-text">
      {{ 'voucher_applied' | translate }} (-{{ state.voucherDiscount }}%)
    </p>
  </div>

  <!-- Submit Button -->
  <button 
    *ngIf="shouldShowFormFields(state)"
    (click)="onSubmit()"
    [disabled]="state.isLoading || state.quantityError !== ''"
    class="submit-btn"
    [class.disabled]="state.quantityError !== ''">
    
    <span *ngIf="!state.isLoading">{{ 'send' | translate }}</span>
    <span *ngIf="state.isLoading">{{ 'processing' | translate }}...</span>

    <div class="price-display">
      <!-- Original price with strikethrough if there's a discount -->
      <span *ngIf="state.discountPercent > 0 || state.voucherApplied" class="original-price">
        {{state.originalPrice | currencyConvert}}
      </span>
      
      <!-- Discount badges -->
      <span *ngIf="state.discountPercent > 0" class="discount-badge service">
        -{{state.discountPercent}}%
      </span>
      
      <span *ngIf="state.voucherApplied" class="discount-badge voucher">
        -{{state.voucherDiscount}}%
      </span>
      
      <!-- Final price -->
      <span class="final-price">{{state.formData.fee | currencyConvert}}</span>
    </div>
  </button>

  <!-- Error message -->
  <div *ngIf="state.errorMessage" class="error-message">
    {{ state.errorMessage }}
  </div>

</div>
