import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { Observable } from 'rxjs';

// Services
import { HeaderLogicService, HeaderState } from '../../services/header-logic.service';

// Components
import { LangDropdownComponent } from '../../../common/lang-dropdown/lang-dropdown.component';
import { InterfaceSwitcherComponent } from '../../../common/interface-switcher/interface-switcher.component';
import { AvatarComponent } from '../../../common/avatar/avatar.component';
import { NotificationComponent } from '../../../common/notification/notification.component';
import { UserNotificationDropdownComponent } from '../../../common/user-notification-dropdown/user-notification-dropdown.component';

@Component({
  selector: 'app-simple-header',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    LangDropdownComponent,
    InterfaceSwitcherComponent,
    AvatarComponent,
    NotificationComponent,
    UserNotificationDropdownComponent
  ],
  templateUrl: './simple-header.component.html',
  styleUrls: ['./simple-header.component.css']
})
export class SimpleHeaderComponent implements OnInit, OnDestroy {
  // Header logic state
  headerState$: Observable<HeaderState>;

  constructor(private headerLogicService: HeaderLogicService) {
    this.headerState$ = this.headerLogicService.state$;
  }

  ngOnInit(): void {
    // HeaderLogicService handles all initialization
  }

  ngOnDestroy(): void {
    // HeaderLogicService is singleton, no cleanup needed
  }

  // Delegate methods to HeaderLogicService for template compatibility
  toggleSidebar(): void {
    this.headerLogicService.toggleSidebar();
  }

  navigateTo(link: string): void {
    this.headerLogicService.navigateTo(link);
  }

  toggleUserMenu(): void {
    this.headerLogicService.toggleUserMenu();
  }

  closeUserMenu(): void {
    this.headerLogicService.closeUserMenu();
  }
}
