import { Component, Input, Output, EventEmitter, OnChanges, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { IconDropdownComponent } from "../../../common/icon-dropdown/icon-dropdown.component";

@Component({
  selector: 'app-admin-services-header',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    FontAwesomeModule,
    TranslateModule,
    IconDropdownComponent
  ],
  templateUrl: './admin-services-header.component.html',
  styleUrls: ['./admin-services-header.component.css']
})
export class AdminServicesHeaderComponent implements OnChanges, OnDestroy {
  @Input() categoryOptions: any[] = [];
  @Input() selectedCategory: any = null;
  @Input() isCategoryMoveMode: boolean = false;
  @Input() searchTerm: string = '';

  @Output() addServiceClicked = new EventEmitter<void>();
  @Output() importServicesClicked = new EventEmitter<void>();
  @Output() toggleCategoryMoveModeClicked = new EventEmitter<void>();
  @Output() platformManagementClicked = new EventEmitter<void>();
  @Output() categorySelected = new EventEmitter<any>();
  @Output() newCategoryClicked = new EventEmitter<void>();
  @Output() searchChanged = new EventEmitter<string>();
  @Output() filterReset = new EventEmitter<void>();

  searchValue: string = '';
  private searchTimeout: any;

  ngOnChanges(): void {
    // Sync searchValue with searchTerm input
    this.searchValue = this.searchTerm;
  }

  ngOnDestroy(): void {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
  }

  onAddService(): void {
    this.addServiceClicked.emit();
  }

  onImportServices(): void {
    this.importServicesClicked.emit();
  }

  onToggleCategoryMoveMode(): void {
    this.toggleCategoryMoveModeClicked.emit();
  }

  onPlatformManagement(): void {
    this.platformManagementClicked.emit();
  }

  onCategorySelected(category: any): void {
    this.categorySelected.emit(category);
  }

  onNewCategory(): void {
    this.newCategoryClicked.emit();
  }

  onSearchInput(): void {
    // Clear previous timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    // Set new timeout for debouncing
    this.searchTimeout = setTimeout(() => {
      this.searchChanged.emit(this.searchValue);
    }, 300); // 300ms delay
  }

  onResetFilter(): void {
    this.searchValue = '';
    this.searchChanged.emit('');
    this.filterReset.emit();
  }
}
