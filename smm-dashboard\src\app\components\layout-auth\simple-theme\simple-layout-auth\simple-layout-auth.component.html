<!-- Simple Theme Layout Auth Component -->
<div class="min-h-screen bg-gray-50" *ngIf="layoutAuthState$ | async as layoutAuthState">
  <!-- Simple Header -->
  <header class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo -->
        <div class="flex-shrink-0">
          <a routerLink="/auth/login" class="flex items-center">
            <img [src]="logoUrl" alt="Logo" class="h-8 w-auto">
          </a>
        </div>

        <!-- Language Switcher -->
        <div class="relative" *ngIf="languages.length > 1">
          <button
            (click)="toggleLanguageDropdown()"
            class="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <i [class]="getFlagClass(currentLanguage)"></i>
            <span>{{ currentLanguage.toUpperCase() }}</span>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>

          <!-- Language Dropdown -->
          <div
            *ngIf="showLanguageDropdown"
            class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50"
          >
            <div class="py-1">
              <button
                *ngFor="let lang of languages"
                (click)="changeLanguage(lang.code)"
                class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                [class.bg-gray-100]="lang.code === currentLanguage"
              >
                <i [class]="lang.flag" class="mr-3"></i>
                {{ lang.name }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <main class="flex-1">
    <router-outlet></router-outlet>
  </main>

  <!-- Simple Footer -->
  <footer class="bg-white border-t border-gray-200">
    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
        <!-- Copyright -->
        <div class="text-sm text-gray-500">
          © {{ 'footer.copyright' | translate }}
        </div>

        <!-- Links -->
        <div class="flex space-x-6">
          <a href="#" class="text-sm text-gray-500 hover:text-gray-900">
            {{ 'footer.privacy_policy' | translate }}
          </a>
          <a href="#" class="text-sm text-gray-500 hover:text-gray-900">
            {{ 'footer.terms_of_service' | translate }}
          </a>
          <a href="#" class="text-sm text-gray-500 hover:text-gray-900">
            {{ 'footer.support' | translate }}
          </a>
        </div>

        <!-- Social Links -->
        <div class="flex space-x-4">
          <a href="#" class="text-gray-400 hover:text-gray-500">
            <span class="sr-only">Facebook</span>
            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M20 10C20 4.477 15.523 0 10 0S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z" clip-rule="evenodd"></path>
            </svg>
          </a>
          <a href="#" class="text-gray-400 hover:text-gray-500">
            <span class="sr-only">Twitter</span>
            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84"></path>
            </svg>
          </a>
        </div>
      </div>
    </div>
  </footer>
</div>
