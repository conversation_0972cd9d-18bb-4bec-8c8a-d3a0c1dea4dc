package tndung.vnfb.smm.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import tndung.vnfb.smm.anotation.TenantCheck;
import tndung.vnfb.smm.dto.ApiResponseEntity;
import tndung.vnfb.smm.dto.request.TenantLanguageSettingsReq;
import tndung.vnfb.smm.dto.request.TenantI18nContentReq;
import tndung.vnfb.smm.dto.request.CustomLanguageReq;

import tndung.vnfb.smm.dto.response.TenantLanguageSettingsRes;
import tndung.vnfb.smm.dto.response.TenantDefaultLanguageRes;
import tndung.vnfb.smm.dto.response.TenantI18nContentRes;
import tndung.vnfb.smm.dto.response.CustomLanguageRes;
import tndung.vnfb.smm.dto.response.LanguageOptionRes;
import tndung.vnfb.smm.service.TenantSettingsService;
import tndung.vnfb.smm.service.TenantI18nContentService;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/v1/tenant-settings")
@RequiredArgsConstructor
public class TenantSettingsController {

    private final TenantSettingsService tenantSettingsService;
    private final TenantI18nContentService tenantI18nContentService;

    @GetMapping("/language")
    public ApiResponseEntity<TenantLanguageSettingsRes> getLanguageSettings() {
        TenantLanguageSettingsRes settings = tenantSettingsService.getLanguageSettings();
        return ApiResponseEntity.success(settings);
    }

    @PutMapping("/language")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<TenantLanguageSettingsRes> updateLanguageSettings(
            @Valid @RequestBody TenantLanguageSettingsReq request) {
        TenantLanguageSettingsRes settings = tenantSettingsService.updateLanguageSettings(request);
        return ApiResponseEntity.success(settings);
    }

    @GetMapping("/language/default")
    public ApiResponseEntity<TenantDefaultLanguageRes> getTenantDefaultLanguage() {
        TenantDefaultLanguageRes defaultLanguage = tenantSettingsService.getTenantDefaultLanguage();
        return ApiResponseEntity.success(defaultLanguage);
    }

    @GetMapping("/language/available")
    public ApiResponseEntity<List<String>> getTenantAvailableLanguages() {
        List<String> availableLanguages = tenantSettingsService.getTenantAvailableLanguages();
        return ApiResponseEntity.success(availableLanguages);
    }

    // I18n Content Management Endpoints

    @GetMapping("/i18n/{languageCode}")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<TenantI18nContentRes> getI18nContent(@PathVariable String languageCode) {
        TenantI18nContentRes content = tenantI18nContentService.getI18nContent(languageCode);
        return ApiResponseEntity.success(content);
    }

    @PutMapping("/i18n/{languageCode}")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<TenantI18nContentRes> updateI18nContent(
            @PathVariable String languageCode,
            @Valid @RequestBody TenantI18nContentReq request) {
        TenantI18nContentRes content = tenantI18nContentService.updateI18nContent(languageCode, request);
        return ApiResponseEntity.success(content);
    }

    @DeleteMapping("/i18n/{languageCode}")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<String> deleteI18nContent(@PathVariable String languageCode) {
        tenantI18nContentService.deleteI18nContent(languageCode);
        return ApiResponseEntity.success();
    }

    @GetMapping("/i18n/languages")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<List<String>> getAvailableI18nLanguages() {
        List<String> languages = tenantI18nContentService.getAvailableLanguageCodes();
        return ApiResponseEntity.success(languages);
    }

    @GetMapping("/i18n/template")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<Map<String, Object>> getDefaultTemplate() {
        Map<String, Object> template = tenantI18nContentService.getDefaultTemplate();
        return ApiResponseEntity.success(template);
    }

    @GetMapping("/i18n/template/{languageCode}")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<Map<String, Object>> getTemplateForLanguage(@PathVariable String languageCode) {
        Map<String, Object> template = tenantI18nContentService.getTemplateForLanguage(languageCode);
        return ApiResponseEntity.success(template);
    }

    @PostMapping("/i18n/{languageCode}/import")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<TenantI18nContentRes> importTranslations(
            @PathVariable String languageCode,
            @RequestBody Map<String, Object> translations) {
        TenantI18nContentRes content = tenantI18nContentService.importTranslations(languageCode, translations);
        return ApiResponseEntity.success(content);
    }

    // Public endpoint for dashboard to get translations
    @GetMapping("/i18n/public/{languageCode}")
    public ApiResponseEntity<Map<String, Object>> getDashboardTranslations(@PathVariable String languageCode) {
        Map<String, Object> translations = tenantI18nContentService.getDashboardTranslations(languageCode);
        return ApiResponseEntity.success(translations);
    }

    // Language Management Endpoints

    @GetMapping("/languages/predefined")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<List<LanguageOptionRes>> getPredefinedLanguages() {
        List<LanguageOptionRes> languages = tenantSettingsService.getPredefinedLanguages();
        return ApiResponseEntity.success(languages);
    }

    @GetMapping("/languages/custom")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<List<CustomLanguageRes>> getCustomLanguages() {
        List<CustomLanguageRes> languages = tenantSettingsService.getCustomLanguages();
        return ApiResponseEntity.success(languages);
    }

    @PostMapping("/languages/custom")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<CustomLanguageRes> createCustomLanguage(@Valid @RequestBody CustomLanguageReq request) {
        CustomLanguageRes language = tenantSettingsService.createCustomLanguage(request);
        return ApiResponseEntity.success(language);
    }

    @PutMapping("/languages/custom/{languageCode}")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<CustomLanguageRes> updateCustomLanguage(
            @PathVariable String languageCode,
            @Valid @RequestBody CustomLanguageReq request) {
        CustomLanguageRes language = tenantSettingsService.updateCustomLanguage(languageCode, request);
        return ApiResponseEntity.success(language);
    }

    @DeleteMapping("/languages/custom/{languageCode}")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<String> deleteCustomLanguage(@PathVariable String languageCode) {
        tenantSettingsService.deleteCustomLanguage(languageCode);
        return ApiResponseEntity.success();
    }

    @GetMapping("/languages/all")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<List<LanguageOptionRes>> getAllAvailableLanguages() {
        List<LanguageOptionRes> languages = tenantSettingsService.getAllAvailableLanguages();
        return ApiResponseEntity.success(languages);
    }
}
