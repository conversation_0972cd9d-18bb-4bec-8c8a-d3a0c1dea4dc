import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { Observable } from 'rxjs';

// Services
import { HomeLogicService, HomeState } from '../../services/home-logic.service';

// Components
import { IconsModule } from '../../../../icons/icons.module';
import { SimpleNewOrderComponent } from '../simple-new-order/simple-new-order.component';
import { SimpleServiceInfoComponent } from '../simple-service-info/simple-service-info.component';
import { SimpleFavoriteServicesComponent } from '../simple-favorite-services/simple-favorite-services.component';
import { SimpleNotificationHomeComponent } from '../simple-notification-home/simple-notification-home.component';
import { SocialIconComponent } from '../../../common/social-icon/social-icon.component';

// Models
import { SuperPlatformRes } from '../../../../model/response/super-platform.model';

@Component({
  selector: 'app-simple-home',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    IconsModule,
    SimpleNewOrderComponent,
    SimpleServiceInfoComponent,
    SimpleFavoriteServicesComponent,
    SimpleNotificationHomeComponent,
    SocialIconComponent
  ],
  templateUrl: './simple-home.component.html',
  styleUrl: './simple-home.component.css'
})
export class SimpleHomeComponent implements OnInit, OnDestroy {
  // Expose state as observable for template
  homeState$: Observable<HomeState>;

  constructor(private homeLogicService: HomeLogicService) {
    this.homeState$ = this.homeLogicService.state$;
  }

  ngOnInit(): void {
    // HomeLogicService handles all initialization
  }

  ngOnDestroy(): void {
    // HomeLogicService is singleton, no cleanup needed
  }

  // Delegate methods to HomeLogicService for template compatibility
  onPlatformSelected(platform: SuperPlatformRes): void {
    this.homeLogicService.onPlatformSelected(platform);
  }

  // TrackBy function for platform list performance
  trackByPlatformId(_index: number, platform: SuperPlatformRes): number {
    return platform.id;
  }
}
