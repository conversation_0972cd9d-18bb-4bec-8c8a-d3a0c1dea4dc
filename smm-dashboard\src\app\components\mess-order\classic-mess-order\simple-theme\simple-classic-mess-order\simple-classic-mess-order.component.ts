import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { Observable } from 'rxjs';

// Services
import { MessOrderFormLogicService, MessOrderFormState } from '../../../services/mess-order-form-logic.service';

@Component({
  selector: 'app-simple-classic-mess-order',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule
  ],
  templateUrl: './simple-classic-mess-order.component.html',
  styleUrl: './simple-classic-mess-order.component.css'
})
export class SimpleClassicMessOrderComponent implements OnInit, OnDestroy {
  // Mess order form logic state
  messOrderFormState$: Observable<MessOrderFormState>;

  // Local state for classic mode
  content = '';

  constructor(private messOrderFormLogicService: MessOrderFormLogicService) {
    this.messOrderFormState$ = this.messOrderFormLogicService.state$;
  }

  ngOnInit(): void {
    // MessOrderFormLogicService handles all initialization
  }

  ngOnDestroy(): void {
    // MessOrderFormLogicService is singleton, no cleanup needed
  }

  // Delegate methods to MessOrderFormLogicService for template compatibility
  createOrder(): void {
    this.messOrderFormLogicService.createClassicOrder(this.content);
  }

  // Getter methods for template compatibility
  get isProcessing() {
    return this.messOrderFormLogicService.isProcessing;
  }

  get successCount() {
    return this.messOrderFormLogicService.successCount;
  }

  get failedCount() {
    return this.messOrderFormLogicService.failedCount;
  }

  get showResults() {
    return this.messOrderFormLogicService.showResults;
  }

  get orderResults() {
    return this.messOrderFormLogicService.orderResults;
  }

  // Helper methods for template
  getLineCount(): number {
    return this.content.split('\n').filter(line => line.trim() !== '').length;
  }

  getCharacterCount(): number {
    return this.content.length;
  }

  getTotalCount(): number {
    return this.successCount + this.failedCount;
  }
}
