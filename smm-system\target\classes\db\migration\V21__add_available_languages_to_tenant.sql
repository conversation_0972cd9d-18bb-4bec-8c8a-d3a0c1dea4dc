-- Add available_languages column to tenant table
DO $$
BEGIN
    -- Check if the column exists
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'tenant' 
        AND column_name = 'available_languages'
    ) THEN
        -- Add the column
        ALTER TABLE tenant ADD COLUMN available_languages VARCHAR(50) DEFAULT 'vi,en';
        
        -- Update existing records to have both languages available by default
        UPDATE tenant SET available_languages = 'vi,en' WHERE available_languages IS NULL;
        
        -- Add comment
        COMMENT ON COLUMN tenant.available_languages IS 'Comma-separated list of available languages for tenant (e.g., vi,en)';
    END IF;
END $$;
