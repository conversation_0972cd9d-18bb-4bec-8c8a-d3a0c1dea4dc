import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output, OnInit, OnDestroy, ElementRef, HostListener, OnChanges, SimpleChanges } from '@angular/core';
import { IconsModule } from '../../../../icons/icons.module';
import { SimpleServiceLabelComponent } from "../../service-label/simple/simple-service-label.component";
import { SuperGeneralSvRes } from '../../../../model/response/super-general-sv.model';
import { DropdownService } from '../../../../core/services/dropdown.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-simple-service-dropdown',
  standalone: true,
  imports: [CommonModule, IconsModule, SimpleServiceLabelComponent],
  templateUrl: './simple-service-dropdown.component.html',
  styleUrls: ['./simple-service-dropdown.component.css']
})
export class SimpleServiceDropdownComponent implements OnInit, OnDestroy, OnChanges {
  private justOpened = false;
  private dropdownId: string = `simple-service-dropdown-${Math.random().toString(36).substring(2, 9)}`;
  private closeAllSubscription: Subscription;

  @Input() lite: boolean = false;
  @Input() options: SuperGeneralSvRes[] = [];
  @Input() placeholder: string = 'Chọn một tùy chọn';
  @Output() selected = new EventEmitter<SuperGeneralSvRes>();
  @Input() customClassButton: string = '';
  @Input() customClassDropdown: string = '';

  isOpen = false;
  private _selectedOption: SuperGeneralSvRes = {} as SuperGeneralSvRes;

  get selectedOption(): SuperGeneralSvRes {
    return this._selectedOption;
  }

  @Input()
  set selectedOption(value: SuperGeneralSvRes | undefined) {
    if (value && value.id && (!this._selectedOption || this._selectedOption.id !== value.id)) {
      console.log('Simple service dropdown selectedOption changed externally:', value.id, value.name);
      this._selectedOption = value;
    }
  }

  constructor(
    private elementRef: ElementRef,
    private dropdownService: DropdownService
  ) {
    this.closeAllSubscription = this.dropdownService.closeAllDropdowns.subscribe(() => {
      if (this.isOpen) {
        this.isOpen = false;
        document.body.classList.remove('simple-service-dropdown-open');
      }
    });
  }

  ngOnInit(): void {
    if (this.options.length > 0 && (!this._selectedOption || !this._selectedOption.id)) {
      this._selectedOption = this.options[0];
      this.selected.emit(this.options[0]);
      console.log('Simple service dropdown initialized with first option:', this._selectedOption.id, this._selectedOption.name);
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['options'] && changes['options'].currentValue && changes['options'].currentValue.length > 0) {
      const newOptions = changes['options'].currentValue;

      if (this._selectedOption && this._selectedOption.id) {
        const existingOption = newOptions.find((o: SuperGeneralSvRes) => o.id === this._selectedOption.id);
        if (existingOption) {
          console.log('Simple service dropdown options changed, keeping current selection:', this._selectedOption.id);
          return;
        }
      }

      this._selectedOption = newOptions[0];
      this.selected.emit(this._selectedOption);
      console.log('Simple service dropdown options changed, selected first option:', this._selectedOption.id, this._selectedOption.name);
    }
  }

  ngOnDestroy() {
    document.body.classList.remove('simple-service-dropdown-open');
    if (this.closeAllSubscription) {
      this.closeAllSubscription.unsubscribe();
    }
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    if (this.justOpened) {
      this.justOpened = false;
      return;
    }

    const target = event.target as HTMLElement;
    const isInsideDropdown = this.elementRef.nativeElement.contains(target);

    if (!isInsideDropdown) {
      this.isOpen = false;
      document.body.classList.remove('simple-service-dropdown-open');
    }
  }



  toggleDropdown(event?: MouseEvent) {
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }

    this.isOpen = !this.isOpen;

    if (this.isOpen) {
      this.dropdownService.openDropdown(this.dropdownId, this);
      this.justOpened = true;
    } else {
      this.dropdownService.closeDropdown();
    }
  }

  selectOption(option: SuperGeneralSvRes, event?: MouseEvent) {
    if (event) {
      event.stopPropagation();
    }

    if (!option || !option.id) {
      console.warn('Attempted to select an invalid service option:', option);
      return;
    }

    console.log('Simple service dropdown selecting option:', option.id, option.name);

    this._selectedOption = option;
    this.selected.emit(option);

    this.isOpen = false;
    document.body.classList.remove('simple-service-dropdown-open');
    this.dropdownService.closeDropdown();
  }


}
