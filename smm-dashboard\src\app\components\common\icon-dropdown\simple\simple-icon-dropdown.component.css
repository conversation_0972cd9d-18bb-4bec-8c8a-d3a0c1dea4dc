/* Simple Icon Dropdown Styles */

.simple-icon-dropdown-container {
  @apply relative inline-block text-left w-full;
}

.simple-dropdown-button {
  @apply w-full bg-white border border-gray-300 rounded-lg shadow-sm;
  @apply hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
  @apply transition-all duration-200 ease-in-out;
  padding: 12px 16px;
  min-height: 48px;
}

.simple-dropdown-button:hover {
  @apply shadow-md;
}

.button-content {
  @apply flex items-center justify-between w-full;
}

.icon-section {
  @apply flex-shrink-0 mr-3;
}

.selected-icon {
  @apply w-6 h-6;
}

.label-section {
  @apply flex-1 text-left;
}

.selected-label {
  @apply text-gray-900 font-medium;
  font-size: 0.875rem;
}

.arrow-section {
  @apply flex-shrink-0 ml-3;
}

.dropdown-arrow {
  @apply w-4 h-4 text-gray-500 transition-transform duration-200;
}

.dropdown-arrow.rotated {
  @apply transform rotate-180;
}

/* Dropdown Menu */
.simple-dropdown-menu {
  @apply absolute left-0 right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-[99999];
  @apply max-h-60 overflow-y-auto;
  animation: slideDown 0.15s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.options-list {
  @apply list-none p-0 m-0;
}

.option-item {
  @apply flex items-center cursor-pointer transition-colors duration-150;
  @apply hover:bg-gray-50 border-b border-gray-100;
  padding: 12px 16px;
}

.option-item:last-child {
  border-bottom: none;
}

.option-item.selected {
  @apply bg-blue-50;
}

.option-item:hover {
  @apply bg-gray-100;
}

.option-item.selected:hover {
  @apply bg-blue-100;
}

.option-icon {
  @apply flex-shrink-0 mr-3;
}

.option-icon .icon {
  @apply w-5 h-5;
}

.option-label {
  @apply flex-1 text-left;
}

.option-label span {
  @apply text-gray-900;
  font-size: 0.875rem;
}

.selected-indicator {
  @apply flex-shrink-0 ml-3;
}

.check-icon {
  @apply w-4 h-4 text-blue-600;
}

/* Focus states for accessibility */
.option-item:focus {
  @apply outline-none bg-blue-50;
}

.option-item:focus-visible {
  @apply outline-2 outline-blue-500 outline-offset-2;
}

/* Loading/Disabled states */
.simple-dropdown-button[disabled] {
  @apply opacity-50 cursor-not-allowed;
}

.simple-dropdown-button[disabled]:hover {
  @apply border-gray-300 shadow-sm;
}

/* Error state */
.simple-icon-dropdown-container.error .simple-dropdown-button {
  @apply border-red-300 focus:ring-red-500 focus:border-red-500;
}

/* Success state */
.simple-icon-dropdown-container.success .simple-dropdown-button {
  @apply border-green-300 focus:ring-green-500 focus:border-green-500;
}

/* Responsive Design */
@media (max-width: 640px) {
  .simple-dropdown-button {
    padding: 10px 12px;
    min-height: 44px;
  }
  
  .selected-icon {
    @apply w-5 h-5;
  }
  
  .option-icon .icon {
    @apply w-4 h-4;
  }
  
  .option-item {
    padding: 10px 12px;
  }
  
  .simple-dropdown-menu {
    @apply max-h-48;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .simple-dropdown-button {
    @apply bg-gray-800 border-gray-600 text-gray-100;
  }
  
  .simple-dropdown-button:hover {
    @apply border-gray-500;
  }
  
  .selected-label {
    @apply text-gray-100;
  }
  
  .dropdown-arrow {
    @apply text-gray-400;
  }
  
  .simple-dropdown-menu {
    @apply bg-gray-800 border-gray-600;
  }
  
  .option-item {
    border-bottom-color: #4b5563;
  }
  
  .option-item:hover {
    @apply bg-gray-700;
  }
  
  .option-item.selected {
    @apply bg-gray-700;
  }
  
  .option-item.selected:hover {
    @apply bg-gray-600;
  }
  
  .option-label span {
    @apply text-gray-100;
  }
}

/* Custom scrollbar for dropdown menu */
.simple-dropdown-menu::-webkit-scrollbar {
  width: 6px;
}

.simple-dropdown-menu::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded;
}

.simple-dropdown-menu::-webkit-scrollbar-thumb {
  @apply bg-gray-400 rounded;
}

.simple-dropdown-menu::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500;
}

/* Prevent body scroll when dropdown is open */
body.icon-dropdown-open {
  overflow: hidden;
}
