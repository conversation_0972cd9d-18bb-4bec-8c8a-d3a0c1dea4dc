import { Injectable } from '@angular/core';
import { BehaviorSubject, Subscription } from 'rxjs';
import { CurrencyService } from '../../../core/services/currency.service';
import { UserService } from '../../../core/services/user.service';
import { UserRes } from '../../../model/response/user-res.model';
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';

export interface ServiceLabelState {
  user: UserRes | undefined;
  userCustomDiscount: number;
}

@Injectable({
  providedIn: 'root'
})
export class ServiceLabelLogicService {
  private subscriptions: Subscription[] = [];

  // State management
  private _state$ = new BehaviorSubject<ServiceLabelState>({
    user: undefined,
    userCustomDiscount: 0
  });

  public readonly state$ = this._state$.asObservable();

  constructor(
    private currencyService: CurrencyService,
    private userService: UserService
  ) {
    this.initialize();
  }

  // Get current state
  get currentState(): ServiceLabelState {
    return this._state$.value;
  }

  // Get user
  get user(): UserRes | undefined {
    return this.currentState.user;
  }

  // Get user custom discount
  get userCustomDiscount(): number {
    return this.currentState.userCustomDiscount;
  }

  // Initialize service
  private initialize(): void {
    this.loadUserData();
  }

  // Update state helper
  private updateState(updates: Partial<ServiceLabelState>): void {
    const currentState = this._state$.value;
    this._state$.next({ ...currentState, ...updates });
  }

  // Load user data
  private loadUserData(): void {
    const userSubscription = this.userService.user$.subscribe(user => {
      const userCustomDiscount = user && (user as any).custom_discount ? (user as any).custom_discount : 0;
      
      this.updateState({
        user,
        userCustomDiscount
      });

      // If user is not loaded yet, trigger a fetch
      if (!user) {
        this.userService.get$.next();
      }
    });

    this.subscriptions.push(userSubscription);
  }

  // Format price with currency conversion
  formatPrice(price: number): string {
    return this.currencyService.formatPrice(price);
  }

  // Check if service has special prices
  hasSpecialPrices(service: SuperGeneralSvRes): boolean {
    return !!service && !!service.special_prices && service.special_prices.length > 0;
  }

  // Get special price count
  getSpecialPriceCount(service: SuperGeneralSvRes): number {
    return service && service.special_prices ? service.special_prices.length : 0;
  }

  // Check if service has any discount (special price or custom discount)
  hasDiscount(service: SuperGeneralSvRes): boolean {
    // Check for special prices first (priority)
    if (this.hasSpecialPrices(service)) {
      return true;
    }

    // Check for user's custom discount
    if (this.userCustomDiscount > 0) {
      return true;
    }

    return false;
  }

  // Get the original price before discount
  getOriginalPrice(service: SuperGeneralSvRes): number {
    return service ? service.original_price || service.price : 0;
  }

  // Get the discount percentage
  getDiscountPercent(service: SuperGeneralSvRes): number {
    // Check for special prices with PERCENT type (priority)
    if (this.hasSpecialPrices(service) && service.special_prices[0].discount_type === 'PERCENT') {
      return service.special_prices[0].discount_value;
    }

    if (this.hasSpecialPrices(service) && service.special_prices[0].discount_type === 'FIXED') {
      return 0;
    }

    // Return user's custom discount if available
    if (this.userCustomDiscount > 0) {
      return this.userCustomDiscount;
    }

    return 0;
  }

  // Calculate discount price
  calculateDiscount(service: SuperGeneralSvRes): number {
    let result: number;

    // Check for special prices with FIXED type (priority)
    if (this.hasSpecialPrices(service) && service.special_prices[0].discount_type === 'FIXED') {
      result = service.special_prices[0].discount_value;
    } else {
      const discountPercent = this.getDiscountPercent(service);
      if (discountPercent > 0) {
        result = service.price * (1 - discountPercent / 100);
      } else {
        result = service.price;
      }
    }

    // Format to 6 decimal places and remove trailing zeros
    return parseFloat(result.toFixed(6));
  }

  // Clean up subscriptions
  destroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.subscriptions = [];
  }
}
