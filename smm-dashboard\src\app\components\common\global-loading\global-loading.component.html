<!-- Default Theme -->
<ng-container *ngIf="(themeService.currentLayoutTheme$ | async) === LayoutTheme.DEFAULT">
  <app-loading
    *ngIf="(globalLoadingState$ | async)?.isLoading"
    [fullScreen]="true"
    [message]="(globalLoadingState$ | async)?.loadingMessage || ''"
    size="lg">
  </app-loading>
</ng-container>

<!-- Simple Theme -->
<ng-container *ngIf="(themeService.currentLayoutTheme$ | async) === LayoutTheme.SIMPLE">
  <app-simple-global-loading
    [globalLoadingState]="globalLoadingState$ | async">
  </app-simple-global-loading>
</ng-container>
