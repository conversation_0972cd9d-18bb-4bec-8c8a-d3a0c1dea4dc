import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject, Observable } from 'rxjs';

export interface LayoutAuthState {
  currentLanguage: string;
  availableLanguages: { code: string; name: string; flag: string }[];
}

@Injectable({
  providedIn: 'root'
})
export class LayoutAuthLogicService {
  private stateSubject = new BehaviorSubject<LayoutAuthState>({
    currentLanguage: 'vi',
    availableLanguages: [
      { code: 'vi', name: 'Tiếng Việt', flag: '🇻🇳' },
      { code: 'en', name: 'English', flag: '🇺🇸' }
    ]
  });

  public state$ = this.stateSubject.asObservable();

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    this.initializeLanguage();
  }

  private initializeLanguage(): void {
    if (isPlatformBrowser(this.platformId)) {
      const savedLanguage = localStorage.getItem('language') || 'vi';
      this.updateState({ currentLanguage: savedLanguage });
    }
  }

  public changeLanguage(languageCode: string): void {
    this.updateState({ currentLanguage: languageCode });
    
    if (isPlatformBrowser(this.platformId)) {
      localStorage.setItem('language', languageCode);
    }
    
    // Reload page to apply language changes
    if (isPlatformBrowser(this.platformId)) {
      window.location.reload();
    }
  }

  private updateState(partialState: Partial<LayoutAuthState>): void {
    const currentState = this.stateSubject.value;
    this.stateSubject.next({
      ...currentState,
      ...partialState
    });
  }

  public getCurrentLanguage(): string {
    return this.stateSubject.value.currentLanguage;
  }

  public getAvailableLanguages(): { code: string; name: string; flag: string }[] {
    return this.stateSubject.value.availableLanguages;
  }
}
