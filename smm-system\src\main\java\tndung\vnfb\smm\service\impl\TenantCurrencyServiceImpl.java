package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tndung.vnfb.smm.dto.request.TenantCurrencyReq;
import tndung.vnfb.smm.dto.response.TenantCurrencyRes;
import tndung.vnfb.smm.entity.Currency;
import tndung.vnfb.smm.entity.Tenant;
import tndung.vnfb.smm.entity.TenantCurrencySettings;
import tndung.vnfb.smm.repository.tenant.TenantCurrencySettingsRepository;
import tndung.vnfb.smm.service.CurrencyService;
import tndung.vnfb.smm.service.TenantCurrencyService;
import tndung.vnfb.smm.service.TenantService;
import tndung.vnfb.smm.config.TenantContext;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class TenantCurrencyServiceImpl implements TenantCurrencyService {

    private final TenantService tenantService;
    private final CurrencyService currencyService;
    private final TenantCurrencySettingsRepository tenantCurrencySettingsRepository;

    @Override
    public TenantCurrencyRes getTenantCurrencies() {
        String tenantId = TenantContext.getCurrentTenant();
        Tenant tenant = tenantService.findByTenantId(tenantId)
                .orElseThrow(() -> new RuntimeException("Tenant not found"));

        TenantCurrencyRes response = new TenantCurrencyRes();

        // Get available currencies for this tenant
        List<String> availableCurrencies = Arrays.asList(
            tenant.getAvailableCurrencies().split(",")
        );
        response.setAvailableCurrencies(availableCurrencies);

        // Get all currencies from system (excluding base currency for editing)
        List<Currency> allCurrencies = currencyService.getAllCurrencies();
        response.setAllCurrencies(allCurrencies);

        // Get currency settings for this tenant - for each available currency
        List<TenantCurrencySettings> currencySettings = availableCurrencies.stream()
            .map(currencyCode -> {
                Optional<TenantCurrencySettings> existing = tenantCurrencySettingsRepository.findByCurrencyCode(currencyCode);
                if (existing.isPresent()) {
                    log.info("Found existing settings for currency: {}", currencyCode);
                    return existing.get();
                }
                // Return default if not found (but don't save it yet)
                log.info("No settings found for currency: {}, returning default", currencyCode);
                return TenantCurrencySettings.builder()
                    .currencyCode(currencyCode)
                    .syncEnabled(true)
                    .paymentSyncEnabled(false)
                    .build();
            })
            .collect(Collectors.toList());

        log.info("Returning {} currency settings for tenant: {}", currencySettings.size(), tenantId);

        response.setCurrencySettings(currencySettings);
        response.setLastCurrencySync(tenant.getLastCurrencySync());

        return response;
    }

    @Override
    @Transactional
    public TenantCurrencyRes updateTenantCurrencies(TenantCurrencyReq req) {
        String tenantId = TenantContext.getCurrentTenant();
        Tenant tenant = tenantService.findByTenantId(tenantId)
                .orElseThrow(() -> new RuntimeException("Tenant not found"));

        // Validate that USD is always included
        if (!req.getAvailableCurrencies().contains("USD")) {
            req.getAvailableCurrencies().add(0, "USD");
        }

        // Validate that all currencies exist
        for (String currencyCode : req.getAvailableCurrencies()) {
            currencyService.getCurrencyByCode(currencyCode);
        }

        // Update tenant currencies
        String currenciesString = String.join(",", req.getAvailableCurrencies());
        tenant.setAvailableCurrencies(currenciesString);

        // Update individual currency sync settings
        if (req.getCurrencySyncSettings() != null || req.getPaymentSyncSettings() != null) {
            for (String currencyCode : req.getAvailableCurrencies()) {
                Boolean syncEnabled = req.getCurrencySyncSettings() != null ?
                    req.getCurrencySyncSettings().get(currencyCode) : null;
                Boolean paymentSyncEnabled = req.getPaymentSyncSettings() != null ?
                    req.getPaymentSyncSettings().get(currencyCode) : null;

                if (syncEnabled != null || paymentSyncEnabled != null) {
                    // Get current settings to preserve existing values
                    TenantCurrencySettings currentSettings = tenantCurrencySettingsRepository
                        .findByCurrencyCode(currencyCode)
                        .orElse(TenantCurrencySettings.builder()
                            .currencyCode(currencyCode)
                            .syncEnabled(true)
                            .paymentSyncEnabled(false)
                            .build());

                    updateCurrencySyncSettings(currencyCode,
                        syncEnabled != null ? syncEnabled : currentSettings.getSyncEnabled(),
                        paymentSyncEnabled != null ? paymentSyncEnabled : currentSettings.getPaymentSyncEnabled());
                }
            }
        }

        // Update custom rates if provided (for manual updates when sync is disabled)
        if (req.getCustomRates() != null) {
            for (Map.Entry<String, BigDecimal> entry : req.getCustomRates().entrySet()) {
                TenantCurrencySettings settings = tenantCurrencySettingsRepository
                        .findByCurrencyCode(entry.getKey())
                        .orElse(TenantCurrencySettings.builder()
                                .currencyCode(entry.getKey())
                                .build());

                settings.setCustomRate(entry.getValue());
                tenantCurrencySettingsRepository.save(settings);
            }
        }

        tenantService.save(tenant);

        return getTenantCurrencies();
    }

    @Override
    public List<Currency> getAvailableCurrenciesForCurrentTenant() {
        if(!tenantService.isMainTenantSite()) {
            TenantContext.setCurrentTenant(TenantContext.getSiteTenant());
        }
        String tenantId = TenantContext.getCurrentTenant();
        Tenant tenant = tenantService.findByTenantId(tenantId)
                .orElseThrow(() -> new RuntimeException("Tenant not found"));

        List<String> availableCurrencyCodes = Arrays.asList(
            tenant.getAvailableCurrencies().split(",")
        );

        return availableCurrencyCodes.stream()
                .map(currencyCode -> {
                    Currency currency = currencyService.getCurrencyByCode(currencyCode);

                    // Get effective rate (custom rate if sync disabled, otherwise standard rate)
                    BigDecimal effectiveRate = getEffectiveRate(tenantId, currencyCode);

                    // Create a new Currency object with the effective rate
                    return Currency.builder()
                            .code(currency.getCode())
                            .name(currency.getName())
                            .symbol(currency.getSymbol())
                            .exchangeRate(effectiveRate)
                            .baseCurrency(currency.isBaseCurrency())
                            .build();
                })
                .collect(Collectors.toList());
    }

    @Override
    public void triggerCurrencySync() {
        String tenantId = TenantContext.getCurrentTenant();
        Tenant tenant = tenantService.findByTenantId(tenantId)
                .orElseThrow(() -> new RuntimeException("Tenant not found"));

        // Get currencies that have sync enabled for this tenant (tenant filter applied automatically)
        List<TenantCurrencySettings> settings = tenantCurrencySettingsRepository.findAll();
        List<TenantCurrencySettings> syncEnabledSettings = settings.stream()
                .filter(s -> Boolean.TRUE.equals(s.getSyncEnabled()))
                .toList();

        if (syncEnabledSettings.isEmpty()) {
            throw new RuntimeException("No currencies have sync enabled for this tenant");
        }

        // Copy rates from Currency table to custom_rate for sync-enabled currencies
        // This uses the rates that were already updated by the background sync job
        for (TenantCurrencySettings setting : syncEnabledSettings) {
            Currency currency = currencyService.getCurrencyByCode(setting.getCurrencyCode());
            setting.setCustomRate(currency.getExchangeRate());
            tenantCurrencySettingsRepository.save(setting);
        }

        tenant.setLastCurrencySync(ZonedDateTime.now());
        tenantService.save(tenant);
    }

    @Override
    @Transactional
    public void updateCurrencySyncSettings(String currencyCode, boolean syncEnabled, boolean paymentSyncEnabled) {
        String tenantId = TenantContext.getCurrentTenant();
        log.info("Updating currency sync settings for tenant: {}, currency: {}, syncEnabled: {}, paymentSyncEnabled: {}",
                tenantId, currencyCode, syncEnabled, paymentSyncEnabled);

        TenantCurrencySettings settings = tenantCurrencySettingsRepository
                .findByCurrencyCode(currencyCode)
                .orElse(TenantCurrencySettings.builder()
                        .currencyCode(currencyCode)
                        .build());

        log.info("Found existing settings: {}", settings.getId() != null ? "Yes (ID: " + settings.getId() + ")" : "No (creating new)");

        settings.setSyncEnabled(syncEnabled);
        settings.setPaymentSyncEnabled(paymentSyncEnabled);

        TenantCurrencySettings savedSettings = tenantCurrencySettingsRepository.save(settings);
        tenantCurrencySettingsRepository.flush(); // Ensure data is persisted immediately
        log.info("Saved settings with ID: {}, tenantId: {}", savedSettings.getId(), savedSettings.getTenantId());
    }

    @Override
    public TenantCurrencySettings getTenantCurrencySettings(String tenantId, String currencyCode) {
        // Note: tenantId parameter is kept for interface compatibility but not used
        // since tenant filtering is handled automatically by TenantAwareRepository
        return tenantCurrencySettingsRepository
                .findByCurrencyCode(currencyCode)
                .orElse(TenantCurrencySettings.builder()
                        .currencyCode(currencyCode)
                        .syncEnabled(true)
                        .paymentSyncEnabled(false)
                        .build());
    }

    @Override
    public BigDecimal getEffectiveRate(String tenantId, String currencyCode) {
        TenantCurrencySettings settings = getTenantCurrencySettings(tenantId, currencyCode);

        // If sync is enabled or no custom rate is set, use the rate from Currency table
        if (Boolean.TRUE.equals(settings.getSyncEnabled()) || settings.getCustomRate() == null) {
            Currency currency = currencyService.getCurrencyByCode(currencyCode);
            return currency.getExchangeRate();
        }

        // If sync is disabled and custom rate is set, use custom rate
        return settings.getCustomRate();
    }
}
