-- Create user_tenant table to store the relationship between users and tenants
CREATE TABLE IF NOT EXISTS user_tenant (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    tenant_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),

    -- Add foreign key constraint to g_user table
    CONSTRAINT fk_user_tenant_user FOREIGN KEY (user_id) REFERENCES g_user(id) ON DELETE CASCADE,

    -- Add unique constraint to prevent duplicate user-tenant relationships
    CONSTRAINT uk_user_tenant UNIQUE (user_id, tenant_id)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_user_tenant_user_id ON user_tenant(user_id);
CREATE INDEX IF NOT EXISTS idx_user_tenant_tenant_id ON user_tenant(tenant_id);

-- Insert default relationships for existing users with the main tenant
-- First, get the tenant_id from the existing tenant table for the main domain
DO $$
DECLARE
    main_tenant_id VARCHAR;
BEGIN
    -- Try to get the tenant_id for autovnfb.dev
    SELECT tenant_id INTO main_tenant_id FROM tenant WHERE domain = 'autovnfb.dev' LIMIT 1;

    -- If not found, use a default UUID
    IF main_tenant_id IS NULL THEN
        main_tenant_id := '0e22c37d-bfb5-4276-bd30-355fcdb39c9e';
    END IF;

    -- Insert relationships for all users
    INSERT INTO user_tenant (user_id, tenant_id, created_at)
    SELECT id, main_tenant_id, NOW()
    FROM g_user
    WHERE NOT EXISTS (
        SELECT 1 FROM user_tenant
        WHERE user_tenant.user_id = g_user.id
        AND user_tenant.tenant_id = main_tenant_id
    );
END $$;

-- Add comments to the table and columns for better documentation
COMMENT ON TABLE user_tenant IS 'Stores the relationship between users and tenants for multi-tenant support';
COMMENT ON COLUMN user_tenant.id IS 'Primary key';
COMMENT ON COLUMN user_tenant.user_id IS 'Foreign key to g_user table';
COMMENT ON COLUMN user_tenant.tenant_id IS 'Tenant ID from tenant table';
COMMENT ON COLUMN user_tenant.created_at IS 'Timestamp when the relationship was created';
