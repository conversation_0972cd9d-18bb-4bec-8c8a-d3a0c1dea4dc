import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { FavoritesService } from '../../../core/services/favorites.service';
import { ServiceSelectionService } from '../../../core/services/service-selection.service';
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';

@Component({
  template: '', // Will be overridden by child components
})
export abstract class BaseFavoriteServicesComponent implements OnInit, OnDestroy {
  // Selected service from the service selection service
  selectedService: SuperGeneralSvRes | undefined;

  // Track if the service is in favorites
  isFavorite: boolean = false;

  // Track loading state
  isLoading: boolean = false;

  // Subscriptions to clean up on destroy
  private serviceSubscription: Subscription | undefined;
  private favoritesSubscription: Subscription | undefined;
  private loadingSubscription: Subscription | undefined;

  constructor(
    protected favoritesService: FavoritesService,
    protected serviceSelectionService: ServiceSelectionService
  ) {}

  ngOnInit(): void {
    // Subscribe to the selected service
    this.serviceSubscription = this.serviceSelectionService.serviceSelected$.subscribe(service => {
      this.selectedService = service;

      // When the service changes, check if it's in favorites
      if (service) {
        this.checkIfFavorite(service.id);
      }
    });

    // Subscribe to loading state
    this.loadingSubscription = this.favoritesService.loading$.subscribe(loading => {
      this.isLoading = loading;
    });

    // Load favorites
    this.loadFavorites();
  }

  ngOnDestroy(): void {
    // Clean up subscriptions to prevent memory leaks
    if (this.serviceSubscription) {
      this.serviceSubscription.unsubscribe();
    }
    if (this.favoritesSubscription) {
      this.favoritesSubscription.unsubscribe();
    }
    if (this.loadingSubscription) {
      this.loadingSubscription.unsubscribe();
    }
  }

  loadFavorites(): void {
    // Subscribe to favorites from the service
    this.favoritesSubscription = this.favoritesService.favorites$.subscribe(favorites => {
      // If we have a selected service, check if it's in favorites
      if (this.selectedService) {
        this.checkIfFavorite(this.selectedService.id);
      }
    });
  }

  checkIfFavorite(serviceId: number): void {
    // Use the isFavorite method from the FavoritesService
    this.isFavorite = this.favoritesService.isFavorite(serviceId);
  }

  onToggleFavorite(isFavorite: boolean): void {
    if (!this.selectedService) {
      console.warn('No service selected to toggle favorite');
      return;
    }

    if (isFavorite) {
      // Add to favorites
      this.favoritesService.addFavorite(this.selectedService.id).subscribe({
        next: () => {
          console.log('Service added to favorites successfully');
          this.isFavorite = true;
        },
        error: (error) => {
          console.error('Error adding service to favorites:', error);
          // Revert the toggle state on error
          this.isFavorite = false;
        }
      });
    } else {
      // Remove from favorites
      this.favoritesService.removeFavorite(this.selectedService.id).subscribe({
        next: () => {
          console.log('Service removed from favorites successfully');
          this.isFavorite = false;
        },
        error: (error) => {
          console.error('Error removing service from favorites:', error);
          // Revert the toggle state on error
          this.isFavorite = true;
        }
      });
    }
  }
}
