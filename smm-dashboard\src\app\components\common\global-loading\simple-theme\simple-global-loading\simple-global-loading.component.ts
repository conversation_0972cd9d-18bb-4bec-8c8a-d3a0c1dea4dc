import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GlobalLoadingState } from '../../services/global-loading-logic.service';

@Component({
  selector: 'app-simple-global-loading',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './simple-global-loading.component.html',
  styleUrl: './simple-global-loading.component.css'
})
export class SimpleGlobalLoadingComponent {
  @Input() globalLoadingState: GlobalLoadingState | null = null;
}
