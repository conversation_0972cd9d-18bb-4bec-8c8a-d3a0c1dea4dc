<!-- Simple Profile Theme - Clean & Minimal Design -->
<div class="simple-profile-container" *ngIf="profileState$ | async as profileState">

  <!-- Profile Header -->
  <div class="profile-header">
    <div class="header-content">
      <div class="avatar-section">
        <div class="avatar-wrapper">
          <img [src]="getAvatarPath()" alt="Profile" class="avatar-image">
          <div class="avatar-overlay">
            <i class="fas fa-camera"></i>
          </div>
        </div>
      </div>
      <div class="user-info">
        <h1 class="user-name">{{ profileState.user?.user_name || 'User' }}</h1>
        <p class="user-email">{{ profileState.user?.email || '<EMAIL>' }}</p>
        <div class="status-indicator">
          <span class="status-dot"></span>
          <span class="status-text">{{ 'profile.online' | translate }}</span>
        </div>
      </div>
      <div class="balance-info">
        <div class="balance-amount">{{ profileState.formattedBalance }}</div>
        <div class="balance-label">{{ 'profile.balance' | translate }}</div>
      </div>
    </div>
  </div>

  <!-- Tab Navigation -->
  <div class="tabs-container">
    <div class="tab-list">
      <button
        class="tab-button"
        [class.active]="profileState.activeTab === 'account'"
        (click)="setActiveTab('account')">
        <i class="fas fa-user"></i>
        <span>{{ 'profile.account' | translate }}</span>
      </button>
      <button
        class="tab-button"
        [class.active]="profileState.activeTab === 'security'"
        (click)="setActiveTab('security')">
        <i class="fas fa-shield-alt"></i>
        <span>{{ 'profile.security' | translate }}</span>
      </button>
      <button
        class="tab-button"
        [class.active]="profileState.activeTab === 'settings'"
        (click)="setActiveTab('settings')">
        <i class="fas fa-cog"></i>
        <span>{{ 'profile.settings' | translate }}</span>
      </button>
      <button
        class="tab-button"
        [class.active]="profileState.activeTab === 'history'"
        (click)="setActiveTab('history')">
        <i class="fas fa-history"></i>
        <span>{{ 'profile.login_history_tab' | translate }}</span>
      </button>
    </div>
  </div>

  <!-- Tab Content -->
  <div class="tab-content">

    <!-- Account Tab -->
    <div *ngIf="profileState.activeTab === 'account'" class="tab-panel">
      <div class="panel-header">
        <h2 class="panel-title">{{ 'profile.account_information' | translate }}</h2>
        <p class="panel-description">{{ 'profile.update_account_info' | translate }}</p>
      </div>

      <form [formGroup]="profileState.profileForm" (ngSubmit)="onProfileSubmit()" class="form-wrapper">
        <div class="form-field">
          <label class="field-label">{{ 'profile.email' | translate }}</label>
          <input
            type="email"
            formControlName="email"
            class="field-input"
            [placeholder]="'profile.email_placeholder' | translate">
        </div>

        <div class="form-field">
          <label class="field-label">{{ 'profile.phone' | translate }}</label>
          <input
            type="tel"
            formControlName="phone"
            class="field-input"
            [placeholder]="'profile.phone_placeholder' | translate">
        </div>

        <button
          type="submit"
          class="submit-button"
          [disabled]="profileState.isLoading">
          <i class="fas fa-save" *ngIf="!profileState.isLoading"></i>
          <i class="fas fa-spinner fa-spin" *ngIf="profileState.isLoading"></i>
          {{ 'profile.save_changes' | translate }}
        </button>
      </form>
    </div>

    <!-- Security Tab -->
    <div *ngIf="profileState.activeTab === 'security'" class="tab-panel">
      <div class="panel-header">
        <h2 class="panel-title">{{ 'profile.security_settings' | translate }}</h2>
        <p class="panel-description">{{ 'profile.manage_security' | translate }}</p>
      </div>

      <!-- Password Change Form -->
      <form [formGroup]="profileState.passwordForm" (ngSubmit)="onPasswordSubmit()" class="form-wrapper">
        <div class="form-field">
          <label class="field-label">{{ 'profile.current_password' | translate }}</label>
          <div class="password-field">
            <input
              [type]="profileState.showCurrentPassword ? 'text' : 'password'"
              formControlName="currentPassword"
              class="field-input"
              [placeholder]="'profile.current_password_placeholder' | translate">
            <button
              type="button"
              class="password-toggle"
              (click)="togglePasswordVisibility('current')">
              <i [class]="profileState.showCurrentPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
            </button>
          </div>
        </div>

        <div class="form-field">
          <label class="field-label">{{ 'profile.new_password' | translate }}</label>
          <div class="password-field">
            <input
              [type]="profileState.showNewPassword ? 'text' : 'password'"
              formControlName="newPassword"
              class="field-input"
              [placeholder]="'profile.new_password_placeholder' | translate">
            <button
              type="button"
              class="password-toggle"
              (click)="togglePasswordVisibility('new')">
              <i [class]="profileState.showNewPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
            </button>
          </div>
        </div>

        <div class="form-field">
          <label class="field-label">{{ 'profile.confirm_password' | translate }}</label>
          <div class="password-field">
            <input
              [type]="profileState.showConfirmPassword ? 'text' : 'password'"
              formControlName="confirmPassword"
              class="field-input"
              [placeholder]="'profile.confirm_password_placeholder' | translate">
            <button
              type="button"
              class="password-toggle"
              (click)="togglePasswordVisibility('confirm')">
              <i [class]="profileState.showConfirmPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
            </button>
          </div>
        </div>

        <button
          type="submit"
          class="submit-button"
          [disabled]="profileState.isPasswordLoading">
          <i class="fas fa-key" *ngIf="!profileState.isPasswordLoading"></i>
          <i class="fas fa-spinner fa-spin" *ngIf="profileState.isPasswordLoading"></i>
          {{ 'profile.change_password' | translate }}
        </button>
      </form>

      <!-- 2FA Section -->
      <div class="security-options">
        <div class="security-option">
          <div class="option-info">
            <h3 class="option-title">{{ 'profile.two_factor_auth' | translate }}</h3>
            <p class="option-description">{{ 'profile.two_factor_description' | translate }}</p>
          </div>
          <button
            class="toggle-button"
            [class.enabled]="profileState.is2FAEnabled"
            (click)="toggle2FA()">
            <span>{{ profileState.is2FAEnabled ? ('profile.enabled' | translate) : ('profile.disabled' | translate) }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Settings Tab -->
    <div *ngIf="profileState.activeTab === 'settings'" class="tab-panel">
      <div class="panel-header">
        <h2 class="panel-title">{{ 'profile.preferences' | translate }}</h2>
        <p class="panel-description">{{ 'profile.customize_experience' | translate }}</p>
      </div>

      <div class="settings-list">
        <div class="setting-item">
          <label class="setting-label">{{ 'profile.preferred_currency' | translate }}</label>
          <app-lite-dropdown
            [options]="profileState.currencyOptions"
            [selectedOption]="profileState.selectedCurrency"
            (selected)="onCurrencyChange($event)"
            class="setting-dropdown">
          </app-lite-dropdown>
        </div>

        <div class="setting-item">
          <label class="setting-label">{{ 'profile.language' | translate }}</label>
          <app-lite-dropdown
            [options]="languageOptions"
            [selectedOption]="'English'"
            (selected)="onLanguageChange($event)"
            class="setting-dropdown">
          </app-lite-dropdown>
        </div>
      </div>
    </div>

    <!-- History Tab -->
    <div *ngIf="profileState.activeTab === 'history'" class="tab-panel">
      <div class="panel-header">
        <h2 class="panel-title">{{ 'profile.login_history' | translate }}</h2>
        <p class="panel-description">{{ 'profile.recent_activity' | translate }}</p>
      </div>

      <div class="history-content" *ngIf="!profileState.isLoadingHistory">
        <div *ngIf="profileState.loginHistory.length === 0" class="empty-state">
          <i class="fas fa-history"></i>
          <p>{{ 'profile.no_login_history' | translate }}</p>
        </div>

        <div *ngFor="let item of profileState.loginHistory" class="history-item">
          <div class="history-details">
            <div class="history-time">{{ item.created_at | date:'medium' }}</div>
            <div class="history-info">{{ item.ip || 'N/A' }} - {{ item.user_agent || 'N/A' }}</div>
          </div>
          <div class="history-status">
            <span class="status-badge success">{{ 'profile.successful' | translate }}</span>
          </div>
        </div>
      </div>

      <div *ngIf="profileState.isLoadingHistory" class="loading-state">
        <i class="fas fa-spinner fa-spin"></i>
        <p>{{ 'profile.loading_history' | translate }}</p>
      </div>
    </div>

  </div>
</div>
