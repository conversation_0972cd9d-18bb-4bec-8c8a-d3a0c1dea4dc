package tndung.vnfb.smm.dto.response;

import lombok.Data;
import tndung.vnfb.smm.entity.Currency;
import tndung.vnfb.smm.entity.TenantCurrencySettings;

import java.time.ZonedDateTime;
import java.util.List;

@Data
public class TenantCurrencyRes {
    private List<String> availableCurrencies;
    private List<Currency> allCurrencies;
    private List<TenantCurrencySettings> currencySettings;
    private ZonedDateTime lastCurrencySync;
}
