package tndung.vnfb.smm.dto.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomLanguageReq {

    @NotBlank(message = "Language code is required")
    @Pattern(regexp = "^[a-z0-9_-]+$", message = "Language code must contain only lowercase letters, numbers, underscores, and hyphens")
    @Size(min = 2, max = 20, message = "Language code must be between 2 and 20 characters")
    private String languageCode;

    @NotBlank(message = "Language name is required")
    @Size(min = 1, max = 100, message = "Language name must be between 1 and 100 characters")
    private String languageName;

    @Size(max = 20, message = "Flag class must not exceed 20 characters")
    private String flagClass;

    @Size(max = 500, message = "Description must not exceed 500 characters")
    private String description;

    private boolean isActive = true;
}
