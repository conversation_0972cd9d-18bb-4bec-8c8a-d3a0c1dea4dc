import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { SearchBoxBaseComponent } from '../../search-box-base.component';
import { SearchBoxLogicService } from '../../search-box.service';

@Component({
  selector: 'app-simple-search-box',
  standalone: true,
  imports: [FormsModule, CommonModule],
  templateUrl: './simple-search-box.component.html',
  styleUrl: './simple-search-box.component.css',
  providers: [SearchBoxLogicService]
})
export class SimpleSearchBoxComponent extends SearchBoxBaseComponent {
}
