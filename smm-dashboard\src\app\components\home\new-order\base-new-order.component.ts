import { <PERSON>mponent, <PERSON><PERSON>nit, <PERSON><PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { Subscription } from 'rxjs';

// Services
import { NewOrderLogicService, NewOrderState } from '../services/new-order-logic.service';
import { PlatformSelectionService } from '../../../core/services/platform-selection.service';
import { ServiceSelectionService } from '../../../core/services/service-selection.service';
import { FavoritesService } from '../../../core/services/favorites.service';
import { OrderService, CreateOrderReq } from '../../../core/services/order.service';
import { VoucherService } from '../../../core/services/voucher.service';
import { CategoriesService } from '../../../core/services/categories.service';

// Models
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';
import { SuperPlatformRes } from '../../../model/response/super-platform.model';
import { IconBaseModel } from '../../../model/base-model';
import { OrderRes } from '../../../model/response/order-res.model';


// Components
import { ServiceDropdownComponent } from '../../common/service-dropdown/service-dropdown.component';
import { IconName } from '@fortawesome/fontawesome-svg-core';

@Component({
  template: '', // Will be overridden by child components
})
export abstract class BaseNewOrderComponent implements OnInit, OnDestroy {
  @ViewChild(ServiceDropdownComponent) serviceDropdown!: ServiceDropdownComponent;
  @ViewChild('categoryDropdown', { static: false }) categoryDropdown: any;

  // State from service
  state$ = this.newOrderLogicService.state$;
  
  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(
    protected newOrderLogicService: NewOrderLogicService,
    protected platformSelectionService: PlatformSelectionService,
    protected serviceSelectionService: ServiceSelectionService,
    protected favoritesService: FavoritesService,
    protected orderService: OrderService,
    protected voucherService: VoucherService,
    protected categoriesService: CategoriesService
  ) {}

  ngOnInit(): void {
    this.initializeSubscriptions();
    this.loadInitialData();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.newOrderLogicService.destroy();
  }

  private initializeSubscriptions(): void {
    // Platform selection subscription
    this.subscriptions.push(
      this.platformSelectionService.platformSelected$.subscribe((platform: SuperPlatformRes) => {
        if (platform) {
          this.onPlatformChanged(platform);
        }
      })
    );

    // Service selection subscription
    this.subscriptions.push(
      this.serviceSelectionService.serviceSelected$.subscribe(service => {
        if (service) {
          this.onServiceChanged(service);
        }
      })
    );

    // Favorites subscription - favorites are service IDs, not service objects
    this.subscriptions.push(
      this.favoritesService.favorites$.subscribe(favoriteIds => {
        // We'll need to convert IDs to services later when we have the services loaded
        // For now, just store the IDs
        this.newOrderLogicService.updateState({
          favoriteServices: [] // Will be populated when services are loaded
        });
      })
    );
  }

  private loadInitialData(): void {
    // Load favorites
    this.favoritesService.loadFavorites();
    // Load platforms and categories
    this.loadPlatforms();
  }

  // Platform handling
  private onPlatformChanged(platform: SuperPlatformRes): void {
    console.log('BaseNewOrderComponent - Platform changed:', platform.name, 'Categories:', platform.categories?.length);

    // Convert platform categories to IconBaseModel format for dropdown
    const categories = platform.categories
      ?.filter(category => !category.hide)
      ?.map(category => ({
        id: category.id.toString(),
        label: category.name,
        icon: platform.icon as IconName,
        sort: category.sort
      }))
      ?.sort((a, b) => a.sort - b.sort) || [];

    this.newOrderLogicService.updateState({
      selectedPlatformId: platform.id,
      categories: categories,
      services: [],
      selectedCategory: undefined,
      selectedService: undefined
    });

    // Auto-select first category if available
    if (categories.length > 0) {
      console.log('Auto-selecting first category:', categories[0].label);
      this.onCategorySelected(categories[0]);
    }
  }

  // Service handling
  private onServiceChanged(service: SuperGeneralSvRes): void {
    this.newOrderLogicService.updateState({
      selectedService: service
    });
    this.validateQuantityAndCalculateFee();
  }

  // UI Event handlers
  setActiveButton(button: 'new_order' | 'favorite' | 'auto_subscription'): void {
    this.newOrderLogicService.updateState({ activeButton: button });
    
    if (button === 'favorite') {
      this.loadFavoriteServices();
    }
  }

  onCategorySelected(category: any): void {
    console.log('BaseNewOrderComponent - Category selected:', category);

    // Find the selected category in platforms data and get its services
    const currentState = this.newOrderLogicService.currentState;
    let services: SuperGeneralSvRes[] = [];

    for (const platform of currentState.platforms) {
      const foundCategory = platform.categories.find(c => c.id.toString() === category.id);
      if (foundCategory) {
        services = foundCategory.services || [];
        console.log('Services loaded for category:', category.label, 'Services count:', services.length);
        break;
      }
    }

    this.newOrderLogicService.updateState({
      selectedCategory: category,
      services: services,
      selectedService: undefined
    });
  }

  onServiceDropdownSelected(service: SuperGeneralSvRes): void {
    this.serviceSelectionService.selectService(service);
  }

  onServiceSelected(service: SuperGeneralSvRes): void {
    this.serviceSelectionService.selectService(service);
    this.newOrderLogicService.updateState({ showAutocomplete: false });
  }

  // Search functionality
  onSearchInput(event: any): void {
    const searchTerm = event.target.value;
    this.newOrderLogicService.updateState({ searchTerm });
    
    if (searchTerm.length >= 2) {
      this.performSearch(searchTerm);
    } else {
      this.newOrderLogicService.updateState({ 
        searchResults: [],
        showAutocomplete: false 
      });
    }
  }

  onSearchClick(): void {
    const currentState = this.newOrderLogicService.currentState;
    if (currentState.searchTerm.length >= 2) {
      this.newOrderLogicService.updateState({ showAutocomplete: true });
    }
  }

  handleSearchKeydown(event: KeyboardEvent): void {
    if (event.key === 'Escape') {
      this.newOrderLogicService.updateState({ showAutocomplete: false });
    }
  }

  onAutocompleteVisibilityChange(isVisible: boolean): void {
    this.newOrderLogicService.updateState({ showAutocomplete: isVisible });
  }

  private performSearch(searchTerm: string): void {
    const currentState = this.newOrderLogicService.currentState;
    const results = currentState.allServices.filter(service =>
      service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.id.toString().includes(searchTerm)
    );
    
    this.newOrderLogicService.updateState({
      searchResults: results,
      showAutocomplete: true
    });
  }

  // Form validation and calculation
  validateQuantityAndCalculateFee(): void {
    const currentState = this.newOrderLogicService.currentState;
    const { selectedService, formData } = currentState;
    
    if (!selectedService) return;

    let quantity = formData.quantity;
    let quantityError = '';

    // Handle comment type services
    if (selectedService.type === 'Custom Comments') {
      const comments = this.getCommentsArray();
      quantity = comments.length;
      
      if (quantity < (selectedService.min || 1)) {
        quantityError = `Minimum ${selectedService.min || 1} comments required`;
      } else if (quantity > (selectedService.max || 5000000)) {
        quantityError = `Maximum ${selectedService.max || 5000000} comments allowed`;
      }
    } else {
      // Regular quantity validation
      if (quantity && quantity < (selectedService.min || 10)) {
        quantityError = `Minimum quantity is ${selectedService.min || 10}`;
      } else if (quantity && quantity > (selectedService.max || 5000000)) {
        quantityError = `Maximum quantity is ${selectedService.max || 5000000}`;
      }
    }

    // Calculate fee
    let fee = 0;
    let originalPrice = 0;

    if (quantity && selectedService.price) {
      originalPrice = (quantity * selectedService.price) / 1000;
      fee = originalPrice;

      // Apply voucher discount
      if (currentState.voucherApplied && currentState.voucherDiscount > 0) {
        fee = fee * (1 - currentState.voucherDiscount / 100);
      }
    }

    this.newOrderLogicService.updateState({
      quantityError,
      originalPrice,
      discountPercent: 0,
      discountType: ''
    });

    this.newOrderLogicService.updateFormData({
      quantity,
      fee
    });
  }

  getCommentsArray(): string[] {
    const currentState = this.newOrderLogicService.currentState;
    return currentState.formData.comments
      .split('\n')
      .map(comment => comment.trim())
      .filter(comment => comment.length > 0);
  }

  // Voucher handling
  validateVoucherCode(): void {
    const currentState = this.newOrderLogicService.currentState;
    const voucherCode = currentState.formData.voucher_code;
    
    if (!voucherCode) return;

    this.newOrderLogicService.updateState({ isValidatingVoucher: true });

    this.voucherService.verifyVoucherCode(voucherCode).subscribe({
      next: (response) => {
        this.newOrderLogicService.updateState({
          voucherApplied: true,
          voucherDiscount: response.discount_value,
          voucherError: '',
          isValidatingVoucher: false
        });
        this.validateQuantityAndCalculateFee();
      },
      error: (error) => {
        this.newOrderLogicService.updateState({
          voucherApplied: false,
          voucherDiscount: 0,
          voucherError: error.message || 'Invalid voucher code',
          isValidatingVoucher: false
        });
      }
    });
  }

  clearVoucherCode(): void {
    this.newOrderLogicService.updateFormData({ voucher_code: '' });
    this.newOrderLogicService.updateState({
      voucherApplied: false,
      voucherDiscount: 0,
      voucherError: ''
    });
    this.validateQuantityAndCalculateFee();
  }

  // Order submission
  onSubmit(): void {
    const currentState = this.newOrderLogicService.currentState;
    const { selectedService, formData, quantityError } = currentState;
    
    if (!selectedService || quantityError || !formData.quantity) {
      return;
    }

    this.newOrderLogicService.updateState({ isLoading: true, errorMessage: '' });

    const orderRequest: CreateOrderReq = {
      service_id: selectedService.id,
      link: formData.link,
      quantity: formData.quantity,
      comments: selectedService.type === 'Custom Comments' ? this.getCommentsArray() : undefined,
      voucher_code: formData.voucher_code || undefined
    };

    this.orderService.createOrder(orderRequest).subscribe({
      next: (order: OrderRes) => {
        this.newOrderLogicService.updateState({
          createdOrder: order,
          isOrderSuccess: true,
          isLoading: false
        });
      },
      error: (error) => {
        this.newOrderLogicService.updateState({
          errorMessage: error.message || 'Failed to create order',
          isLoading: false
        });
      }
    });
  }

  resetOrderState(): void {
    this.newOrderLogicService.resetOrderState();
  }

  private loadFavoriteServices(): void {
    this.favoritesService.loadFavorites();
  }

  // Load platforms and initialize categories
  private loadPlatforms(): void {
    this.subscriptions.push(
      this.categoriesService.getPlatforms().subscribe({
        next: (platforms: SuperPlatformRes[]) => {
          console.log('BaseNewOrderComponent - Platforms loaded:', platforms);

          // Initialize allServices array for search functionality
          const allServices: SuperGeneralSvRes[] = [];
          const categories: IconBaseModel[] = [];

          // Store all categories and services for search functionality
          platforms.forEach(platform => {
            platform.categories.forEach(category => {
              if (!category.hide) {
                // Add category to the categories list
                categories.push({
                  id: category.id.toString(),
                  label: category.name,
                  icon: platform.icon as IconName,
                  sort: category.sort
                });

                // Add all services to allServices array for search functionality
                category.services.forEach(service => {
                  allServices.push(service);
                });
              }
            });
          });

          // Sort categories by sort field for consistent display
          categories.sort((a, b) => a.sort - b.sort);

          // Update state with loaded data
          this.newOrderLogicService.updateState({
            platforms: platforms,
            categories: categories,
            allServices: allServices
          });

          // Always set the first category as the selected category and load its services
          if (categories.length > 0) {
            const selectedCategory = categories[0];
            console.log('Setting default selected category:', selectedCategory);
            this.onCategorySelected(selectedCategory);
          } else {
            // Initialize with empty services if no categories are available
            this.newOrderLogicService.updateState({
              services: [],
              selectedService: undefined
            });
            console.log('No categories available');
          }

          console.log('Platforms loaded, all categories displayed');
          console.log('Total categories:', categories.length);
          console.log('Total services available for search:', allServices.length);
        },
        error: (error) => {
          console.error('Error loading platforms:', error);
        }
      })
    );
  }
}
