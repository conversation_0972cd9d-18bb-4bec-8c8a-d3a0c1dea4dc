import { Component, OnInit, inject } from '@angular/core';
import { ServicesLogicService } from './services.service';
import { SuperPlatformRes } from '../../model/response/super-platform.model';
import { IconBaseModel } from '../../model/base-model';
import { SuperCategoryRes } from '../../model/response/super-category.model';

// Extended interface for our dropdown options
interface ExtendedIconBaseModel extends IconBaseModel {
  platformId?: string;
}

// Extended interface for categories with platform info
interface ExtendedCategoryRes extends SuperCategoryRes {
  platformIcon?: string;
  isAllPlatforms?: boolean;
  isAllCategories?: boolean;
}

@Component({
  template: ''
})
export abstract class ServicesBaseComponent implements OnInit {
  protected servicesLogic = inject(ServicesLogicService);

  ngOnInit(): void {
    this.servicesLogic.loadPlatforms();
    this.servicesLogic.loadFavorites();
  }

  // Expose service properties
  get allPlatforms(): SuperPlatformRes[] { return this.servicesLogic.allPlatforms; }
  get platformOptions(): ExtendedIconBaseModel[] { return this.servicesLogic.platformOptions; }
  get selectedPlatform(): ExtendedIconBaseModel | undefined { return this.servicesLogic.selectedPlatform; }
  get categoryOptions(): ExtendedIconBaseModel[] { return this.servicesLogic.categoryOptions; }
  get selectedCategory(): ExtendedIconBaseModel | undefined { return this.servicesLogic.selectedCategory; }
  get currentCategory(): ExtendedCategoryRes | null { return this.servicesLogic.currentCategory; }
  get loading(): boolean { return this.servicesLogic.loading; }
  get favoriteServiceIds(): number[] { return this.servicesLogic.favoriteServiceIds; }
  get displayCategories(): ExtendedCategoryRes[] { return this.servicesLogic.displayCategories; }

  // Expose service methods
  loadFavorites(): void {
    this.servicesLogic.loadFavorites();
  }

  loadPlatforms(): void {
    this.servicesLogic.loadPlatforms();
  }

  onPlatformSelected(platform: IconBaseModel): void {
    this.servicesLogic.onPlatformSelected(platform);
  }

  onCategorySelected(category: ExtendedIconBaseModel): void {
    this.servicesLogic.onCategorySelected(category);
  }

  showPlatformCategoryInfo(category: ExtendedIconBaseModel | undefined, service: any): boolean {
    return this.servicesLogic.showPlatformCategoryInfo(category, service);
  }

  getPlatformName(service: any): string {
    return this.servicesLogic.getPlatformName(service);
  }

  getCategoryName(service: any): string {
    return this.servicesLogic.getCategoryName(service);
  }

  applyFilter(searchText: string): void {
    this.servicesLogic.applyFilter(searchText);
  }

  resetFilter(searchInput: HTMLInputElement): void {
    this.servicesLogic.resetFilter(searchInput);
  }

  isServiceLoading(serviceId: number): boolean {
    return this.servicesLogic.isServiceLoading(serviceId);
  }

  onToggleFavorite(serviceId: number | null): void {
    this.servicesLogic.onToggleFavorite(serviceId);
  }

  orderService(serviceId: number): void {
    this.servicesLogic.orderService(serviceId);
  }

  // TrackBy functions for better change detection
  trackByOptionId(_index: number, option: ExtendedIconBaseModel): string {
    return option.id;
  }
}
