package tndung.vnfb.smm.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import tndung.vnfb.smm.anotation.TenantCheck;
import tndung.vnfb.smm.config.TenantContext;
import tndung.vnfb.smm.dto.ApiResponseEntity;
import tndung.vnfb.smm.dto.response.FileUploadResponse;
import tndung.vnfb.smm.service.FileUploadService;

/**
 * Controller for handling file uploads
 */
@RestController
@RequestMapping("/v1/uploads")
@RequiredArgsConstructor
public class FileUploadController {
    private final FileUploadService fileUploadService;

    /**
     * Upload a logo file
     * @param file The logo file to upload
     * @return The uploaded file information
     */
    @PostMapping(value = "/logo", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @PreAuthorize("hasAnyRole('ROLE_PANEL',  'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<FileUploadResponse> uploadLogo(
            @RequestParam("file") MultipartFile file) {
        String tenantId = TenantContext.getWildcardTenant();
        return ApiResponseEntity.success(fileUploadService.uploadLogo(file, tenantId));
    }

    /**
     * Upload a favicon file
     * @param file The favicon file to upload
     * @return The uploaded file information
     */
    @PostMapping(value = "/favicon", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @PreAuthorize("hasAnyRole('ROLE_PANEL',  'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<FileUploadResponse> uploadFavicon(
            @RequestParam("file") MultipartFile file) {
        String tenantId = TenantContext.getWildcardTenant();
        return ApiResponseEntity.success(fileUploadService.uploadFavicon(file, tenantId));
    }

    /**
     * Upload an icon file for platforms
     * @param file The icon file to upload (PNG format)
     * @return The uploaded file information
     */
    @PostMapping(value = "/icon", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @PreAuthorize("hasAnyRole('ROLE_PANEL',  'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<FileUploadResponse> uploadIcon(
            @RequestParam("file") MultipartFile file) {
        String tenantId = TenantContext.getWildcardTenant();
        return ApiResponseEntity.success(fileUploadService.uploadIcon(file, tenantId));
    }
}
