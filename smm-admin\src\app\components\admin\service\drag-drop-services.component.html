<div class="layout-container py-6 px-6">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold">{{ 'admin.services.drag_drop_title' | translate }}</h1>
    <button (click)="navigateToServices()" class="bg-transparent border border-[#0095f6] text-[#0095f6] font-medium text-sm px-6 py-3 rounded-lg cursor-pointer transition-colors duration-300 hover:bg-[#0095f6]/10 active:bg-[#0095f6]/20">
      {{ 'admin.services.back_to_services' | translate }}
    </button>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="loading" class="flex justify-center items-center py-8">
    <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-cyan-500"></div>
  </div>

  <!-- Categories and services grid -->
  <div *ngIf="!loading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <!-- Category column for each category -->
    <div *ngFor="let category of categories" class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <!-- Category header -->
      <div class="bg-gray-50 p-4 border-b border-gray-200">
        <div class="flex items-center">
          <app-social-icon *ngIf="category.platformIcon" [icon]="category.platformIcon" class="mr-2"></app-social-icon>
          <div>
            <h2 class="font-semibold text-gray-800">{{ category.name }}</h2>
            <p class="text-xs text-gray-500">{{ category.platformName }}</p>
          </div>
        </div>
      </div>

      <!-- Services list -->
      <div class="p-2">
        <!-- Empty state -->
        <div *ngIf="!servicesByCategory[category.id] || servicesByCategory[category.id].length === 0"
             class="p-4 text-center text-gray-500">
          {{ 'admin.services.no_services' | translate }}
        </div>

        <!-- Services -->
        <div *ngFor="let service of servicesByCategory[category.id.toString()]; let i = index"
             class="service-row p-3 mb-2 rounded-lg border border-gray-100 bg-white hover:bg-gray-50 cursor-grab"
             [attr.data-service-id]="service.id"
             [attr.data-category-id]="category.id"
             [attr.data-index]="i"
             draggable="true"
             (dragstart)="onDragStart($event, service, i, category.id.toString())"
             (dragover)="onDragOver($event, category.id.toString(), i)"
             (dragenter)="onDragEnter($event)"
             (dragleave)="onDragLeave($event)"
             (drop)="onDrop($event, category.id.toString(), i)"
             (dragend)="onDragEnd($event)">

          <!-- Service content -->
          <div class="flex items-center">
            <app-service-label [service]="service"></app-service-label>
          </div>

          <!-- Service details -->
          <div class="mt-2 text-sm text-gray-600">
            <div class="flex justify-between">
              <span>{{ 'admin.services.price' | translate }}: {{ service.price }} VNĐ</span>
              <span>{{ 'admin.services.min' | translate }}: {{ service.min }}</span>
              <span>{{ 'admin.services.max' | translate }}: {{ service.max }}</span>
            </div>
          </div>
        </div>

        <!-- Drop zone at the end of the list -->
        <div class="empty-drop-zone p-3 mb-2 rounded-lg border border-dashed border-gray-300 bg-gray-50 min-h-[60px] flex items-center justify-center"
             (dragover)="onDragOver($event, category.id.toString(), (servicesByCategory[category.id.toString()] || []).length)"
             (drop)="onDrop($event, category.id.toString(), (servicesByCategory[category.id.toString()] || []).length)">
          <span class="text-gray-400">{{ 'admin.services.drop_here' | translate }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- No categories message -->
  <div *ngIf="!loading && categories.length === 0" class="bg-white p-8 rounded-lg shadow text-center">
    <p class="text-gray-500">{{ 'admin.services.no_categories' | translate }}</p>
  </div>
</div>
