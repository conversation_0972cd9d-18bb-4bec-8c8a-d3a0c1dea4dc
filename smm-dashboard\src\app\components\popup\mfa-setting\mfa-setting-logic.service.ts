import { Injectable } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { BehaviorSubject } from 'rxjs';
import { MfaService, MfaVerifyRequest } from '../../../core/services/mfa.service';
import { ToastService } from '../../../core/services/toast.service';
import { NotifyType } from '../../../constant/notify-type';

export interface MfaSettingState {
  isLoading: boolean;
  qrCodeImage: string;
  secretKey: string;
  showPassword: boolean;
  mfaForm: FormGroup;
}

@Injectable()
export class MfaSettingLogicService {
  private stateSubject = new BehaviorSubject<MfaSettingState>({
    isLoading: false,
    qrCodeImage: '',
    secretKey: '',
    showPassword: false,
    mfaForm: this.createForm()
  });

  public state$ = this.stateSubject.asObservable();

  constructor(
    private fb: FormBuilder,
    private mfaService: MfaService,
    private toastService: ToastService
  ) {}

  private createForm(): FormGroup {
    return this.fb.group({
      code: ['', [Validators.required, Validators.minLength(6), Validators.maxLength(6), Validators.pattern(/^\d{6}$/)]],
      password: ['', [Validators.required]]
    });
  }

  private updateState(updates: Partial<MfaSettingState>): void {
    const currentState = this.stateSubject.value;
    this.stateSubject.next({ ...currentState, ...updates });
  }

  get currentState(): MfaSettingState {
    return this.stateSubject.value;
  }

  /**
   * Initialize MFA setting by generating QR code
   */
  initialize(): void {
    this.generateMfaCode();
  }

  /**
   * Generate MFA QR code and secret key
   */
  generateMfaCode(): void {
    this.updateState({ isLoading: true });

    this.mfaService.generateMfa()
      .subscribe({
        next: (response) => {
          if (response) {
            this.updateState({
              qrCodeImage: response.image,
              secretKey: response.secret_key,
              isLoading: false
            });
          } else {
            this.updateState({ isLoading: false });
          }
        },
        error: (error: any) => {
          console.error('Error generating MFA code:', error);
          this.toastService.showToast(
            error.message || 'Failed to generate MFA code. Please try again.', 
            NotifyType.ERROR
          );
          this.updateState({ isLoading: false });
        }
      });
  }

  /**
   * Verify MFA code and enable 2FA
   */
  verifyMfa(onSuccess: () => void): void {
    const form = this.currentState.mfaForm;
    
    if (form.invalid) {
      // Mark all form controls as touched to show validation errors
      Object.keys(form.controls).forEach(key => {
        const control = form.get(key);
        if (control) {
          control.markAsTouched();
        }
      });
      return;
    }

    this.updateState({ isLoading: true });

    const verifyData: MfaVerifyRequest = {
      password: form.value.password,
      secret_key: this.currentState.secretKey,
      code: form.value.code
    };

    this.mfaService.verifyMfa(verifyData)
      .subscribe({
        next: () => {
          this.toastService.showToast(
            'Two-factor authentication enabled successfully!', 
            NotifyType.SUCCESS
          );
          this.updateState({ isLoading: false });
          onSuccess();
        },
        error: (error: any) => {
          console.error('Error verifying MFA code:', error);
          this.toastService.showToast(
            error.message || 'Failed to verify MFA code. Please check your code and try again.', 
            NotifyType.ERROR
          );
          this.updateState({ isLoading: false });
        }
      });
  }

  /**
   * Toggle password visibility
   */
  togglePasswordVisibility(): void {
    this.updateState({ showPassword: !this.currentState.showPassword });
  }

  /**
   * Reset the form and state
   */
  reset(): void {
    this.updateState({
      isLoading: false,
      qrCodeImage: '',
      secretKey: '',
      showPassword: false,
      mfaForm: this.createForm()
    });
  }
}
