package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.dto.request.NotificationReq;
import tndung.vnfb.smm.dto.response.NotificationRes;
import tndung.vnfb.smm.entity.Notification;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class NotificationMapperImpl implements NotificationMapper {

    @Override
    public Notification toEntity(NotificationReq req) {
        if ( req == null ) {
            return null;
        }

        Notification notification = new Notification();

        notification.setOfferEndDate( NotificationMapper.localToOffset( req.getOfferEndDate() ) );
        notification.setAutoDismiss( req.getAutoDismiss() );
        notification.setContent( req.getContent() );
        notification.setDismissHours( req.getDismissHours() );
        notification.setOfferEndingType( req.getOfferEndingType() );
        notification.setShow( req.getShow() );
        notification.setTitle( req.getTitle() );
        notification.setType( req.getType() );

        return notification;
    }

    @Override
    public NotificationRes toDTO(Notification notification) {
        if ( notification == null ) {
            return null;
        }

        NotificationRes notificationRes = new NotificationRes();

        notificationRes.setAutoDismiss( notification.getAutoDismiss() );
        notificationRes.setContent( notification.getContent() );
        notificationRes.setDismissHours( notification.getDismissHours() );
        notificationRes.setId( notification.getId() );
        notificationRes.setOfferEndDate( notification.getOfferEndDate() );
        notificationRes.setOfferEndingType( notification.getOfferEndingType() );
        notificationRes.setShow( notification.getShow() );
        notificationRes.setTitle( notification.getTitle() );
        notificationRes.setType( notification.getType() );

        return notificationRes;
    }

    @Override
    public List<NotificationRes> toDTO(List<Notification> notifications) {
        if ( notifications == null ) {
            return null;
        }

        List<NotificationRes> list = new ArrayList<NotificationRes>( notifications.size() );
        for ( Notification notification : notifications ) {
            list.add( toDTO( notification ) );
        }

        return list;
    }

    @Override
    public void updateEntityFromDTO(NotificationReq req, Notification notification) {
        if ( req == null ) {
            return;
        }

        notification.setOfferEndDate( NotificationMapper.localToOffset( req.getOfferEndDate() ) );
        notification.setAutoDismiss( req.getAutoDismiss() );
        notification.setContent( req.getContent() );
        notification.setDismissHours( req.getDismissHours() );
        notification.setOfferEndingType( req.getOfferEndingType() );
        notification.setShow( req.getShow() );
        notification.setTitle( req.getTitle() );
        notification.setType( req.getType() );
    }
}
