<!-- Simple Lang Dropdown Theme - Modern Clean Design -->
<div class="simple-lang-dropdown" *ngIf="langDropdownState$ | async as langDropdownState" #dropdownContainer>
  
  <!-- Language Selector Button -->
  <div class="lang-selector" (click)="toggleDropdown()">
    <div class="selected-language">
      <div class="flag-container">
        <span [class]="langDropdownState.selectedFlag" class="flag-icon"></span>
      </div>
      <div class="language-info">
        <span class="language-name">
          {{ langDropdownState.selectedFlag.includes('vn') ? 'Tiếng Việt' : 
             langDropdownState.selectedFlag.includes('us') ? 'English' : 
             langDropdownState.selectedFlag.includes('cn') ? '中文' : 'Language' }}
        </span>
        <span class="language-code">
          {{ langDropdownState.selectedFlag.includes('vn') ? 'VI' : 
             langDropdownState.selectedFlag.includes('us') ? 'EN' : 
             langDropdownState.selectedFlag.includes('cn') ? 'CN' : 'XX' }}
        </span>
      </div>
    </div>
    <div class="dropdown-arrow" [class.rotated]="langDropdownState.isOpen">
      <svg viewBox="0 0 24 24" fill="currentColor">
        <path d="M7 10l5 5 5-5z"/>
      </svg>
    </div>
  </div>

  <!-- Language Options Dropdown -->
  <div class="lang-dropdown-menu" [class.show]="langDropdownState.isOpen">
    <div class="dropdown-content">
      <div class="dropdown-header">
        <span class="header-text">Select Language</span>
      </div>
      
      <div class="language-options">
        <div *ngFor="let lang of langDropdownState.languages" 
             class="language-option"
             [class.selected]="lang.flag === langDropdownState.selectedFlag"
             (click)="selectLanguage(lang.code)">
          <div class="option-flag">
            <span [class]="lang.flag" class="flag-icon"></span>
          </div>
          <div class="option-info">
            <span class="option-name">
              {{ lang.flag.includes('vn') ? 'Tiếng Việt' : 
                 lang.flag.includes('us') ? 'English' : 
                 lang.flag.includes('cn') ? '中文' : 'Language' }}
            </span>
            <span class="option-code">
              {{ lang.flag.includes('vn') ? 'VI' : 
                 lang.flag.includes('us') ? 'EN' : 
                 lang.flag.includes('cn') ? 'CN' : 'XX' }}
            </span>
          </div>
          <div class="option-check" *ngIf="lang.flag === langDropdownState.selectedFlag">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Overlay -->
<div class="overlay" *ngIf="(langDropdownState$ | async)?.isOpen" (click)="closeDropdown()"></div>
