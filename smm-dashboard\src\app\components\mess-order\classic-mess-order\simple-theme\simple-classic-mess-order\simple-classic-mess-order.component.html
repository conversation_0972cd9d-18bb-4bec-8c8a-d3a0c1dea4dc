<!-- Simple Theme for Classic Mess Order - Advanced Professional Design -->
<div class="simple-classic-mess-order-container" *ngIf="messOrderFormState$ | async as messOrderFormState">
  
  <!-- Header Section -->
  <div class="order-header">
    <div class="header-content">
      <div class="header-icon">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6zm4 18H6V4h7v5h5v11z"/>
        </svg>
      </div>
      <div class="header-text">
        <h2 class="header-title">{{ 'mass_order.classic_order' | translate }}</h2>
        <p class="header-description">{{ 'mass_order.classic_description' | translate }}</p>
      </div>
    </div>
  </div>

  <!-- Order Form -->
  <div class="order-form-container">
    
    <!-- Instructions Section -->
    <div class="instructions-section">
      <div class="instructions-header">
        <div class="instructions-icon">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z"/>
          </svg>
        </div>
        <h3 class="instructions-title">{{ 'simple_theme.mass_order.help_title' | translate }}</h3>
      </div>

      <div class="instructions-content">
        <div class="instruction-item">
          <div class="instruction-label">{{ 'mass_order.format' | translate }}:</div>
          <code class="instruction-code">service_id | link | quantity</code>
        </div>
        <div class="instruction-item">
          <div class="instruction-label">{{ 'mass_order.example' | translate }}:</div>
          <code class="instruction-code">1 | https://example.com | 1000</code>
        </div>
        <div class="instruction-item">
          <div class="instruction-label">{{ 'mass_order.note' | translate }}:</div>
          <span class="instruction-text">{{ 'mass_order.one_order_per_line' | translate }}</span>
        </div>
      </div>
    </div>

    <!-- Content Input Section -->
    <div class="content-section">
      <div class="content-header">
        <div class="content-icon">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
          </svg>
        </div>
        <h3 class="content-title">{{ 'mass_order.content' | translate }}</h3>
        <span class="content-subtitle">{{ 'mass_order.classic_description' | translate }}</span>
      </div>

      <div class="content-input">
        <textarea
          [(ngModel)]="content"
          class="content-textarea"
          placeholder="{{ 'mass_order.classic_placeholder' | translate }}"
          rows="12">
        </textarea>

        <!-- Content Stats -->
        <div class="content-stats">
          <div class="stat-item">
            <span class="stat-value">{{ getLineCount() }}</span>
            <span class="stat-label">{{ 'simple_theme.mass_order.lines' | translate }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ getCharacterCount() }}</span>
            <span class="stat-label">{{ 'simple_theme.mass_order.characters' | translate }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Section -->
    <div class="action-section">
      <div class="action-info">
        <div class="info-icon">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
        </div>
        <div class="info-text">
          <div class="info-title">{{ 'mass_order.create_order' | translate }}</div>
          <div class="info-description">{{ 'mass_order.classic_description' | translate }}</div>
        </div>
      </div>

      <button
        class="process-btn"
        (click)="createOrder()"
        [disabled]="isProcessing || !content.trim()">
        <div class="btn-content">
          <div class="btn-icon" *ngIf="!isProcessing">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M8 5v14l11-7z"/>
            </svg>
          </div>
          <div class="btn-spinner" *ngIf="isProcessing">
            <svg viewBox="0 0 24 24" fill="currentColor" class="animate-spin">
              <path d="M12 4V2A10 10 0 0 0 2 12h2a8 8 0 0 1 8-8z"/>
            </svg>
          </div>
          <span class="btn-text">
            {{ isProcessing ? ('mass_order.processing' | translate) : ('mass_order.create_order' | translate) }}
          </span>
        </div>
      </button>
    </div>

  </div>

  <!-- Results Section -->
  <div *ngIf="showResults" class="results-section">
    <div class="results-header">
      <div class="results-title-section">
        <div class="results-icon">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
          </svg>
        </div>
        <h3 class="results-title">{{ 'mass_order.order_results' | translate }}</h3>
      </div>

      <div class="results-summary">
        <div class="summary-card success">
          <div class="summary-icon">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
            </svg>
          </div>
          <div class="summary-content">
            <div class="summary-count">{{ successCount }}</div>
            <div class="summary-label">{{ 'simple_theme.mass_order.result_successful' | translate }}</div>
          </div>
        </div>

        <div class="summary-card failed">
          <div class="summary-icon">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </div>
          <div class="summary-content">
            <div class="summary-count">{{ failedCount }}</div>
            <div class="summary-label">{{ 'simple_theme.mass_order.result_failed' | translate }}</div>
          </div>
        </div>

        <div class="summary-card total">
          <div class="summary-icon">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
            </svg>
          </div>
          <div class="summary-content">
            <div class="summary-count">{{ getTotalCount() }}</div>
            <div class="summary-label">{{ 'mass_order.total' | translate }}</div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="results-list">
      <div class="results-list-header">
        <h4 class="list-title">{{ 'simple_theme.mass_order.detailed_results' | translate }}</h4>
        <div class="list-filters">
          <!-- Future: Add filter buttons here -->
        </div>
      </div>
      
      <div class="results-items">
        <div *ngFor="let result of orderResults; let i = index" 
             class="result-item"
             [class.success]="result.success"
             [class.failed]="!result.success">
          <div class="result-index">{{ i + 1 }}</div>
          <div class="result-status">
            <svg *ngIf="result.success" viewBox="0 0 24 24" fill="currentColor">
              <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
            </svg>
            <svg *ngIf="!result.success" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </div>
          <div class="result-content">
            <div class="result-link">{{ result.link }}</div>
            <div *ngIf="!result.success" class="result-message">{{ result.message }}</div>
          </div>
          <div class="result-badge">
            <span class="badge" [class.success]="result.success" [class.failed]="!result.success">
              {{ result.success ? ('mass_order.success' | translate) : ('mass_order.failed' | translate) }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>

</div>
