import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { ToastService } from '../../../core/services/toast.service';
import { IconsModule } from '../../../icons/icons.module';
import { TenantCurrencyRes, TenantCurrencySettings } from '../../../model/response/tenant-currency-res.model';
import { TenantCurrencyReq } from '../../../model/request/tenant-currency-req.model';
import { TenantCurrencyService } from '../../../core/services/tenant-currency.service';
import { Currency } from '../../../model/response/currency-res.model';


@Component({
  selector: 'app-currency-settings',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IconsModule
  ],
  templateUrl: './currency-settings.component.html',
  styleUrl: './currency-settings.component.css'
})
export class CurrencySettingsComponent implements OnInit {
  isLoading = false;
  isSyncing = false;
  searchText = '';
  tenantCurrencies: TenantCurrencyRes | null = null;
  selectedCurrencies: Set<string> = new Set();
  editingRates: { [currencyCode: string]: number } = {};
  showCurrencySelector = false;
  availableCurrenciesForSelection: Currency[] = [];

  constructor(
    private tenantCurrencyService: TenantCurrencyService,
    private toastService: ToastService,
    private translateService: TranslateService
  ) {}

  ngOnInit(): void {
    this.loadTenantCurrencies();
  }

  loadTenantCurrencies(): void {
    this.isLoading = true;
    this.tenantCurrencyService.getTenantCurrencies().subscribe({
      next: (response) => {
        this.tenantCurrencies = response;
        this.selectedCurrencies = new Set(response.available_currencies);
        // Initialize editing rates with effective rates (custom rate if sync disabled, otherwise currency rate)
        response.all_currencies.forEach(currency => {
          const settings = this.getCurrencySettings(currency.code);
          let effectiveRate: number;

          if (settings && !settings.sync_enabled && settings.custom_rate) {
            effectiveRate = settings.custom_rate;
          } else {
            effectiveRate = currency.exchange_rate;
          }

          this.editingRates[currency.code] = effectiveRate;
        });
        // Get currencies available for selection (excluding already selected and base currency)
        this.availableCurrenciesForSelection = response.all_currencies.filter(currency =>
          !this.selectedCurrencies.has(currency.code) && !currency.base_currency
        );
        this.isLoading = false;
      },
      error: (error) => {
        this.toastService.showError(error?.message || 'Failed to load currencies');
        this.isLoading = false;
      }
    });
  }

  get availableCurrencies(): Currency[] {
    if (!this.tenantCurrencies?.all_currencies) return [];

    return this.tenantCurrencies.all_currencies.filter(currency =>
      this.selectedCurrencies.has(currency.code)
    );
  }

  get filteredAvailableCurrencies(): Currency[] {
    return this.availableCurrencies.filter(currency =>
      currency.name.toLowerCase().includes(this.searchText.toLowerCase()) ||
      currency.code.toLowerCase().includes(this.searchText.toLowerCase())
    );
  }

  get filteredCurrenciesForSelection(): Currency[] {
    return this.availableCurrenciesForSelection.filter(currency =>
      currency.name.toLowerCase().includes(this.searchText.toLowerCase()) ||
      currency.code.toLowerCase().includes(this.searchText.toLowerCase())
    );
  }

  getCurrencySettings(currencyCode: string): TenantCurrencySettings | null {
    return this.tenantCurrencies?.currency_settings?.find(s => s.currency_code === currencyCode) || null;
  }

  toggleCurrencySync(currency: Currency): void {
    if (currency.base_currency) {
      this.toastService.showError('Base currency sync settings cannot be modified');
      return;
    }

    const currentSettings = this.getCurrencySettings(currency.code);
    const newSyncStatus = !(currentSettings?.sync_enabled ?? true);
    const currencySyncSettings = { [currency.code]: newSyncStatus };

    this.updateCurrencySettings({ currency_sync_settings: currencySyncSettings });
  }

  togglePaymentSync(currency: Currency): void {
    if (currency.base_currency) {
      this.toastService.showError('Base currency payment sync settings cannot be modified');
      return;
    }

    const currentSettings = this.getCurrencySettings(currency.code);
    const newPaymentSyncStatus = !(currentSettings?.payment_sync_enabled ?? false);
    const paymentSyncSettings = { [currency.code]: newPaymentSyncStatus };

    this.updateCurrencySettings({ payment_sync_settings: paymentSyncSettings });
  }

  updateCurrencySettings(settings: Partial<TenantCurrencyReq>): void {
    this.isLoading = true;
    const availableCurrencies = Array.from(this.selectedCurrencies);

    const request: TenantCurrencyReq = {
      available_currencies: availableCurrencies,
      ...settings
    };

    this.tenantCurrencyService.updateTenantCurrencies(request).subscribe({
      next: (response) => {
        this.tenantCurrencies = response;

        // Update editing rates with new effective rates
        response.all_currencies.forEach(currency => {
          const currencySettings = this.getCurrencySettings(currency.code);
          let effectiveRate: number;

          if (currencySettings && !currencySettings.sync_enabled && currencySettings.custom_rate) {
            effectiveRate = currencySettings.custom_rate;
          } else {
            effectiveRate = currency.exchange_rate;
          }

          this.editingRates[currency.code] = effectiveRate;
        });

        this.toastService.showSuccess('Currency settings updated successfully');
        this.isLoading = false;
      },
      error: (error) => {
        this.toastService.showError(error?.message || 'Failed to update currency settings');
        this.isLoading = false;
      }
    });
  }

  updateExchangeRate(currency: Currency): void {
    const newRate = this.editingRates[currency.code];
    const settings = this.getCurrencySettings(currency.code);
    const currentEffectiveRate = settings?.custom_rate || currency.exchange_rate;



    // Validate input
    if (!newRate || newRate <= 0) {
      this.toastService.showError(this.translateService.instant('currency_settings.please_enter_valid_rate'));
      return;
    }

    // Check if rate actually changed
    if (Math.abs(newRate - currentEffectiveRate) < 0.0001) {
      this.toastService.showSuccess(this.translateService.instant('currency_settings.rate_already_updated'));
      return;
    }

    // Check if sync is disabled (required for custom rates)
    if (settings?.sync_enabled) {
      this.toastService.showError(this.translateService.instant('currency_settings.cannot_set_custom_rate_sync_enabled'));
      return;
    }

    const customRates = { [currency.code]: newRate };
    this.updateCurrencySettings({ custom_rates: customRates });
  }

  triggerSync(): void {
    const hasAnySyncEnabled = this.tenantCurrencies?.currency_settings?.some(s => s.sync_enabled) ?? false;

    if (!hasAnySyncEnabled) {
      this.toastService.showError('No currencies have sync enabled');
      return;
    }

    this.isSyncing = true;

    this.tenantCurrencyService.triggerCurrencySync().subscribe({
      next: () => {
        this.toastService.showSuccess('Currency rates updated from latest sync data');
        this.loadTenantCurrencies(); // Reload to get updated rates and sync time
        this.isSyncing = false;
      },
      error: (error) => {
        this.toastService.showError(error?.message || 'Failed to update currency rates');
        this.isSyncing = false;
      }
    });
  }

  addCurrency(currency: Currency): void {
    this.selectedCurrencies.add(currency.code);
    this.availableCurrenciesForSelection = this.availableCurrenciesForSelection.filter(c => c.code !== currency.code);
    this.updateCurrencySettings({});
  }

  removeCurrency(currency: Currency): void {
    if (currency.base_currency) {
      this.toastService.showError('Base currency cannot be removed');
      return;
    }

    this.selectedCurrencies.delete(currency.code);
    this.availableCurrenciesForSelection.push(currency);
    this.updateCurrencySettings({});
  }

  formatLastSync(lastSync?: string | null): string {
    if (!lastSync) return 'Never';
    return new Date(lastSync).toLocaleString();
  }

  canEditRate(currency: Currency): boolean {
    const settings = this.getCurrencySettings(currency.code);
    return !(settings?.sync_enabled ?? true);
  }

  toggleCurrencySelector(): void {
    this.showCurrencySelector = !this.showCurrencySelector;
    if (this.showCurrencySelector) {
      this.searchText = '';
    }
  }
}
