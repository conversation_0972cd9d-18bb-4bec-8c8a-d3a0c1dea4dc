/* Modern Sidebar Theme Styles */
.modern-sidebar {
  position: fixed;
  top: 0;
  left: -320px;
  width: 320px;
  height: 100vh;
  background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
  box-shadow: 4px 0 24px rgba(0, 0, 0, 0.25);
  transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
  overflow: hidden;
  border-right: 1px solid rgba(148, 163, 184, 0.1);
}

.modern-sidebar.open {
  left: 0;
}

.modern-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.sidebar-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

/* Header Section */
.sidebar-header {
  padding: 2rem 1.5rem 1.5rem 1.5rem;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.logo-area {
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.logo-area:hover {
  transform: translateX(4px);
}

.logo-icon {
  width: 2.5rem;
  height: 2.5rem;
  color: #fbbf24;
  filter: drop-shadow(0 4px 8px rgba(251, 191, 36, 0.3));
}

.logo-text {
  display: flex;
  flex-direction: column;
}

.logo-title {
  font-size: 1.5rem;
  font-weight: 800;
  color: white;
  line-height: 1;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.logo-subtitle {
  font-size: 0.75rem;
  color: #94a3b8;
  font-weight: 500;
  letter-spacing: 0.1em;
  text-transform: uppercase;
}



/* Navigation Container */
.nav-container {
  flex: 1;
  padding: 1.5rem 1rem;
  overflow-y: auto;
  overflow-x: hidden;
}

.nav-section {
  margin-bottom: 2rem;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.section-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  white-space: nowrap;
}

.section-line {
  flex: 1;
  height: 1px;
  background: linear-gradient(90deg, rgba(148, 163, 184, 0.3), transparent);
}

.menu-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.nav-item {
  position: relative;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 16px;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  background: rgba(148, 163, 184, 0.05);
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.6s ease;
}

.nav-link:hover::before {
  left: 100%;
}

.nav-link:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateX(4px);
}

.nav-item.active .nav-link {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-color: #3b82f6;
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
}

/* Icon Container */
.icon-container {
  width: 2.5rem;
  height: 2.5rem;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.nav-item.active .icon-container {
  background: rgba(255, 255, 255, 0.2);
}

/* Nav Content */
.nav-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.nav-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #cbd5e1;
  transition: color 0.3s ease;
}

.nav-item.active .nav-label {
  color: white;
}

.nav-description {
  font-size: 0.75rem;
  color: #64748b;
  transition: color 0.3s ease;
}

.nav-item.active .nav-description {
  color: rgba(255, 255, 255, 0.7);
}

/* Nav Arrow */
.nav-arrow {
  width: 1rem;
  height: 1rem;
  color: #64748b;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateX(-8px);
}

.nav-link:hover .nav-arrow {
  opacity: 1;
  transform: translateX(0);
}

.nav-item.active .nav-arrow {
  color: white;
  opacity: 1;
  transform: translateX(0);
}

/* Active Glow */
.active-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(29, 78, 216, 0.2));
  border-radius: 16px;
  pointer-events: none;
  animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 0.8; }
}

/* Footer Section */
.sidebar-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.footer-stats {
  background: rgba(16, 185, 129, 0.1);
  padding: 0.75rem;
  border-radius: 12px;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.stat-icon {
  width: 1rem;
  height: 1rem;
  color: #10b981;
}

.stat-text {
  font-size: 0.75rem;
  color: #10b981;
  font-weight: 500;
}

/* Desktop Styles */
@media (min-width: 768px) {
  .modern-sidebar {
    position: relative;
    left: 0;
    width: 320px;
    height: 100vh;
    overflow: hidden;
  }

  .modern-sidebar.open {
    left: 0;
  }

  .sidebar-wrapper {
    overflow: hidden;
  }
}

/* Tablet Responsive */
@media (max-width: 1024px) {
  .modern-sidebar {
    width: 300px;
  }

  .sidebar-header {
    padding: 1.5rem 1rem 1rem 1rem;
  }

  .nav-container {
    padding: 1rem 0.75rem;
  }

  .nav-link {
    padding: 0.875rem;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .modern-sidebar {
    width: 100%;
    max-width: 320px;
  }

  .logo-title {
    font-size: 1.25rem;
  }

  .nav-link {
    padding: 0.75rem;
  }

  .nav-label {
    font-size: 0.8125rem;
  }

  .nav-description {
    font-size: 0.6875rem;
  }
}

/* Focus States */
.nav-link:focus {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
}

.logo-area:focus {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
  border-radius: 12px;
}

/* Scrollbar Styling for Navigation */
.nav-container::-webkit-scrollbar {
  width: 6px;
}

.nav-container::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
}

.nav-container::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 3px;
}

.nav-container::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}
