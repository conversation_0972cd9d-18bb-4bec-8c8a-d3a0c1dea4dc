<div class="simple-overlay" >
  <div class="simple-modal" (click)="onPopupClick($event)">
    <div class="modal-content">
      
      <!-- Header -->
      <div class="modal-header">
        <div class="header-content">
          <div class="icon-wrapper">
            <i class="fas fa-shield-alt"></i>
          </div>
          <div class="title-section">
            <h2 class="modal-title">{{ 'auth.google_authentication_setting' | translate }}</h2>
            <p class="modal-subtitle">{{ 'auth.secure_your_account' | translate }}</p>
          </div>
        </div>
        <button type="button" class="close-button" (click)="onClose()">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Body -->
      <div class="modal-body">
        
        <!-- Step 1: Download App -->
        <div class="step-section">
          <div class="step-header">
            <span class="step-number">1</span>
            <h3 class="step-title">{{ 'auth.get_google_authenticator' | translate }}</h3>
          </div>
          
          <div class="app-download-grid">
            <a href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2" 
               target="_blank" 
               class="download-card android">
              <div class="download-icon">
                <i class="fab fa-android"></i>
              </div>
              <div class="download-info">
                <span class="download-platform">Android</span>
                <span class="download-text">{{ 'auth.get_on_android' | translate }}</span>
              </div>
            </a>
            
            <a href="https://apps.apple.com/us/app/google-authenticator/id388497605" 
               target="_blank" 
               class="download-card ios">
              <div class="download-icon">
                <i class="fab fa-apple"></i>
              </div>
              <div class="download-info">
                <span class="download-platform">iOS</span>
                <span class="download-text">{{ 'auth.get_on_ios' | translate }}</span>
              </div>
            </a>
          </div>
        </div>

        <!-- Step 2: Scan QR Code -->
        <div class="step-section">
          <div class="step-header">
            <span class="step-number">2</span>
            <h3 class="step-title">{{ 'auth.scan_qr_code_instruction' | translate }}</h3>
          </div>

          <div class="qr-section">
            <div class="qr-container">
              <div *ngIf="isLoading" class="loading-state">
                <div class="spinner"></div>
                <p>{{ 'common.generating' | translate }}...</p>
              </div>
              <div *ngIf="!isLoading && qrCodeImage" class="qr-code-wrapper">
                <img [src]="qrCodeImage" alt="QR Code" class="qr-code">
              </div>
            </div>

            <!-- Manual Entry -->
            <div class="manual-entry" *ngIf="secretKey">
              <div class="info-card">
                <div class="info-icon">
                  <i class="fas fa-key"></i>
                </div>
                <div class="info-content">
                  <h4>{{ 'auth.manual_entry_instruction' | translate }}</h4>
                  <div class="secret-key-container">
                    <code class="secret-key">{{ secretKey }}</code>
                    <button type="button" class="copy-button" (click)="copySecretKey()">
                      <i class="fas fa-copy"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 3: Verify -->
        <div class="step-section">
          <div class="step-header">
            <span class="step-number">3</span>
            <h3 class="step-title">{{ 'auth.verify_setup' | translate }}</h3>
          </div>

          <form [formGroup]="mfaForm" (ngSubmit)="onSubmit()" class="verify-form">
            
            <!-- Authentication Code -->
            <div class="form-group">
              <label for="code" class="form-label">
                <i class="fas fa-mobile-alt"></i>
                {{ 'auth.enter_authentication_code' | translate }}
              </label>
              <input
                type="text"
                id="code"
                formControlName="code"
                class="form-input code-input"
                placeholder="000000"
                maxlength="6"
                [ngClass]="{'input-error': mfaForm.get('code')?.invalid && mfaForm.get('code')?.touched}"
              >
              <div *ngIf="mfaForm.get('code')?.invalid && mfaForm.get('code')?.touched" class="error-message">
                <i class="fas fa-exclamation-circle"></i>
                <span *ngIf="mfaForm.get('code')?.errors?.['required']">{{ 'auth.code_required' | translate }}</span>
                <span *ngIf="mfaForm.get('code')?.errors?.['pattern'] || mfaForm.get('code')?.errors?.['minlength'] || mfaForm.get('code')?.errors?.['maxlength']">
                  {{ 'auth.code_must_be_6_digits' | translate }}
                </span>
              </div>
            </div>

            <!-- Password -->
            <div class="form-group">
              <label for="password" class="form-label">
                <i class="fas fa-lock"></i>
                {{ 'auth.password' | translate }}
              </label>
              <div class="password-wrapper">
                <input
                  [type]="showPassword ? 'text' : 'password'"
                  id="password"
                  formControlName="password"
                  class="form-input"
                  placeholder="{{ 'auth.enter_password' | translate }}"
                  [ngClass]="{'input-error': mfaForm.get('password')?.invalid && mfaForm.get('password')?.touched}"
                >
                <button type="button" class="toggle-password" (click)="togglePasswordVisibility()">
                  <i class="fas" [ngClass]="showPassword ? 'fa-eye-slash' : 'fa-eye'"></i>
                </button>
              </div>
              <div *ngIf="mfaForm.get('password')?.invalid && mfaForm.get('password')?.touched" class="error-message">
                <i class="fas fa-exclamation-circle"></i>
                <span *ngIf="mfaForm.get('password')?.errors?.['required']">{{ 'auth.password_required' | translate }}</span>
              </div>
            </div>

            <!-- Submit Button -->
            <div class="form-actions">
              <button type="submit" class="submit-button" [disabled]="mfaForm.invalid || isLoading">
                <span *ngIf="!isLoading" class="button-content">
                  <i class="fas fa-shield-check"></i>
                  {{ 'auth.enable' | translate }}
                </span>
                <span *ngIf="isLoading" class="button-loading">
                  <div class="button-spinner"></div>
                  {{ 'common.processing' | translate }}...
                </span>
              </button>
            </div>
          </form>
        </div>

      </div>
    </div>
  </div>
</div>
