import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { DatePipe } from '@angular/common';

// Base component
import { BaseCashFlowComponent } from '../../base-cash-flow.component';

// Services
import { CashFlowLogicService } from '../../services/cash-flow-logic.service';

// Components
import { IconsModule } from '../../../../icons/icons.module';
import { TransactionLabelComponent } from '../../../common/transaction-label/transaction-label.component';
import { DateRangePickerComponent } from '../../../common/date-range-picker/date-range-picker.component';
import { LiteDropdownComponent } from '../../../common/lite-dropdown/lite-dropdown.component';
import { CurrencyConvertPipe } from '../../../../core/pipes/currency-convert.pipe';
import { LoadingComponent } from '../../../common/loading/loading.component';

@Component({
  selector: 'app-simple-cash-flow',
  standalone: true,
  imports: [
    CommonModule,
    IconsModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    DatePipe,
    TransactionLabelComponent,
    DateRangePickerComponent,
    LiteDropdownComponent,
    CurrencyConvertPipe,
    LoadingComponent
  ],
  templateUrl: './simple-cash-flow.component.html',
  styleUrl: './simple-cash-flow.component.css'
})
export class SimpleCashFlowComponent extends BaseCashFlowComponent implements OnInit, OnDestroy {

  constructor(cashFlowLogicService: CashFlowLogicService) {
    super(cashFlowLogicService);
  }

  override ngOnInit(): void {
    super.ngOnInit();
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
  }

  // Helper method for pagination
  getPageNumbers(): number[] {
    const totalPages = this.cashFlowLogicService.totalPages;
    const currentPage = this.cashFlowLogicService.currentPage;
    const pages: number[] = [];

    // Show max 5 page numbers
    const maxPages = 5;
    let startPage = Math.max(0, currentPage - Math.floor(maxPages / 2));
    let endPage = Math.min(totalPages - 1, startPage + maxPages - 1);

    // Adjust start page if we're near the end
    if (endPage - startPage < maxPages - 1) {
      startPage = Math.max(0, endPage - maxPages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  // Expose Math for template
  Math = Math;

  // Date range change handler
  override onDateRangeChanged(dateRange: { startDate: Date | null; endDate: Date | null }): void {
    // The date range picker already emits the correct format
    this.cashFlowLogicService.updateDateRange(dateRange);
  }
}
