package tndung.vnfb.smm.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Filter;
import org.hibernate.annotations.FilterDef;
import org.hibernate.annotations.ParamDef;
import tndung.vnfb.smm.entity.audit.AbstractTenantCreatedEntity;

import javax.persistence.*;
import java.math.BigDecimal;

@Entity
@Table(name = "tenant_currency_settings")
@Data
@EqualsAndHashCode(callSuper=false)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FilterDef(name = "tenantFilter", parameters = { @ParamDef(name = "tenantId", type = "string") ,  @ParamDef(name = "wildcardTenant", type = "string") })
@Filter(name = "tenantFilter", condition = "(tenant_id = :tenantId  OR tenant_id = :wildcardTenant)")
public class TenantCurrencySettings extends AbstractTenantCreatedEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "currency_code", nullable = false)
    private String currencyCode;

    @Column(name = "sync_enabled")
    @Builder.Default
    private Boolean syncEnabled = true;

    @Column(name = "payment_sync_enabled")
    @Builder.Default
    private Boolean paymentSyncEnabled = false;

    @Column(name = "custom_rate", precision = 20, scale = 8)
    private BigDecimal customRate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "currency_code", insertable = false, updatable = false)
    private Currency currency;
}
