import { Component, EventEmitter, HostListener, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';
import { VoucherService } from '../../../core/services/voucher.service';
import { VoucherReq, VoucherType } from '../../../model/request/voucher-req.model';
import { VoucherRes } from '../../../model/response/voucher-res.model';
import { LiteDropdownComponent } from '../../common/lite-dropdown/lite-dropdown.component';

@Component({
  selector: 'app-create-promo-code',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IconsModule,
    LiteDropdownComponent
  ],
  templateUrl: './create-promo-code.component.html',
  styleUrls: ['./create-promo-code.component.css']
})
export class CreatePromoCodeComponent implements OnInit {
  @Output() close = new EventEmitter<void>();
  @Output() voucherCreated = new EventEmitter<VoucherRes>();

  // Form data
  promoCodeTypes: string[] = [];
  selectedType: string = '';
  promoCode: string = '';
  discountValue: number = 0;
  activationsCount: number = 1;

  // Map display names to enum values
  private typeMap: { [key: string]: VoucherType } = {};

  // Error handling
  errorMessage: string = '';
  isSubmitting: boolean = false;

  constructor(
    private voucherService: VoucherService,
    private translateService: TranslateService
  ) {}

  ngOnInit(): void {
    this.initializePromoCodeTypes();
  }

  private initializePromoCodeTypes(): void {
    const topUpBalance = this.translateService.instant('promo_codes.top_up_balance');
    const discountForOrder = this.translateService.instant('promo_codes.discount_for_order');

    this.promoCodeTypes = [topUpBalance, discountForOrder];
    this.selectedType = topUpBalance;

    this.typeMap = {
      [topUpBalance]: VoucherType.TOP_UP,
      [discountForOrder]: VoucherType.ORDER
    };
  }

  @HostListener('document:keydown.escape')
  onEscapeKey(): void {
    this.onClose();
  }

  onClose(): void {
    this.close.emit();
  }

  onSelectType(type: string): void {
    this.selectedType = type;
  }

  isDiscountType(): boolean {
    return this.typeMap[this.selectedType] === VoucherType.ORDER;
  }

  generateRandomCode(): void {
    this.promoCode = this.voucherService.generateRandomCode();
  }

  onSubmit(): void {
    // Validate form
    if (!this.promoCode) {
      this.errorMessage = this.translateService.instant('promo_codes.validation.code_required');
      return;
    }

    if (this.discountValue <= 0) {
      this.errorMessage = this.translateService.instant('promo_codes.validation.amount_required');
      return;
    }

    // For Order type (percentage), validate that the value is between 0 and 100
    if (this.typeMap[this.selectedType] === VoucherType.ORDER && this.discountValue > 100) {
      this.errorMessage = this.translateService.instant('promo_codes.validation.percentage_max');
      return;
    }

    if (this.activationsCount <= 0) {
      this.errorMessage = this.translateService.instant('promo_codes.validation.activations_required');
      return;
    }

    this.isSubmitting = true;
    this.errorMessage = '';

    const voucherReq: VoucherReq = {
      code: this.promoCode,
      discount_value: this.discountValue,
      usage_limit: this.activationsCount,
      type: this.typeMap[this.selectedType]
    };

    this.voucherService.createVoucher(voucherReq).subscribe({
      next: (response) => {
        this.isSubmitting = false;
        this.voucherCreated.emit(response);
      },
      error: (error) => {
        this.isSubmitting = false;
        this.errorMessage = error.message || this.translateService.instant('promo_codes.validation.create_failed');
        console.error('Error creating voucher:', error);
      }
    });
  }
}
