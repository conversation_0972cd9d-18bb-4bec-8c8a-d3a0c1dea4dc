import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ToggleSwitchComponent } from "../../common/toggle-switch/toggle-switch.component";
import { IconsModule } from "../../../icons/icons.module";
import { TranslateModule } from '@ngx-translate/core';
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';
import { SuperGeneralSvReq } from '../../../model/request/super-general-sv-req.model';
import { LiteDropdownComponent } from "../../common/lite-dropdown/lite-dropdown.component";
import { IconDropdownComponent } from "../../common/icon-dropdown/icon-dropdown.component";
import { IconBaseModel } from "../../../model/base-model";
import { ServiceLabel } from "../../../model/response/service-label.model";
import { TagLabelComponent } from "../../common/tag-label/tag-label.component";
import { AdminServiceService } from "../../../core/services/admin-service.service";
import { ProviderRes } from "../../../model/response/provider-res.model";
import { SMMServiceRes } from "../../../model/response/smm-service-res.model";
import { SuperPlatformRes } from "../../../model/response/super-platform.model";
import { ToastService } from "../../../core/services/toast.service";
import { UIStateService } from "../../../core/services/ui-state.service";
import { CurrencyService } from "../../../core/services/currency.service";


// Enums for service types
export enum AddType {
  Manual = 'Manual',
  Api = 'Api'
}

export enum ServiceType {
  Default = 'Default',
  Comment = 'Custom Comments',
  // Package = 'Package'
}

@Component({
  selector: 'app-new-service',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ToggleSwitchComponent,
    IconsModule,
    TranslateModule,
    LiteDropdownComponent,
    IconDropdownComponent,
    TagLabelComponent
  ],
  templateUrl: './new-service.component.html',
  styleUrl: './new-service.component.css'
})
export class NewServiceComponent implements OnInit {
  @Input() serviceType: string = 'Provider';
  @Input() serviceToEdit: SuperGeneralSvRes | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() serviceAdded = new EventEmitter<SuperGeneralSvRes>();
  @Output() serviceUpdated = new EventEmitter<SuperGeneralSvRes>();
  @Output() openResources = new EventEmitter<void>();

  // Service type description
  serviceTypeDescription: string = 'Add working services from provider panels.';

  // Provider details
  providers: ProviderRes[] = [];
  selectedProvider: ProviderRes | null = null;
  providerOptions: IconBaseModel[] = [];

  // Service ID details
  serviceId: number | null = null;
  serviceIdOptions: IconBaseModel[] = [];
  filteredServiceIdOptions: IconBaseModel[] = []; // Filtered options for search
  selectedServiceId: IconBaseModel | null = null;
  services: SMMServiceRes[] = [];
  isLoadingServices: boolean = false;
  serviceSearchQuery: string = ''; // Search query for service ID/name
  isDropdownOpen: boolean = false; // Track if the dropdown is open

  // Description
  serviceName: string = '';
  serviceTypeOption: string = ServiceType.Default;
  serviceTypeOptions: string[] = [ServiceType.Default, ServiceType.Comment];
  description: string = '';

  // Add Type - Always set to Api
  addTypeOption: AddType = AddType.Api;

  // Category
  category: string = '';
  allPlatforms: SuperPlatformRes[] = [];
  categoryOptions: IconBaseModel[] = [];
  selectedCategory: IconBaseModel | undefined;
  iconType: string = 'No icon';

  // Toggle options
  userFriendlyDescription: boolean = false;
  seoSettings: boolean = false;
  additionalOptions: boolean = false;

  // User-friendly description options
  speedPerDay: string = '';


  linkSample: string = '';

  // Service labels
  serviceLabels: ServiceLabel[] = [];
  newLabelText: string = '';
  labelTypes: ('Green' | 'Red')[] = ['Green', 'Red'];
  selectedLabelType: 'Green' | 'Red' = 'Green';

  // Predefined label options
  predefinedLabels: {text: string, type: 'Green' | 'Red'}[] = [
    { text: 'Real', type: 'Green' },
    { text: 'Mix', type: 'Green' },
    { text: 'Bots', type: 'Red' },
    { text: 'HQ', type: 'Green' },
    { text: 'Fast', type: 'Green' },
    { text: 'Slow', type: 'Red' },
    { text: 'No Drop', type: 'Green' },
    { text: 'Drop', type: 'Red' }
  ];

  // Additional options
  cancelButton: boolean = false;
  refill: boolean = false;
  refillDays: number = 30; // Default value for refill days
  overflow: boolean = false;
  overflowValue: number = 0; // Default value for overflow
  oneOrderInOneHand: boolean = false;

  // Price settings
  providerPrice: number = 1;
  extraPricePercentage: number = 0;
  finalPrice: number = 1.00;
  fixedPrice: boolean = false;

  // Min/Max values
  minValue: number = 1000;
  maxValue: number = 5000;

  // Sync toggles
  syncAll: boolean = true;
  syncMinMax: boolean = true;
  syncRefill: boolean = true;
  syncCancel: boolean = true;
  syncStatus: boolean = true;

  // Loading state
  isSubmitting: boolean = false;

  // Edit mode flag
  isEditMode: boolean = false;

  constructor(
    private adminService: AdminServiceService,
    private toastService: ToastService,
    public uiStateService: UIStateService,
    private currencyService: CurrencyService
  ) {
    this.calculateFinalPrice();
  }

  loadCategories(): void {
    // Load platforms and categories from the admin service
    this.adminService.getPlatformsWithServices().subscribe({
      next: (platforms) => {
        this.allPlatforms = platforms;

        // Create category options from all platforms
        this.categoryOptions = [];
        platforms.forEach(platform => {
          platform.categories.forEach(category => {
            this.categoryOptions.push({
              id: category.id.toString(),
              label: category.name,
              icon: platform.icon || '',
              sort: category.sort
            });
          });
        });

        // If we're editing a service, select the correct category
        if (this.serviceToEdit && this.serviceToEdit.category_id) {
          const categoryId = this.serviceToEdit.category_id.toString();
          const foundCategory = this.categoryOptions.find(c => c.id === categoryId);
          if (foundCategory) {
            this.selectedCategory = foundCategory;
          } else if (this.categoryOptions.length > 0) {
            this.selectedCategory = this.categoryOptions[0];
          }
        } else if (this.categoryOptions.length > 0) {
          // Set default selected category if available
          this.selectedCategory = this.categoryOptions[0];
        }
      },
      error: (error) => {
        console.error('Error loading categories:', error);
      }
    });
  }

  loadProviders(): void {
    this.adminService.getProviders().subscribe({
      next: (providers) => {
        this.providers = providers;

        // Create provider options for dropdown
        this.providerOptions = providers.map(provider => ({
          id: provider.id.toString(),
          label: provider.name,
          icon: '',
          sort: 0
        }));

        // If we're editing a service, select the correct provider
        if (this.serviceToEdit) {
          // Use any to access properties that might not be in the type definition

          const providerId = this.serviceToEdit.api_provider.id;

          if (providerId) {
            const foundProvider = providers.find(p => p.id === providerId);
            if (foundProvider) {
              this.selectedProvider = foundProvider;
              // Load services for this provider
              this.loadServicesForProvider(foundProvider.id);
            } else if (this.providerOptions.length > 0) {
              this.selectedProvider = providers[0];
              // Automatically load services for the first provider
              this.loadServicesForProvider(providers[0].id);
            }
          } else if (this.providerOptions.length > 0) {
            this.selectedProvider = providers[0];
            // Automatically load services for the first provider
            this.loadServicesForProvider(providers[0].id);
          }
        } else if (this.providerOptions.length > 0) {
          // Set default selected provider if available
          this.selectedProvider = providers[0];
          // Automatically load services for the first provider
          this.loadServicesForProvider(providers[0].id);
        }
      },
      error: (error) => {
        console.error('Error loading providers:', error);
      }
    });
  }

  closePopup() {
    // Store a reference to serviceToEdit before emitting close
    const serviceBeingEdited = this.serviceToEdit;
    const isEditMode = serviceBeingEdited !== null && serviceBeingEdited !== undefined;

    console.log('closePopup - Is edit mode:', isEditMode);
    console.log('closePopup - serviceToEdit before closing:', serviceBeingEdited);

    if (isEditMode) {
      console.log('closePopup - Closing in edit mode with service ID:', serviceBeingEdited!.id);
    }

    // Emit close event
    this.close.emit();

    // Enable scrolling on the body when the popup is closed
    document.body.style.overflow = 'auto';
  }

  ngOnInit(): void {
    // Disable scrolling on the body when the popup is opened
    // This ensures only the popup has a scrollbar
    document.body.style.overflow = 'hidden';

    // Debug logging to check serviceToEdit value
    console.log('ngOnInit - serviceToEdit:', this.serviceToEdit);
    console.log('ngOnInit - serviceToEdit type:', typeof this.serviceToEdit);
    console.log('ngOnInit - serviceToEdit is null:', this.serviceToEdit === null);
    console.log('ngOnInit - serviceToEdit is undefined:', this.serviceToEdit === undefined);

    // Check if we're in edit mode
    this.isEditMode = this.serviceToEdit !== null && this.serviceToEdit !== undefined;
    console.log('ngOnInit - Is edit mode:', this.isEditMode);

    // Set the service type description based on the service type
    this.updateServiceTypeDescription();

    // Initialize data
    this.loadCategories();

    // Only load providers if we're in Provider mode
    if (this.serviceType === 'Provider') {
      this.loadProviders();
    }

    // If we have a service to edit, populate the form
    if (this.isEditMode) {
      console.log('ngOnInit - Populating form for edit with service ID:', this.serviceToEdit!.id);
      this.populateFormForEdit();
    }

    console.log('NewServiceComponent initialized: ', this.serviceToEdit);
  }

  /**
   * Populates the form with data from the service being edited
   */
  private populateFormForEdit(): void {
    if (!this.serviceToEdit) return;

    // For API services, we'll set most values after selecting the provider and service
    const isApiService = this.serviceToEdit.add_type === AddType.Api;

    // Always set these basic details
    this.serviceName = this.serviceToEdit.name;
    this.description = this.serviceToEdit.description || '';
    this.serviceTypeOption = this.serviceToEdit.type || ServiceType.Default;

    // Set price details
    this.extraPricePercentage = this.serviceToEdit.percent || 0;
    this.finalPrice = this.serviceToEdit.price;

    // For API services, we'll set min/max/providerPrice after selecting the service
    if (!isApiService) {
      this.minValue = this.serviceToEdit.min;
      this.maxValue = this.serviceToEdit.max;
      this.providerPrice = this.serviceToEdit.original_price || this.serviceToEdit.price;
    }

    // Handle fixed price
    this.fixedPrice = this.serviceToEdit.is_fixed_price || false;

    // Set additional options
    this.refill = this.serviceToEdit.refill || false;
    if (this.serviceToEdit.refill_days) {
      this.refillDays = parseInt(this.serviceToEdit.refill_days.toString());
    }

    // Handle overflow properties
    this.overflow = this.serviceToEdit.is_overflow || false;
    this.overflowValue = this.serviceToEdit.overflow || 0;
    this.linkSample = this.serviceToEdit.sample_link || '';

    // Set sync settings
    this.syncMinMax = this.serviceToEdit.sync_min_max || false;
    this.syncRefill = this.serviceToEdit.sync_refill || false;
    this.syncCancel = this.serviceToEdit.sync_cancel || false;
    this.syncStatus = this.serviceToEdit.sync_status || false;
    // Update syncAll based on individual sync states
    this.updateSyncAllState();

    // Set user-friendly description fields
    this.speedPerDay = this.serviceToEdit.speed_per_day ? this.serviceToEdit.speed_per_day.toString() : '';
    // Use type assertion with 'as any' to safely access properties that might not be in the type definition (cancel_button)
    this.cancelButton = this.serviceToEdit.cancel_button || false;


    // Set service labels
    this.serviceLabels = this.serviceToEdit.labels || [];

    // For API services, we'll set the service ID after selecting the provider
    if (!isApiService && this.serviceToEdit.api_service_id) {
      this.serviceId = typeof this.serviceToEdit.api_service_id === 'string'
        ? parseInt(this.serviceToEdit.api_service_id)
        : this.serviceToEdit.api_service_id;
    }

    // Automatically open User-friendly description section if any field has a value
    if (this.speedPerDay || this.linkSample) {
      this.userFriendlyDescription = true;
      console.log('Auto-opening User-friendly description section due to existing values');
    }

    // Automatically open Additional options section if any field has a value or is true
    if (this.cancelButton || this.refill || this.overflow || this.overflowValue > 0 || this.oneOrderInOneHand) {
      this.additionalOptions = true;
      console.log('Auto-opening Additional options section due to existing values');
    }

    console.log('Service to edit: ', this.serviceToEdit);

    // We'll set the category and provider in the loadCategories and loadProviders callbacks
  }

  /**
   * Updates the service type description based on the service type
   */
  private updateServiceTypeDescription(): void {
    switch (this.serviceType) {
      case 'Provider':
        this.serviceTypeDescription = 'Add working services from provider panels.';
        break;
      case 'Manual':
        this.serviceTypeDescription = 'Make any service you like, but you have to complete it with admin api or manually.';
        break;
      case 'BotsApi':
        this.serviceTypeDescription = 'Make own service which you have to complete with soft using bots.socpanel.com';
        break;
      default:
        this.serviceTypeDescription = 'Add working services from provider panels.';
    }
  }

  loadServicesForProvider(providerId: number): void {
    this.isLoadingServices = true;
    this.serviceIdOptions = [];
    this.filteredServiceIdOptions = [];
    this.selectedServiceId = null;
    this.serviceSearchQuery = '';

    this.adminService.getProviderServices(providerId).subscribe({
      next: (services) => {
        this.services = services;

        // Create service options for dropdown with price information
        this.serviceIdOptions = services.map(service => ({
          id: service.service.toString(),
          label: `${service.service} - ${service.name} - $${this.currencyService.formatBalance(service.rate)}`,
          icon: '',
          sort: 0
        }));

        console.log('Loaded service options:', this.serviceIdOptions.length);

        // Initialize filtered options with all options
        this.filteredServiceIdOptions = [...this.serviceIdOptions];
        console.log('Initialized filtered options:', this.filteredServiceIdOptions.length);

        // If we're editing a service with addType = Api, find and select the matching service
        if (this.serviceToEdit && this.serviceToEdit.add_type === AddType.Api && this.serviceToEdit.api_service_id) {
          // Get the API service ID
          const apiServiceId = this.serviceToEdit.api_service_id;

          // Find the matching service option
          const matchingServiceOption = this.serviceIdOptions.find(option =>
            option.id === apiServiceId
          );

          if (matchingServiceOption) {
            // Select the matching service
            this.selectedServiceId = matchingServiceOption;
            this.serviceId = parseInt(matchingServiceOption.id);

            // When editing, we need to preserve the original values from the service being edited
            // We don't want to overwrite them with values from the provider service
            console.log('Found and selected matching service for edit:', matchingServiceOption);

            console.log('Service to edit: ', this.serviceToEdit);

            // However, we should update the provider price to match the selected service
            const serviceData = this.services.find(s => s.service.toString() === matchingServiceOption.id);
            if (serviceData) {
              // Only update the provider price, keep other values from the service being edited
              this.providerPrice = serviceData.rate;

              // Check if refill is enabled and automatically open Additional options section
              if (this.refill) {
                this.additionalOptions = true;
              }

              // Recalculate the final price based on the provider price and extra percentage
              this.calculateFinalPrice();
            }
          } else if (this.serviceIdOptions.length > 0) {
            // If no matching service found, select the first one
            const firstService = this.serviceIdOptions[0];
            this.selectedServiceId = firstService;
            this.serviceId = parseInt(firstService.id);
            console.log('No matching service found for edit, selecting first service:', firstService);

            // In this case, we should keep the original values from the service being edited
            // But update the provider price to match the selected service
            const serviceData = this.services.find(s => s.service.toString() === firstService.id);
            if (serviceData) {
              this.providerPrice = serviceData.rate;

              // Check if refill is enabled and automatically open Additional options section
              if (this.refill) {
                this.additionalOptions = true;
              }

              this.calculateFinalPrice();
            }
          }
        } else if (this.serviceIdOptions.length > 0) {
          // For new services, select the first service as default
          const firstService = this.serviceIdOptions[0];
          this.selectedServiceId = firstService;
          this.serviceId = parseInt(firstService.id);

          // Find the service to populate other fields
          const serviceData = this.services.find(s => s.service.toString() === firstService.id);
          if (serviceData) {
            // Use setTimeout to ensure changes happen after Angular's change detection cycle
            setTimeout(() => {
              this.serviceName = serviceData.name;
              this.description = serviceData.type || '';
              this.minValue = serviceData.min;
              this.maxValue = serviceData.max;
              this.providerPrice = serviceData.rate;
              this.refill = serviceData.refill || false;
              // Set default refill days if refill is enabled
              if (this.refill) {
                this.refillDays = 30; // Default value
              }
              this.calculateFinalPrice();
            });
          }

          console.log('Selected serviceToEdit:', this.serviceToEdit);
        }

        this.isLoadingServices = false;
      },
      error: (error) => {
        console.error('Error loading services:', error);
        this.isLoadingServices = false;
      }
    });
  }

  /**
   * Handles blur event on the service search input
   * Filters the service options based on the search query when the user leaves the input field
   * If input is a number, search for exact ID match
   * If input is text, search for partial name match
   */
  onServiceSearchBlur(): void {
    console.log('Search input blur, current query:', this.serviceSearchQuery);

    // If search is empty, show all options and keep current selection
    if (!this.serviceSearchQuery || this.serviceSearchQuery.trim() === '') {
      console.log('Empty search, showing all options:', this.serviceIdOptions.length);
      this.filteredServiceIdOptions = [...this.serviceIdOptions];
      return;
    }

    const searchTerm = this.serviceSearchQuery.trim();
    console.log('Searching for:', searchTerm);

    // Check if the search term is a number (for ID search)
    const isNumberSearch = /^\d+$/.test(searchTerm);

    if (isNumberSearch) {
      console.log('Searching by ID with exact match');
      // Search for exact ID match
      this.filteredServiceIdOptions = this.serviceIdOptions.filter(option => {
        if (!option || !option.id) return false;
        return option.id === searchTerm;
      });

      // Show warning toast if no results found for ID search
      if (this.filteredServiceIdOptions.length === 0) {
        this.toastService.showWarning(`Service with ID "${searchTerm}" not found`);
      }
    } else {
      console.log('Searching by name with partial match');
      // Search for partial name match
      const searchLower = searchTerm.toLowerCase();
      this.filteredServiceIdOptions = this.serviceIdOptions.filter(option => {
        if (!option || !option.label) return false;
        const optionText = option.label.toLowerCase();
        return optionText.includes(searchLower);
      });
    }

    console.log('Filtered options:', this.filteredServiceIdOptions.length);

    // If we have filtered results, automatically select the first matching service
    if (this.filteredServiceIdOptions.length > 0) {
      const firstMatchingService = this.filteredServiceIdOptions[0];

      // Only update if the selection has changed
      if (!this.selectedServiceId || this.selectedServiceId.id !== firstMatchingService.id) {
        console.log('Auto-selecting first matching service:', firstMatchingService.label);
        this.selectedServiceId = firstMatchingService;
        this.serviceId = parseInt(firstMatchingService.id);

        // Update other fields based on the selected service
        this.updateServiceFields(firstMatchingService);
      }
    }
  }

  /**
   * Updates service fields based on the selected service
   */
  private updateServiceFields(serviceOption: IconBaseModel): void {
    // Find the service to populate other fields
    const service = this.services.find(s => s.service.toString() === serviceOption.id);
    if (service) {
      console.log('Updating fields for service:', service.name);

      // Update service fields
      this.serviceName = service.name;
      this.description = '';
      this.serviceTypeOption = service.type || ServiceType.Default;

      // Update min/max values only if syncMinMax is not enabled
      // If syncMinMax is enabled, the values should be controlled by the provider
      if (!this.syncMinMax) {
        this.minValue = service.min;
        this.maxValue = service.max;
      } else {
        // If sync is enabled, ensure we use the provider's values
        this.minValue = service.min;
        this.maxValue = service.max;
      }

      this.providerPrice = service.rate;

      // Update refill based on service data only if syncRefill is not enabled
      // If syncRefill is enabled, the value should be controlled by the provider
      if (!this.syncRefill) {
        this.refill = service.refill || false;
      } else {
        // If sync is enabled, ensure we use the provider's value
        this.refill = service.refill || false;
      }

      // Update cancel button if syncCancel is enabled
      if (this.syncCancel && service.cancel !== undefined) {
        this.cancelButton = service.cancel;
      }

      // Set default refill days if refill is enabled
      if (this.refill) {
        this.refillDays = 30; // Default value

        // Automatically open Additional options section if refill is true
        this.additionalOptions = true;
      }

      // Update the final price
      this.calculateFinalPrice();

      // Log sync status
      console.log('Sync settings - Min/Max:', this.syncMinMax, 'Refill:', this.syncRefill,
                  'Cancel:', this.syncCancel, 'Status:', this.syncStatus);
    }
  }

  ngOnDestroy(): void {
    // Make sure to re-enable scrolling if component is destroyed
    document.body.style.overflow = 'auto';
  }

  onSave() {
    // Debug logging to check serviceToEdit value
    console.log('onSave - serviceToEdit:', this.serviceToEdit);
    console.log('onSave - serviceToEdit type:', typeof this.serviceToEdit);
    console.log('onSave - serviceToEdit is null:', this.serviceToEdit === null);
    console.log('onSave - serviceToEdit is undefined:', this.serviceToEdit === undefined);

    if (this.serviceToEdit) {
      console.log('onSave - serviceToEdit ID:', this.serviceToEdit.id);
      console.log('onSave - serviceToEdit properties:', Object.keys(this.serviceToEdit));
    }

    // Validate required fields based on service type
    if (this.serviceType === 'Api') {
      if (!this.selectedCategory || !this.selectedProvider) {
        this.toastService.showError('Missing required fields: category or provider');
        return;
      }
    } else if (this.serviceType === 'Manual') {
      if (!this.selectedCategory) {
        this.toastService.showError('Missing required field: category');
        return;
      }
    }

    // Set loading state
    this.isSubmitting = true;

    // Ensure selectedCategory has a valid id
    if (!this.selectedCategory?.id) {
      this.toastService.showError('Invalid category selected');
      this.isSubmitting = false;
      return;
    }

    // If this is a Provider service and any sync flags are enabled, update fields from provider first
    if (this.serviceType === 'Provider' && this.selectedProvider && this.serviceId &&
        (this.syncMinMax || this.syncRefill || this.syncCancel || this.syncStatus)) {
      console.log('Updating fields from provider before saving...');
      this.updateFieldsFromProviderBeforeSave();
    } else {
      this.saveService();
    }
  }

  /**
   * Updates fields from provider before saving
   * This ensures we have the latest provider values when sync is enabled
   */
  private updateFieldsFromProviderBeforeSave(): void {
    if (!this.selectedProvider || !this.serviceId) {
      console.warn('Cannot update fields from provider: No provider or service ID selected');
      this.saveService();
      return;
    }

    this.adminService.getProviderServices(this.selectedProvider.id).subscribe({
      next: (services: SMMServiceRes[]) => {
        // Find the selected service in the provider's services
        const providerService = services.find(s => s.service === this.serviceId);

        if (!providerService) {
          console.warn(`Provider service with ID ${this.serviceId} not found`);
          // Continue with save even if provider service is not found
          this.saveService();
          return;
        }

        console.log('Updating fields from provider before save:', providerService);

        // Update min/max if sync is enabled
        if (this.syncMinMax) {
          this.minValue = providerService.min;
          this.maxValue = providerService.max;
          console.log(`Updated min/max values before save: min=${this.minValue}, max=${this.maxValue}`);
        }

        // Update refill if sync is enabled
        if (this.syncRefill && providerService.refill !== undefined) {
          this.refill = providerService.refill;
          console.log(`Updated refill value before save: ${this.refill}`);
        }

        // Update cancel button if sync is enabled
        if (this.syncCancel && providerService.cancel !== undefined) {
          this.cancelButton = providerService.cancel;
          console.log(`Updated cancel button value before save: ${this.cancelButton}`);
        }

        // Status sync is handled by the backend scheduler

        // Continue with save after updating fields
        this.saveService();
      },
      error: (error) => {
        console.error('Error fetching provider services before save:', error);
        // Continue with save even if there's an error
        this.saveService();
      }
    });
  }

  /**
   * Saves the service with current field values
   */
  private saveService(): void {
    // Create a service object using the SuperGeneralSvReq model
    const serviceData: SuperGeneralSvReq = {
      name: this.serviceName,
      description: this.description,
      price: this.finalPrice,
      min: this.minValue,
      max: this.maxValue,
      type: this.serviceTypeOption, // Already a string value from the dropdown
      api_service_id: this.serviceType === 'Provider' ? this.serviceId || null : null,
      // Add other required fields
      price1: 0,
      price2: 0,
      original_price: this.serviceType === 'Provider' ? this.providerPrice : this.finalPrice,

      auto_sync: this.serviceType === 'Provider', // Only auto-sync for Provider services
      // Sync settings - only applicable for Provider services
      sync_min_max: this.serviceType === 'Provider' ? this.syncMinMax : false,
      sync_refill: this.serviceType === 'Provider' ? this.syncRefill : false,
      sync_cancel: this.serviceType === 'Provider' ? this.syncCancel : false,
      sync_status: this.serviceType === 'Provider' ? this.syncStatus : false,
      // limit_from: 0,
      // limit_to: 0,
      // sort: 0,
      labels: this.serviceLabels,

      percent: this.serviceType === 'Provider' ? this.extraPricePercentage : 0,
      percent1: 0,
      percent2: 0,
      add_type: this.serviceType === 'Provider' ? AddType.Api : AddType.Manual, // Set based on service type
      category_id: parseInt(this.selectedCategory?.id || '0'), // Set based on selected category
      api_provider_id: this.serviceType === 'Provider' && this.selectedProvider ? this.selectedProvider.id : null,
      average_time: 0,
      // icon: this.iconType,
      refill: this.refill,
      // Add the additional fields
      is_overflow: this.overflow,
      overflow: this.overflow ? this.overflowValue : 0,
      speed_per_day: this.speedPerDay ? parseInt(this.speedPerDay) : null,
      refill_days: this.refill ? this.refillDays.toString() : null,
      sample_link: this.linkSample,
      is_fixed_price: this.serviceType === 'Manual' ? true : this.fixedPrice,
      cancel_button: this.cancelButton
    };

    // Check if we're editing an existing service or creating a new one
    // Get the service from UIStateService as a fallback
    const serviceFromUIState = this.uiStateService.selectedServiceForAction;

    // Use either the local serviceToEdit or the one from UIStateService
    const serviceToEdit = this.serviceToEdit || serviceFromUIState;
    const isEditMode = serviceToEdit !== null && serviceToEdit !== undefined;

    console.log('Is edit mode:', isEditMode);
    console.log('serviceToEdit:', this.serviceToEdit);
    console.log('serviceFromUIState:', serviceFromUIState);

    if (isEditMode && serviceToEdit) {
      console.log('Updating existing service with ID:', serviceToEdit.id);

      // Update existing service
      this.adminService.updateService(serviceToEdit.id, serviceData).subscribe({
        next: (updatedService) => {
          console.log('Service updated successfully:', updatedService);
          this.isSubmitting = false;
          this.toastService.showSuccess('Service updated successfully');
          this.closePopup();
          // Emit the serviceUpdated event with the updated service
          this.serviceUpdated.emit(updatedService);
        },
        error: (error) => {
          console.error('Error updating service:', error);
          this.isSubmitting = false;
          this.toastService.showError('Error updating service: ' + (error.message || 'Unknown error'));
        }
      });
    } else {
      console.log('Creating new service');

      // Create new service
      this.adminService.createService(serviceData).subscribe({
        next: (createdService) => {
          console.log('Service created successfully:', createdService);
          this.isSubmitting = false;
          this.toastService.showSuccess('Service created successfully');
          this.closePopup();
          // Emit the serviceAdded event with the new service
          this.serviceAdded.emit(createdService);
        },
        error: (error) => {
          console.error('Error creating service:', error);
          this.isSubmitting = false;
          this.toastService.showError('Error creating service: ' + (error.message || 'Unknown error'));
        }
      });
    }
  }

  onChangeClick() {
    console.log('Change button clicked - closing new-service and opening resources');
    // Close this popup and emit event to open resources component
    this.closePopup();
    this.openResources.emit();
  }

  onMinChange(event: any) {
    this.minValue = event.target.value;
  }

  onMaxChange(event: any) {
    this.maxValue = event.target.value;
  }

  onProviderPriceChange() {
    // Force recalculation when user manually changes the provider price
    // even in edit mode, as this is an explicit user action
    this.forceCalculateFinalPrice();
  }

  onExtraPriceChange() {
    // Force recalculation when user manually changes the extra price percentage
    // even in edit mode, as this is an explicit user action
    this.forceCalculateFinalPrice();
  }

  forceCalculateFinalPrice() {
    // Calculate the final price based on provider price and extra percentage
    // This method bypasses the edit mode check for explicit user actions
    this.finalPrice = this.providerPrice * (1 + this.extraPricePercentage / 100);
    console.log('Force calculated final price:', this.finalPrice);
  }

  calculateFinalPrice() {
    // In edit mode, don't recalculate the price automatically to preserve the existing price
    if (this.isEditMode) {
      console.log('Edit mode: Skipping automatic price recalculation to preserve existing price');
      return;
    }

    // Calculate the final price based on provider price and extra percentage
    this.finalPrice = this.providerPrice * (1 + this.extraPricePercentage / 100);
    // Keep full precision for display with toFixed(6) in the template
    // No rounding needed here as we'll display with 6 decimal places
  }

  toggleFixedPrice() {
    this.fixedPrice = !this.fixedPrice;
  }

  onProviderSelected(providerOption: IconBaseModel) {
    // Find the provider object that matches the selected option
    const provider = this.providers.find(p => p.id.toString() === providerOption.id);
    if (provider) {
      this.selectedProvider = provider;
      console.log('Selected provider:', provider);

      // Load services for this provider
      this.loadServicesForProvider(provider.id);
    }
  }

  onServiceIdSelected(serviceIdOption: IconBaseModel) {
    console.log('Service selected manually:', serviceIdOption);
    this.selectedServiceId = serviceIdOption;
    this.serviceId = parseInt(serviceIdOption.id);

    // If the selection was made from a filtered list, keep the search query
    // but reset the filtered options to show all options when the dropdown is opened again
    if (this.serviceSearchQuery) {
      console.log('Selection made with active search query:', this.serviceSearchQuery);
    } else {
      // If no search query, ensure filtered options match all options
      this.filteredServiceIdOptions = [...this.serviceIdOptions];
    }

    // Reset dropdown state
    this.isDropdownOpen = false;

    // Update service fields based on the selected service
    // Use setTimeout to ensure changes happen after Angular's change detection cycle
    setTimeout(() => {
      this.updateServiceFields(serviceIdOption);
    });

    console.log('Selected service ID:', serviceIdOption);
  }

  onServiceTypeSelected(serviceType: string) {
    this.serviceTypeOption = serviceType;
    console.log('Selected service type:', serviceType);
  }

  onCategorySelected(category: IconBaseModel) {
    this.selectedCategory = category;
    this.category = category.label;
    console.log('Selected category:', category);
  }

  toggleUserFriendlyDescription() {
    this.userFriendlyDescription = !this.userFriendlyDescription;
  }

  toggleAdditionalOptions() {
    this.additionalOptions = !this.additionalOptions;
  }

  // Service label methods
  addServiceLabel() {
    if (this.newLabelText.trim()) {
      // Check if label already exists
      const labelExists = this.serviceLabels.some(
        label => label.text.toLowerCase() === this.newLabelText.trim().toLowerCase()
      );

      if (!labelExists) {
        this.serviceLabels.push({
          text: this.newLabelText.trim(),
          type: this.selectedLabelType
        });
        this.newLabelText = '';
      }
    }
  }

  addPredefinedLabel(label: {text: string, type: 'Green' | 'Red'}) {
    // Check if label already exists
    const labelExists = this.serviceLabels.some(
      existingLabel => existingLabel.text.toLowerCase() === label.text.toLowerCase()
    );

    if (!labelExists) {
      this.serviceLabels.push({
        text: label.text,
        type: label.type
      });
    }
  }

  removeServiceLabel(index: number) {
    this.serviceLabels.splice(index, 1);
  }

  setLabelType(type: 'Green' | 'Red') {
    this.selectedLabelType = type;
  }

  // Method to handle service name changes
  onServiceNameChange(newName: string) {
    // Use setTimeout to ensure this happens after Angular's change detection cycle
    setTimeout(() => {
      this.serviceName = newName;
    });
  }

  /**
   * Toggles the global sync setting
   * When enabled, all individual sync options will be enabled
   * When disabled, all individual sync options will be disabled
   */
  toggleSyncAll(value: boolean): void {
    this.syncAll = value;
    // Update all individual sync options
    this.syncMinMax = value;
    this.syncRefill = value;
    this.syncCancel = value;
    this.syncStatus = value;

    // Open additional options section if it's closed
    if (!this.additionalOptions) {
      this.additionalOptions = true;
    }

    // If enabling sync and we have a provider service selected, update all values
    if (value && this.serviceType === 'Provider' && this.selectedProvider && this.serviceId) {
      this.updateFieldsFromProvider();
    }
  }

  /**
   * Updates service fields from provider based on enabled sync options
   * This method fetches the latest provider service data and updates the corresponding fields
   */
  private updateFieldsFromProvider(): void {
    if (!this.selectedProvider || !this.serviceId) {
      console.warn('Cannot update fields from provider: No provider or service ID selected');
      return;
    }

    // Get the provider services
    this.adminService.getProviderServices(this.selectedProvider.id).subscribe({
      next: (services: SMMServiceRes[]) => {
        // Find the selected service in the provider's services
        const providerService = services.find(s => s.service === this.serviceId);

        if (!providerService) {
          console.warn(`Provider service with ID ${this.serviceId} not found`);
          return;
        }

        console.log('Updating fields from provider service:', providerService);

        // Update min/max if sync is enabled
        if (this.syncMinMax) {
          this.minValue = providerService.min;
          this.maxValue = providerService.max;
          console.log(`Updated min/max values: min=${this.minValue}, max=${this.maxValue}`);
        }

        // Update refill if sync is enabled
        if (this.syncRefill && providerService.refill !== undefined) {
          this.refill = providerService.refill;
          console.log(`Updated refill value: ${this.refill}`);
        }

        // Update cancel button if sync is enabled
        if (this.syncCancel && providerService.cancel !== undefined) {
          this.cancelButton = providerService.cancel;
          console.log(`Updated cancel button value: ${this.cancelButton}`);
        }

        // Status sync is handled by the backend scheduler
      },
      error: (error) => {
        console.error('Error fetching provider services:', error);
        this.toastService.showError('Failed to fetch provider services');
      }
    });
  }

  /**
   * Toggles the min-max sync setting
   */
  toggleSyncMinMax(value: boolean): void {
    this.syncMinMax = value;
    // Update syncAll based on individual sync states
    this.updateSyncAllState();

    // If enabling sync and we have a provider service selected, update min/max values
    if (value && this.serviceType === 'Provider' && this.selectedProvider && this.serviceId) {
      this.updateFieldsFromProvider();
    }
  }

  /**
   * Toggles the refill sync setting
   */
  toggleSyncRefill(value: boolean): void {
    this.syncRefill = value;
    // Update syncAll based on individual sync states
    this.updateSyncAllState();

    // If enabling sync and we have a provider service selected, update refill value
    if (value && this.serviceType === 'Provider' && this.selectedProvider && this.serviceId) {
      this.updateFieldsFromProvider();
    }
  }

  /**
   * Toggles the cancel sync setting
   */
  toggleSyncCancel(value: boolean): void {
    this.syncCancel = value;
    // Update syncAll based on individual sync states
    this.updateSyncAllState();

    // If enabling sync and we have a provider service selected, update cancel value
    if (value && this.serviceType === 'Provider' && this.selectedProvider && this.serviceId) {
      this.updateFieldsFromProvider();
    }
  }

  /**
   * Handles checkbox change for refill
   */
  onRefillChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.refill = target.checked;
  }

  /**
   * Handles checkbox change for cancel button
   */
  onCancelButtonChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.cancelButton = target.checked;
  }

  /**
   * Handles checkbox change for overflow
   */
  onOverflowChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.overflow = target.checked;
  }

  /**
   * Toggles the service status sync setting
   */
  toggleSyncStatus(value: boolean): void {
    this.syncStatus = value;
    // Update syncAll based on individual sync states
    this.updateSyncAllState();

    // If enabling sync and we have a provider service selected, update status value
    if (value && this.serviceType === 'Provider' && this.selectedProvider && this.serviceId) {
      this.updateFieldsFromProvider();
    }
  }

  /**
   * Updates the syncAll state based on individual sync states
   * If all individual syncs are enabled, syncAll should be enabled
   * If any individual sync is disabled, syncAll should be disabled
   */
  private updateSyncAllState(): void {
    this.syncAll = this.syncMinMax && this.syncRefill && this.syncCancel && this.syncStatus;
  }

  /**
   * Format balance using the common currency service
   */
  formatBalance(balance: number | undefined): string {
    return this.currencyService.formatBalance(balance);
  }
}
