.simple-search-container {
  width: 100%;
  max-width: 100%;
}

.simple-search-wrapper {
  display: flex;
  align-items: stretch;
  background: #ffffff;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  height: 46px;
}

.simple-search-wrapper:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.simple-search-input {
  flex: 1;
  padding: 12px 16px;
  border: none;
  outline: none;
  font-size: 14px;
  color: #374151;
  background: transparent;
  height: 100%;
  box-sizing: border-box;
}

.simple-search-input::placeholder {
  color: #9ca3af;
}

.simple-search-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 12px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
  min-width: 100px;
  height: 100%;
  box-sizing: border-box;
}

.simple-search-button:hover {
  background: #2563eb;
}

.simple-search-button:active {
  background: #1d4ed8;
}

.simple-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.simple-button-text {
  white-space: nowrap;
}

/* Responsive */
@media (max-width: 640px) {
  .simple-search-container {
    max-width: 100%;
  }

  .simple-search-wrapper {
    height: 44px;
  }

  .simple-search-input {
    font-size: 16px; /* Prevent zoom on iOS */
    padding: 10px 14px;
  }

  .simple-search-button {
    min-width: 80px;
    padding: 10px 12px;
  }

  .simple-button-text {
    font-size: 12px;
  }
}

/* Custom class overrides */
.simple-search-input.bg-white {
  background: #ffffff;
}

.simple-search-button.bg-cyan-500 {
  background: #06b6d4;
}

.simple-search-button.bg-cyan-500:hover {
  background: #0891b2;
}

.simple-search-button.text-white {
  color: #ffffff;
}

.simple-search-button.font-medium {
  font-weight: 500;
}
