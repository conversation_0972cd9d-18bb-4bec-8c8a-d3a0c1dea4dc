import { Compo<PERSON>, On<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Inject, PLATFORM_ID, HostListener } from '@angular/core';
import { FooterComponent } from "./footer/footer.component";
import { HeaderComponent } from "./header/header.component";
import { RouterModule, Router } from '@angular/router';
import { AuthUtilsService } from '../../core/services/auth-utils.service';
import { isPlatformBrowser, CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { IconsModule } from '../../icons/icons.module';
import { AppAssetsService } from '../../core/services/app-assets.service';
import { LanguageService } from '../../core/services/language.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-layout-auth',
  standalone: true,
  imports: [CommonModule, RouterModule, TranslateModule, IconsModule],
  templateUrl: './layout-auth.component.html',
  styleUrl: './layout-auth.component.css'
})
export class LayoutAuthComponent implements OnInit, OnDestroy {
  // Language switcher
  currentLanguage = 'en';
  showLanguageDropdown = false;
  languages = [
    { code: 'en', name: 'English' },
    { code: 'vi', name: 'Tiếng Việt' }
  ];

  // Logo
  logoUrl: string = 'assets/images/logo.png';
  private subscription = new Subscription();

  constructor(
    private authUtils: AuthUtilsService,
    private router: Router,
    private translateService: TranslateService,
    private languageService: LanguageService,
    @Inject(PLATFORM_ID) private platformId: Object,
   // private appAssetsService: AppAssetsService
  ) {}

  ngOnInit(): void {
    // Initialize language settings
    this.initializeLanguage();

    // Check if user is already authenticated
    if (isPlatformBrowser(this.platformId)) {
      if (this.authUtils.isAuthenticated() && !this.authUtils.isTokenExpired()) {
        console.log('LayoutAuth Component - User is already authenticated, redirecting');
        const redirectUrl = localStorage.getItem('redirectAfterLogin') || '/';
        this.router.navigate([redirectUrl]);
      }
    }

    // Subscribe to language changes from LanguageService
    this.subscription.add(
      this.languageService.translationsLoaded$.subscribe(loaded => {
        if (loaded && isPlatformBrowser(this.platformId)) {
          // Update current language when translations are loaded
          const currentLang = this.translateService.currentLang || localStorage.getItem('language') || 'en';
          this.currentLanguage = currentLang;
          console.log('LayoutAuth: Language updated from service:', currentLang);
        }
      })
    );

    // Subscribe to logo URL changes
    // this.subscription.add(
    //   this.appAssetsService.logoUrl$.subscribe(url => {
    //     this.logoUrl = url;
    //   })
    // );
  }

  private initializeLanguage(): void {
    if (isPlatformBrowser(this.platformId)) {
      // Get the current language from localStorage or TranslateService
      const savedLang = localStorage.getItem('language');
      const currentLang = this.translateService.currentLang;

      // Use the language that's already set by LanguageService or fall back to saved/default
      this.currentLanguage = currentLang || savedLang || 'en';

      console.log('LayoutAuth: Language initialized:', this.currentLanguage);
    } else {
      // Server-side rendering fallback
      this.currentLanguage = 'en';
    }
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  // Toggle language dropdown
  toggleLanguageDropdown(): void {
    this.showLanguageDropdown = !this.showLanguageDropdown;
  }

  // Get current language display name
  getCurrentLanguageName(): string {
    const lang = this.languages.find(l => l.code === this.currentLanguage);
    return lang ? lang.name : 'English';
  }

  // Get current language flag class
  getCurrentLanguageFlag(): string {
    return this.currentLanguage === 'en' ? 'fi fi-us' : 'fi fi-vn';
  }

  // Change language
  changeLanguage(langCode: string): void {
    if (isPlatformBrowser(this.platformId)) {
      // Validate language code
      if (!['en', 'vi'].includes(langCode)) {
        console.warn('Invalid language code:', langCode);
        return;
      }

      // Update current language immediately for UI
      this.currentLanguage = langCode;

      // Use LanguageService to change language (this handles localStorage and TranslateService)
      this.languageService.changeLanguage(langCode);

      console.log('LayoutAuth: Language changed to:', langCode);
    }

    // Close dropdown
    this.showLanguageDropdown = false;
  }

  // Close language dropdown when clicking outside
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    if (!target.closest('.relative')) {
      this.showLanguageDropdown = false;
    }
  }


    // Navigate to login page
  navigateToLogin() {
    this.router.navigate(['/auth/login']);
  }

  // Navigate to register page
  navigateToRegister() {
    this.router.navigate(['/auth/register']);
  }

  // Navigate to services page
  navigateToServices() {
    this.router.navigate(['/panel']);
  }

  // Navigate to dashboard
  navigateToDashboard() {
    this.router.navigate(['/panel']);
  }
}
