import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { RouterModule, Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { ToastService } from '../../../core/services/toast.service';
import { GeneralSettingsService } from '../../../core/services/general-settings.service';
import { GeneralSettingsRes } from '../../../model/response/general-settings-res.model';
import { AffiliateSystemComponent } from '../../popup/affiliate-system/affiliate-system.component';
import { IconsModule } from '../../../icons/icons.module';

@Component({
  selector: 'app-general',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    TranslateModule,
    IconsModule,
    AffiliateSystemComponent
  ],
  templateUrl: './general.component.html',
  styleUrl: './general.component.css'
})
export class GeneralComponent implements OnInit {
  generalForm!: FormGroup;
  isLoading: boolean = false;
  showAffiliateSystemPopup: boolean = false;
  showLanguageSettingsPopup: boolean = false;
  showCurrencySettingsPopup: boolean = false;


  constructor(
    private fb: FormBuilder,
    private toastService: ToastService,
    private generalSettingsService: GeneralSettingsService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadGeneralSettings();
  }

  /**
   * Initialize the form with default values
   */
  initForm(): void {
    this.generalForm = this.fb.group({
      // We've removed the site information fields

      // Registration settings
      allow_registration: [true],
      require_email_verification: [true],

      // Security settings
      enable_captcha: [true],
      enable_2fa: [false],

      // Order settings
      min_order_amount: [1000, [Validators.required, Validators.min(0)]],
      max_order_amount: [1000000, [Validators.required, Validators.min(0)]],

      // User settings
      default_user_balance: [0, [Validators.required, Validators.min(0)]],

      // Maintenance settings
      maintenance_mode: [false],
      maintenance_message: ['']
    });
  }

  /**
   * Load general settings from the server
   */
  loadGeneralSettings(): void {
    this.isLoading = true;
    this.generalSettingsService.getGeneralSettings().subscribe({
      next: (settings) => {
        this.updateFormFromSettings(settings);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading general settings:', error);
        this.toastService.showError('Failed to load general settings');
        this.isLoading = false;
      }
    });
  }

  /**
   * Update form values from settings
   */
  updateFormFromSettings(settings: GeneralSettingsRes): void {
    this.generalForm.patchValue({
      // Site information fields removed
      allow_registration: settings.allow_registration,
      require_email_verification: settings.require_email_verification,
      enable_captcha: settings.enable_captcha,
      enable_2fa: settings.enable_2fa,
      min_order_amount: settings.min_order_amount,
      max_order_amount: settings.max_order_amount,
      default_user_balance: settings.default_user_balance,
      maintenance_mode: settings.maintenance_mode,
      maintenance_message: settings.maintenance_message
    });
  }

  /**
   * Open the affiliate system popup
   */
  openAffiliateSystemPopup(): void {
    this.showAffiliateSystemPopup = true;
  }

  /**
   * Close the affiliate system popup
   */
  closeAffiliateSystemPopup(): void {
    this.showAffiliateSystemPopup = false;
  }

  /**
   * Navigate to I18n Management page
   */
  openLanguageSettingsPopup(): void {
    this.router.navigate(['/panel/settings/i18n']);
  }

  /**
   * Close the language settings popup (deprecated - kept for compatibility)
   */
  closeLanguageSettingsPopup(): void {
    this.showLanguageSettingsPopup = false;
  }

  /**
   * Navigate to Currency Settings page
   */
  openCurrencySettingsPopup(): void {
    this.router.navigate(['/panel/settings/currency']);
  }

  /**
   * Close the currency settings popup
   */
  closeCurrencySettingsPopup(): void {
    this.showCurrencySettingsPopup = false;
  }

}
