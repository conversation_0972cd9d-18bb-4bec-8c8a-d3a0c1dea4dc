import { Component, Input, Output, EventEmitter, OnInit, On<PERSON><PERSON>roy, inject } from '@angular/core';
import { SearchBoxLogicService, SearchBoxState } from './search-box.service';
import { Observable, Subject, takeUntil } from 'rxjs';

@Component({
  template: ''
})
export class SearchBoxBaseComponent implements OnInit, OnDestroy {
  protected searchBoxService = inject(SearchBoxLogicService);
  private destroy$ = new Subject<void>();

  @Input() placeholder: string = 'Tìm kiếm';
  @Input() buttonText: string = 'Tìm kiếm';
  @Input() buttonIcon: 'search' | 'edit' | 'none' = 'none';
  @Input() buttonPosition: 'left' | 'right' = 'right';
  @Input() showButtonText: boolean = true;
  
  // CSS customization inputs
  @Input() containerClass: string = '';
  @Input() inputClass: string = 'bg-white';
  @Input() buttonClass: string = 'bg-cyan-500 text-white font-medium';

  @Output() searchEvent = new EventEmitter<string>();

  state$: Observable<SearchBoxState> = this.searchBoxService.state$;

  ngOnInit(): void {
    // Initialize service with input values
    this.searchBoxService.updateState({
      placeholder: this.placeholder,
      buttonText: this.buttonText,
      buttonIcon: this.buttonIcon,
      buttonPosition: this.buttonPosition,
      showButtonText: this.showButtonText,
      containerClass: this.containerClass,
      inputClass: this.inputClass,
      buttonClass: this.buttonClass
    });

    // Subscribe to state changes
    this.state$.pipe(takeUntil(this.destroy$)).subscribe(state => {
      this.placeholder = state.placeholder;
      this.buttonText = state.buttonText;
      this.buttonIcon = state.buttonIcon;
      this.buttonPosition = state.buttonPosition;
      this.showButtonText = state.showButtonText;
      this.containerClass = state.containerClass;
      this.inputClass = state.inputClass;
      this.buttonClass = state.buttonClass;
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  searchValue: string = '';

  updateSearchValue(value: string): void {
    this.searchValue = value;
    this.searchBoxService.updateSearchValue(value);
  }

  search(): void {
    this.searchEvent.emit(this.searchValue);
  }

  onSearchValueChange(value: string): void {
    this.searchBoxService.updateSearchValue(value);
  }

  clearSearch(): void {
    this.searchBoxService.clearSearch();
  }
}
