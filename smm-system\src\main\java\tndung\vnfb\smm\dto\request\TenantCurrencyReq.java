package tndung.vnfb.smm.dto.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class TenantCurrencyReq {

    @NotEmpty(message = "Available currencies cannot be empty")
    private List<String> availableCurrencies;

    // Map of currency code to sync enabled status
    private Map<String, Boolean> currencySyncSettings;

    // Map of currency code to payment sync enabled status
    private Map<String, Boolean> paymentSyncSettings;

    // Map of currency code to custom exchange rate (for manual updates when sync is disabled)
    private Map<String, BigDecimal> customRates;
}
