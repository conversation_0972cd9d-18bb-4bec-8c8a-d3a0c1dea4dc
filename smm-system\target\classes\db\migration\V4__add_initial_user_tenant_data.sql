-- Add initial data to user_tenant table for existing admin users
-- This script grants admin users access to all tenants

-- First, let's create a temporary table to store tenant IDs
CREATE TEMPORARY TABLE temp_tenants (tenant_id VARCHAR(255));

-- Insert tenant IDs into the temporary table
-- We're using the tenant_id values from the existing tenant table
INSERT INTO temp_tenants (tenant_id)
SELECT tenant_id FROM tenant WHERE tenant_id IS NOT NULL;

-- If no tenants found, add the default tenant ID
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM temp_tenants) THEN
        -- Try to get the tenant_id for autovnfb.dev
        INSERT INTO temp_tenants (tenant_id)
        SELECT tenant_id FROM tenant WHERE domain = 'autovnfb.dev'
        UNION
        SELECT '0e22c37d-bfb5-4276-bd30-355fcdb39c9e' WHERE NOT EXISTS (
            SELECT 1 FROM tenant WHERE domain = 'autovnfb.dev'
        );
    END IF;
END $$;

-- Grant admin/panel users access to all tenants
INSERT INTO user_tenant (user_id, tenant_id, created_at)
SELECT u.id, t.tenant_id, NOW()
FROM g_user u
CROSS JOIN temp_tenants t
WHERE u.roles::text LIKE '%PANEL%'  -- Using the new PANEL role instead of ADMIN
AND NOT EXISTS (
    SELECT 1 FROM user_tenant ut
    WHERE ut.user_id = u.id AND ut.tenant_id = t.tenant_id
);

-- Drop the temporary table
DROP TABLE temp_tenants;

-- Get the default tenant ID
DO $$
DECLARE
    default_tenant_id VARCHAR;
BEGIN
    -- Try to get the tenant_id for autovnfb.dev
    SELECT tenant_id INTO default_tenant_id FROM tenant WHERE domain = 'autovnfb.dev' LIMIT 1;

    -- If not found, use a default UUID
    IF default_tenant_id IS NULL THEN
        default_tenant_id := '0e22c37d-bfb5-4276-bd30-355fcdb39c9e';
    END IF;

    -- Add the default tenant for all users who don't have any tenant assigned
    INSERT INTO user_tenant (user_id, tenant_id, created_at)
    SELECT id, default_tenant_id, NOW()
    FROM g_user u
    WHERE NOT EXISTS (
        SELECT 1 FROM user_tenant ut
        WHERE ut.user_id = u.id
    );

    -- Update the tenant_id in the g_user table to match the first tenant assigned to each user
    UPDATE g_user u
    SET tenant_id = (
        SELECT tenant_id
        FROM user_tenant ut
        WHERE ut.user_id = u.id
        ORDER BY ut.created_at
        LIMIT 1
    )
    WHERE tenant_id IS NULL;
END $$;
