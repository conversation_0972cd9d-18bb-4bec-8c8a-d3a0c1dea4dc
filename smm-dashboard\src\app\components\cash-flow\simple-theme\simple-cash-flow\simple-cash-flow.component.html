<!-- Simple Cash Flow Theme - Clean & Minimal Design -->
<div class="simple-cash-flow-container" *ngIf="cashFlowState$ | async as cashFlowState">

  <!-- Header Section -->
  <div class="page-header">
    <div class="header-content">
      <div class="title-section">
        <h1 class="page-title">{{ 'simple_theme.cash_flow.title' | translate }}</h1>
        <p class="page-subtitle">{{ 'simple_theme.cash_flow.subtitle' | translate }}</p>
      </div>
      <div class="stats-section">
        <div class="stat-item">
          <span class="stat-number">{{ cashFlowState.totalElements }}</span>
          <span class="stat-label">{{ 'simple_theme.cash_flow.total_transactions' | translate }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Filter Section -->
  <div class="filter-container">
    <form [formGroup]="cashFlowState.filterForm" (ngSubmit)="onSearch()" class="filter-form">
      <div class="filter-row">

        <!-- Date Range Filter -->
        <div class="filter-group">
          <label class="filter-label">{{ 'date_range' | translate }}</label>
          <app-date-range-picker
            [containerClass]="'date-picker-input'"
            formControlName="dateRange"
            (dateRangeChanged)="onDateRangeChanged($event)">
          </app-date-range-picker>
        </div>

        <!-- Transaction Type Filter -->
        <div class="filter-group">
          <label class="filter-label">{{ 'transaction_type' | translate }}</label>
          <app-lite-dropdown
            [options]="cashFlowState.transactionTypeOptions"
            [selectedOption]="cashFlowState.filterForm.get('type')?.value"
            (selected)="onTransactionTypeSelected($event)"
            [customClassDropdown]="'dropdown-select'"
            [customClassButton]="'dropdown-btn'">
          </app-lite-dropdown>
        </div>

        <!-- Status Filter -->
        <div class="filter-group">
          <label class="filter-label">{{ 'status' | translate }}</label>
          <app-lite-dropdown
            [options]="cashFlowState.statusOptions"
            [selectedOption]="cashFlowState.filterForm.get('status')?.value"
            (selected)="onStatusSelected($event)"
            [customClassDropdown]="'dropdown-select'"
            [customClassButton]="'dropdown-btn'">
          </app-lite-dropdown>
        </div>

        <!-- Order ID Filter -->
        <div class="filter-group">
          <label class="filter-label">{{ 'order_id' | translate }}</label>
          <input
            type="text"
            formControlName="orderId"
            class="text-input"
            [placeholder]="'order_id_placeholder' | translate">
        </div>

        <!-- Action Buttons -->
        <div class="filter-group filter-actions">
          <button
            type="button"
            class="btn btn-secondary"
            (click)="resetFilters()"
            [disabled]="cashFlowState.loading">
            <i class="fas fa-undo"></i>
            {{ 'reset' | translate }}
          </button>
          <button
            type="submit"
            class="btn btn-primary"
            [disabled]="cashFlowState.loading">
            <i class="fas fa-search" *ngIf="!cashFlowState.loading"></i>
            <i class="fas fa-spinner fa-spin" *ngIf="cashFlowState.loading"></i>
            {{ 'search' | translate }}
          </button>
        </div>

      </div>
    </form>
  </div>

  <!-- Content Section -->
  <div class="content-wrapper">

    <!-- Loading State -->
    <div *ngIf="cashFlowState.loading" class="loading-container">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
      <p class="loading-text">{{ 'loading_transactions' | translate }}</p>
    </div>

    <!-- Transactions Content -->
    <div *ngIf="!cashFlowState.loading" class="transactions-content">

      <!-- Selection Bar -->
      <div class="selection-bar" *ngIf="cashFlowState.transactions.length > 0">
        <label class="checkbox-wrapper">
          <input
            type="checkbox"
            [checked]="cashFlowState.selectAll"
            (change)="toggleAllTransactions()">
          <span class="checkbox-custom"></span>
          <span class="checkbox-label">{{ 'select_all' | translate }}</span>
        </label>
        <div class="selection-info" *ngIf="cashFlowState.selectedTransactions.length > 0">
          {{ cashFlowState.selectedTransactions.length }} {{ 'selected' | translate }}
        </div>
      </div>

      <!-- Desktop Table -->
      <div class="table-container desktop-view" *ngIf="cashFlowState.transactions.length > 0">
        <table class="data-table">
          <thead>
            <tr>
              <th class="col-checkbox"></th>
              <th class="col-number">{{ 'stt' | translate }}</th>
              <th class="col-link">{{ 'link' | translate }}</th>
              <th class="col-amount">{{ 'total_amount' | translate }}</th>
              <th class="col-balance">{{ 'cash_flow' | translate }}</th>
              <th class="col-description">{{ 'description' | translate }}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let transaction of cashFlowState.transactions; let i = index" class="table-row">
              <td class="col-checkbox">
                <label class="checkbox-wrapper">
                  <input
                    type="checkbox"
                    [checked]="isSelected(transaction.id)"
                    (change)="toggleTransaction(transaction.id)">
                  <span class="checkbox-custom"></span>
                </label>
              </td>
              <td class="col-number">{{ cashFlowState.currentPage * cashFlowState.pageSize + i + 1 }}</td>
              <td class="col-link">
                <app-transaction-label [transaction]="transaction"></app-transaction-label>
              </td>
              <td class="col-amount">
                <span class="amount-value" [ngClass]="getChangeClass(transaction.change)">
                  {{ transaction.change | currencyConvert }}
                </span>
              </td>
              <td class="col-balance">
                <div class="balance-flow">
                  <span class="balance-from" [ngClass]="getChangeClass(getBalanceComponents(transaction).previous)">
                    {{ getBalanceComponents(transaction).previous | currencyConvert }}
                  </span>
                  <i class="fas fa-arrow-right flow-arrow"></i>
                  <span class="balance-to" [ngClass]="getChangeClass(getBalanceComponents(transaction).current)">
                    {{ getBalanceComponents(transaction).current | currencyConvert }}
                  </span>
                </div>
              </td>
              <td class="col-description">
                <span class="description-text">{{ 'N/A' }}</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Mobile Cards -->
      <div class="cards-container mobile-view" *ngIf="cashFlowState.transactions.length > 0">
        <div *ngFor="let transaction of cashFlowState.transactions; let i = index" class="transaction-card">
          <div class="card-header">
            <label class="checkbox-wrapper">
              <input
                type="checkbox"
                [checked]="isSelected(transaction.id)"
                (change)="toggleTransaction(transaction.id)">
              <span class="checkbox-custom"></span>
            </label>
            <span class="card-number">
              #{{ cashFlowState.currentPage * cashFlowState.pageSize + i + 1 }}
            </span>
            <span class="card-amount" [ngClass]="getChangeClass(transaction.change)">
              {{ transaction.change | currencyConvert }}
            </span>
          </div>

          <div class="card-content">
            <div class="card-row">
              <span class="row-label">{{ 'link' | translate }}</span>
              <div class="row-value">
                <app-transaction-label [transaction]="transaction"></app-transaction-label>
              </div>
            </div>

            <div class="card-row">
              <span class="row-label">{{ 'cash_flow' | translate }}</span>
              <div class="row-value">
                <div class="balance-flow mobile">
                  <span class="balance-from" [ngClass]="getChangeClass(getBalanceComponents(transaction).previous)">
                    {{ getBalanceComponents(transaction).previous | currencyConvert }}
                  </span>
                  <i class="fas fa-arrow-right flow-arrow"></i>
                  <span class="balance-to" [ngClass]="getChangeClass(getBalanceComponents(transaction).current)">
                    {{ getBalanceComponents(transaction).current | currencyConvert }}
                  </span>
                </div>
              </div>
            </div>

            <div class="card-row">
              <span class="row-label">{{ 'description' | translate }}</span>
              <span class="row-value">{{ 'N/A' }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="cashFlowState.transactions.length === 0" class="empty-state">
        <div class="empty-icon">
          <i class="fas fa-receipt"></i>
        </div>
        <h3 class="empty-title">{{ 'no_transactions_found' | translate }}</h3>
        <p class="empty-description">{{ 'no_transactions_description' | translate }}</p>
      </div>

    </div>
  </div>

  <!-- Pagination -->
  <div class="pagination-wrapper" *ngIf="!cashFlowState.loading && cashFlowState.transactions.length > 0">
    <div class="pagination-info">
      <span>{{ 'showing' | translate }} {{ cashFlowState.currentPage * cashFlowState.pageSize + 1 }} -
        {{ Math.min((cashFlowState.currentPage + 1) * cashFlowState.pageSize, cashFlowState.totalElements) }}
        {{ 'of' | translate }} {{ cashFlowState.totalElements }} {{ 'transactions' | translate }}</span>
    </div>

    <div class="pagination-controls">
      <button
        class="page-btn prev"
        [disabled]="cashFlowState.currentPage === 0"
        (click)="onPageChange(cashFlowState.currentPage - 1)">
        <i class="fas fa-chevron-left"></i>
      </button>

      <div class="page-numbers">
        <button
          *ngFor="let page of getPageNumbers()"
          class="page-btn"
          [class.active]="page === cashFlowState.currentPage"
          (click)="onPageChange(page)">
          {{ page + 1 }}
        </button>
      </div>

      <button
        class="page-btn next"
        [disabled]="cashFlowState.currentPage >= cashFlowState.totalPages - 1"
        (click)="onPageChange(cashFlowState.currentPage + 1)">
        <i class="fas fa-chevron-right"></i>
      </button>
    </div>

    <div class="page-size-selector">
      <span class="selector-label">{{ 'show' | translate }}</span>
      <select
        [value]="cashFlowState.pageSize"
        (change)="onPageSizeChange(+($any($event.target).value))"
        class="page-size-select">
        <option value="10">10</option>
        <option value="25">25</option>
        <option value="50">50</option>
        <option value="100">100</option>
      </select>
      <span class="selector-label">{{ 'per_page' | translate }}</span>
    </div>
  </div>

</div>
