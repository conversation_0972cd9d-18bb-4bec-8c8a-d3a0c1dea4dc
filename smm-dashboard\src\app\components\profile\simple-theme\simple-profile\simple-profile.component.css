/* Simple Profile Theme - Clean & Minimal Design */
.simple-profile-container {
  @apply p-6 bg-slate-50 min-h-screen;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Profile Header */
.profile-header {
  @apply bg-white rounded-xl p-6 mb-6 shadow-sm border border-slate-200;
}

.header-content {
  @apply flex items-center justify-between flex-wrap gap-6;
}

.avatar-section {
  @apply relative;
}

.avatar-wrapper {
  @apply relative;
}

.avatar-image {
  @apply w-20 h-20 rounded-full object-cover border-2 border-slate-200;
}

.avatar-overlay {
  @apply absolute bottom-0 right-0 w-6 h-6 bg-blue-400 rounded-full;
  @apply flex items-center justify-center text-white cursor-pointer;
  @apply border-2 border-white transition-colors duration-200 text-xs;
}

.avatar-overlay:hover {
  @apply bg-blue-600;
}

.user-info {
  @apply flex-1 min-w-[200px];
}

.user-name {
  @apply text-2xl font-semibold text-gray-900 m-0 mb-1;
}

.user-email {
  @apply text-gray-500 text-sm m-0 mb-3;
}

.status-indicator {
  @apply flex items-center gap-2 text-emerald-500 font-medium text-sm;
}

.status-dot {
  @apply w-2 h-2 bg-emerald-500 rounded-full animate-pulse;
}

.status-text {
  @apply text-sm;
}

.balance-info {
  @apply bg-blue-400 text-white px-5 py-4 rounded-lg text-center min-w-[120px];
}

.balance-amount {
  @apply text-xl font-semibold mb-1;
}

.balance-label {
  @apply text-xs opacity-90;
}

/* Tab Navigation */
.tabs-container {
  @apply bg-white rounded-xl p-2 mb-6 shadow-sm border border-slate-200;
}

.tab-list {
  @apply flex gap-1 overflow-x-auto;
}

.tab-button {
  @apply flex-1 flex items-center justify-center gap-2 px-4 py-3;
  @apply border-0 bg-transparent text-gray-500 rounded-lg font-medium;
  @apply transition-all duration-200 cursor-pointer whitespace-nowrap;
  @apply min-w-[100px] text-sm;
}

.tab-button:hover {
  @apply bg-gray-50 text-blue-400;
}

.tab-button.active {
  @apply bg-blue-400 text-white;
}

.tab-button i {
  @apply text-base;
}

/* Tab Content */
.tab-content {
  @apply bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden;
}

.tab-panel {
  @apply p-6;
}

.panel-header {
  @apply mb-6;
}

.panel-title {
  @apply text-xl font-semibold text-gray-900 m-0 mb-1;
}

.panel-description {
  @apply text-gray-500 text-sm m-0;
}

/* Form Styles */
.form-wrapper {
  @apply max-w-md mx-auto;
}

.form-field {
  @apply mb-4;
}

.field-label {
  @apply block font-medium text-gray-700 mb-2 text-sm;
}

.field-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md text-sm;
  @apply transition-colors duration-200 bg-white;
}

.field-input:focus {
  @apply outline-none border-blue-400;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.password-field {
  @apply relative;
}

.password-toggle {
  @apply absolute right-3 top-1/2 -translate-y-1/2 bg-transparent border-0;
  @apply text-gray-500 cursor-pointer p-1 rounded transition-colors duration-200;
}

.password-toggle:hover {
  @apply text-blue-400;
}

.submit-button {
  @apply w-full px-4 py-2 bg-blue-400 text-white border-0 rounded-md;
  @apply text-sm font-medium cursor-pointer transition-colors duration-200;
  @apply flex items-center justify-center gap-2;
}

.submit-button:hover:not(:disabled) {
  @apply bg-blue-600;
}

.submit-button:disabled {
  @apply opacity-60 cursor-not-allowed;
}

/* Security Options */
.security-options {
  @apply mt-6 pt-6 border-t border-slate-200;
}

.security-option {
  @apply flex items-center justify-between p-4 bg-gray-50 rounded-lg gap-4;
}

.option-info {
  @apply flex-1;
}

.option-title {
  @apply text-base font-medium text-gray-900 m-0 mb-1;
}

.option-description {
  @apply text-gray-500 m-0 text-sm;
}

.toggle-button {
  @apply px-4 py-2 border border-gray-300 bg-white text-gray-500;
  @apply rounded-md font-medium cursor-pointer transition-all duration-200;
  @apply min-w-[80px] text-sm;
}

.toggle-button.enabled {
  @apply bg-emerald-500 text-white border-emerald-500;
}

/* Settings List */
.settings-list {
  @apply grid gap-4 max-w-md mx-auto;
}

.setting-item {
  @apply flex flex-col gap-2;
}

.setting-label {
  @apply font-medium text-gray-700 text-sm;
}

/* History Content */
.history-content {
  @apply max-w-xl mx-auto;
}

.empty-state,
.loading-state {
  @apply text-center p-8 text-gray-500;
}

.empty-state i,
.loading-state i {
  @apply text-2xl mb-4 opacity-50;
}

.history-item {
  @apply flex items-center justify-between p-4 border-b border-slate-200 gap-4;
}

.history-item:last-child {
  @apply border-b-0;
}

.history-details {
  @apply flex-1;
}

.history-time {
  @apply font-medium text-gray-900 mb-1 text-sm;
}

.history-info {
  @apply text-xs text-gray-500;
}

.history-status {
  @apply flex-shrink-0;
}

.status-badge {
  @apply px-2 py-1 rounded-xl text-xs font-medium;
}

.status-badge.success {
  @apply bg-green-100 text-green-800;
}

/* Responsive Design */
@media (max-width: 768px) {
  .simple-profile-container {
    @apply p-4;
  }

  .profile-header {
    @apply p-4;
  }

  .header-content {
    @apply flex-col text-center gap-4;
  }

  .tab-list {
    @apply flex-col gap-1;
  }

  .tab-button {
    @apply min-w-0;
  }

  .tab-panel {
    @apply p-4;
  }

  .security-option {
    @apply flex-col items-stretch text-center;
  }

  .history-item {
    @apply flex-col items-stretch text-center;
  }
}