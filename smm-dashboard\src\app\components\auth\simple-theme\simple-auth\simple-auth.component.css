/* Simple theme specific styles */
.simple-auth-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Custom focus styles for simple theme */
.simple-input:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  border-color: #3b82f6;
}

/* Simple button hover effects */
.simple-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Animation for error messages */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.error-shake {
  animation: shake 0.3s ease-in-out;
}

/* Simple card styling */
.simple-card {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}
