<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" (click)="onOverlayClick($event)">
  <div class="bg-white rounded-lg w-full max-w-md p-6 shadow-lg">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-xl font-semibold text-gray-800">
        Confirm Delete
      </h2>
      <button class="text-gray-500 hover:text-gray-700" (click)="closePopup()">
        <fa-icon [icon]="['fas', 'times']" class="text-lg"></fa-icon>
      </button>
    </div>

    <!-- Content -->
    <div class="mb-6">
      <p class="text-gray-700">
        Are you sure you want to delete <span class="font-semibold">{{ itemName }}</span>? This action cannot be undone.
      </p>
    </div>

    <!-- Buttons -->
    <div class="flex justify-end gap-3">
      <button
        type="button"
        class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
        [disabled]="isLoading"
        (click)="closePopup()">
        Cancel
      </button>
      <button
        type="button"
        [disabled]="isLoading"
        class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 flex items-center"
        (click)="confirmDelete()">
        <span *ngIf="isLoading" class="mr-2 animate-spin">
          <fa-icon [icon]="['fas', 'spinner']"></fa-icon>
        </span>
        Delete
      </button>
    </div>
  </div>
</div>
