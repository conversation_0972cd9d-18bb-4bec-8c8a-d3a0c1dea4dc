import { Injectable, ViewChild, ElementRef } from '@angular/core';
import { BehaviorSubject, Observable, Subscription, interval, throwError } from 'rxjs';
import { catchError, timeout } from 'rxjs/operators';
import { ActivatedRoute } from '@angular/router';
import { TicketService } from '../../../core/services/ticket.service';
import { TicketRes } from '../../../model/response/ticket-res.model';
import { ThemeService, LayoutTheme } from '../../../core/services/theme.service';

export interface TicketDetailState {
  // Data
  ticketId: number;
  ticket: TicketRes | null;
  firstRepliedBy: string;
  
  // UI State
  isLoading: boolean;
  replyMessage: string;
  
  // Message reset properties
  resetInterval: number;
  timeUntilReset: number;
  
  // API timeout properties
  apiTimeout: number;
  apiTimeoutError: boolean;
  
  // Theme
  currentTheme: LayoutTheme;
}

@Injectable()
export class TicketDetailLogicService {
  private subscriptions: Subscription[] = [];
  private messageResetTimer: any;

  // State management
  private _state$ = new BehaviorSubject<TicketDetailState>({
    ticketId: 0,
    ticket: null,
    firstRepliedBy: '',
    isLoading: false,
    replyMessage: '',
    resetInterval: 10,
    timeUntilReset: 10,
    apiTimeout: 30000,
    apiTimeoutError: false,
    currentTheme: LayoutTheme.DEFAULT
  });

  public readonly state$ = this._state$.asObservable();

  constructor(
    private route: ActivatedRoute,
    private ticketService: TicketService,
    private themeService: ThemeService
  ) {
    // Don't initialize subscriptions in constructor
    // They will be initialized when initialize() is called
  }

  private initializeSubscriptions(): void {
    // Subscribe to route params
    this.subscriptions.push(
      this.route.params.subscribe(params => {
        const ticketId = +params['id'];
        if (ticketId && !isNaN(ticketId)) {
          this.updateState({ ticketId });
          this.loadTicketDetails();
        }
      })
    );

    // Subscribe to loading state
    this.subscriptions.push(
      this.ticketService.loading$.subscribe(isLoading => {
        this.updateState({ isLoading });
      })
    );

    // Subscribe to theme changes
    this.subscriptions.push(
      this.themeService.currentLayoutTheme$.subscribe(currentTheme => {
        this.updateState({ currentTheme });
      })
    );

    // Set up countdown timer
    this.subscriptions.push(
      interval(1000).subscribe(() => {
        const currentState = this._state$.getValue();
        if (currentState.timeUntilReset > 0) {
          this.updateState({ timeUntilReset: currentState.timeUntilReset - 1 });
        } else {
          this.updateState({ timeUntilReset: currentState.resetInterval });
        }
      })
    );
  }

  private updateState(partialState: Partial<TicketDetailState>): void {
    const currentState = this._state$.getValue();
    this._state$.next({ ...currentState, ...partialState });
  }

  // Public methods for components to use
  loadTicketDetails(): void {
    const currentState = this._state$.getValue();
    if (currentState.ticketId) {
      this.updateState({ isLoading: true, apiTimeoutError: false });

      this.ticketService.getTicketById(currentState.ticketId)
        .pipe(
          timeout(currentState.apiTimeout),
          catchError(error => {
            if (error.name === 'TimeoutError') {
              this.updateState({ apiTimeoutError: true });
              return throwError(() => new Error('API call timed out'));
            }
            return throwError(() => error);
          })
        )
        .subscribe({
          next: (ticket) => {
            // Set the first replied_by if there are replies
            const firstRepliedBy = ticket.replies && ticket.replies.length > 0
              ? ticket.replies[0].replied_by
              : '';

            this.updateState({
              ticket,
              firstRepliedBy,
              isLoading: false,
              apiTimeoutError: false
            });
          },
          error: (error) => {
            console.error('Error loading ticket details:', error);
            this.updateState({ isLoading: false });

            // If it's not a timeout error, reset the timeout error flag
            if (error.name !== 'TimeoutError') {
              this.updateState({ apiTimeoutError: false });
            }
          }
        });
    }
  }

  sendReply(): void {
    const currentState = this._state$.getValue();
    
    // Don't allow sending replies if the ticket is closed
    if (this.isTicketClosed()) {
      return;
    }

    if (currentState.replyMessage.trim() && currentState.ticketId) {
      this.updateState({ isLoading: true, apiTimeoutError: false });

      this.ticketService.replyToTicket(currentState.ticketId, currentState.replyMessage)
        .pipe(
          timeout(currentState.apiTimeout),
          catchError(error => {
            if (error.name === 'TimeoutError') {
              this.updateState({ apiTimeoutError: true });
              return throwError(() => new Error('API call timed out'));
            }
            return throwError(() => error);
          })
        )
        .subscribe({
          next: (response) => {
            console.log('Reply sent successfully:', response);
            this.updateState({ 
              replyMessage: '', 
              isLoading: false, 
              apiTimeoutError: false 
            });
            
            // Reload ticket details to show the new reply
            this.loadTicketDetails();
          },
          error: (error) => {
            console.error('Error sending reply:', error);
            this.updateState({ isLoading: false });

            // If it's not a timeout error, reset the timeout error flag
            if (error.name !== 'TimeoutError') {
              this.updateState({ apiTimeoutError: false });
            }
          }
        });
    }
  }

  isTicketClosed(): boolean {
    const currentState = this._state$.getValue();
    return currentState.ticket?.status === 'Closed';
  }

  updateReplyMessage(message: string): void {
    this.updateState({ replyMessage: message });
  }

  dismissApiTimeoutError(): void {
    this.updateState({ apiTimeoutError: false });
  }

  startMessageResetTimer(): void {
    const currentState = this._state$.getValue();
    this.messageResetTimer = setInterval(() => {
      this.updateState({ replyMessage: '' });
    }, currentState.resetInterval * 1000);
  }

  // Initialize the service
  initialize(): void {
    // Clean up any existing subscriptions first
    this.destroy();

    // Initialize subscriptions
    this.initializeSubscriptions();

    // Start message reset timer
    this.startMessageResetTimer();
  }

  // Cleanup
  destroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.subscriptions = [];
    
    // Clear message reset timer
    if (this.messageResetTimer) {
      clearInterval(this.messageResetTimer);
    }
  }
}
