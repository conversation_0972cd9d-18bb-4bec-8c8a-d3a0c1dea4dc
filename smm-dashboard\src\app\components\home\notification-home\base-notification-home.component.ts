import { Component, Input, OnChanges } from '@angular/core';
import { SafeHtml } from '@angular/platform-browser';

@Component({
  template: '', // Will be overridden by child components
})
export abstract class BaseNotificationHomeComponent implements OnChanges {
  @Input() title: string = '';
  @Input() content: SafeHtml = null!;
  
  constructor() {}

  ngOnChanges(): void {
    // Check if we have content and title
    if (this.content) {
      console.log('NotificationHomeComponent - Received fixed notification content');
    }
  }

  // Helper methods that can be used by child components
  get hasContent(): boolean {
    return !!this.content;
  }

  get hasTitle(): boolean {
    return !!this.title;
  }
}
