import { Injectable, On<PERSON><PERSON>roy, HostListener } from '@angular/core';
import { BehaviorSubject, Subscription } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { UpdateService } from '../../core/services/update.service';
import { UpdateLogItem } from '../../model/response/update-logs-res.model';

export interface UpdateState {
  searchTerm: string;
  currentPage: number;
  itemsPerPage: number;
  totalItems: number;
  loading: boolean;
  screenWidth: number;
  selectedFilter: string;
  updateLogs: UpdateLogItem[];
  pages: number[];
}

@Injectable()
export class UpdateLogicService implements OnDestroy {
  private subscriptions: Subscription[] = [];
  
  filterOptions = [
    { value: 'ALL', label: 'update.all' },
    { value: 'PRICE_INCREASE', label: 'update.price_increase' },
    { value: 'PRICE_DECREASE', label: 'update.price_decrease' },
    { value: 'NEW', label: 'update.new' },
    { value: 'ON', label: 'update.on' },
    { value: 'OFF', label: 'update.off' }
  ];

  // Map of status values from API to display values
  statusMap: { [key: string]: string } = {
    'PRICE_INCREASE': 'price_increase',
    'PRICE_DECREASE': 'price_decrease',
    'NEW': 'new',
    'ON': 'on',
    'OFF': 'off'
  };

  private _state$ = new BehaviorSubject<UpdateState>({
    searchTerm: '',
    currentPage: 0,
    itemsPerPage: 20,
    totalItems: 0,
    loading: false,
    screenWidth: 0,
    selectedFilter: 'ALL',
    updateLogs: [],
    pages: []
  });

  public state$ = this._state$.asObservable();

  constructor(
    private updateService: UpdateService,
    private translateService: TranslateService
  ) {
    this.initialize();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private get currentState(): UpdateState {
    return this._state$.value;
  }

  // Getters
  get searchTerm(): string { return this.currentState.searchTerm; }
  get currentPage(): number { return this.currentState.currentPage; }
  get itemsPerPage(): number { return this.currentState.itemsPerPage; }
  get totalItems(): number { return this.currentState.totalItems; }
  get loading(): boolean { return this.currentState.loading; }
  get screenWidth(): number { return this.currentState.screenWidth; }
  get selectedFilter(): string { return this.currentState.selectedFilter; }
  get updateLogs(): UpdateLogItem[] { return this.currentState.updateLogs; }
  get pages(): number[] { return this.currentState.pages; }

  private initialize(): void {
    // Initialize screen width
    this.updateState({ screenWidth: window.innerWidth });

    // Subscribe to loading state
    this.subscriptions.push(
      this.updateService.loading$.subscribe(loading => {
        this.updateState({ loading });
      })
    );

    // Subscribe to update logs
    this.subscriptions.push(
      this.updateService.updateLogs$.subscribe(logs => {
        this.updateState({ updateLogs: logs });
      })
    );

    // Subscribe to pagination
    this.subscriptions.push(
      this.updateService.pagination$.subscribe(pagination => {
        this.updateState({
          currentPage: pagination.pageNumber,
          itemsPerPage: pagination.pageSize,
          totalItems: pagination.totalElements
        });
        this.updatePagination();
      })
    );

    // Initial data load
    this.loadUpdateLogs();
  }

  private updateState(updates: Partial<UpdateState>): void {
    const currentState = this._state$.value;
    this._state$.next({ ...currentState, ...updates });
  }

  onResize(): void {
    this.updateState({ screenWidth: window.innerWidth });
  }

  loadUpdateLogs(): void {
    this.updateService.getUpdateLogs(this.currentPage, this.itemsPerPage).subscribe({
      error: (error) => {
        console.error('Error loading update logs:', error);
      }
    });
  }

  updatePagination(): void {
    const pageCount = Math.ceil(this.totalItems / this.itemsPerPage);
    const pages = Array.from({ length: pageCount }, (_, i) => i + 1);
    this.updateState({ pages });
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= Math.ceil(this.totalItems / this.itemsPerPage)) {
      this.updateState({ currentPage: page - 1 });
      this.loadUpdateLogsWithFilters();
    }
  }

  nextPage(): void {
    this.goToPage(this.currentPage + 2);
  }

  prevPage(): void {
    this.goToPage(this.currentPage);
  }

  onSearch(searchTerm: string): void {
    console.log('Searching for:', searchTerm);
    this.updateState({ 
      searchTerm,
      currentPage: 0
    });
    this.loadUpdateLogsWithFilters();
  }

  onFilterSelected(filter: string): void {
    console.log('Filter selected:', filter);
    const filterKey = this.filterOptions.find(option =>
      this.translateService.instant(option.label) === filter
    )?.value || 'ALL';

    this.updateState({
      selectedFilter: filterKey,
      currentPage: 0
    });
    this.loadUpdateLogsWithFilters();
  }

  loadUpdateLogsWithFilters(): void {
    this.updateService.getUpdateLogsWithFilters(
      this.searchTerm,
      this.selectedFilter,
      this.currentPage,
      this.itemsPerPage
    ).subscribe({
      error: (error) => {
        console.error('Error loading update logs:', error);
      }
    });
  }

  getStatusClass(status: string): string {
    const displayStatus = this.statusMap[status] || status.toLowerCase();

    switch (displayStatus) {
      case 'price_increase':
        return 'bg-orange-500 text-white';
      case 'price_decrease':
        return 'bg-cyan-500 text-white';
      case 'new':
        return 'bg-blue-500 text-white';
      case 'on':
        return 'bg-green-500 text-white';
      case 'off':
        return 'bg-gray-500 text-white';
      default:
        return 'bg-gray-200';
    }
  }

  formatDate(dateString: string): { date: string, time: string } {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return {
      date: `${year}-${month}-${day}`,
      time: date.toLocaleTimeString()
    };
  }

  formatPriceChange(from: number | null, to: number | null): string | null {
    if (from === null || to === null) return null;
    return `${this.translateService.instant('update.from')} $${from} ${this.translateService.instant('update.to')} $${to}`;
  }

  shouldShowPriceChange(status: string): boolean {
    return status === 'PRICE_INCREASE' || status === 'PRICE_DECREASE';
  }

  getVisiblePageCount(): number {
    return this.screenWidth < 640 ? 3 : 5;
  }

  getEllipsisThreshold(): number {
    return this.screenWidth < 640 ? 4 : 6;
  }
}
