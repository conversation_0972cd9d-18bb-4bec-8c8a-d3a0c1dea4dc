import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { Router } from '@angular/router';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { <PERSON><PERSON>anitizer, SafeHtml } from '@angular/platform-browser';
import { IconName } from '@fortawesome/fontawesome-svg-core';

// Services
import { UserService } from '../../../core/services/user.service';
import { CurrencyService } from '../../../core/services/currency.service';
import { AuthService } from '../../../core/services/auth.service';
import { CategoriesService } from '../../../core/services/categories.service';
import { ServiceSelectionService } from '../../../core/services/service-selection.service';
import { OrderService } from '../../../core/services/order.service';
import { PlatformSelectionService } from '../../../core/services/platform-selection.service';
import { NotificationService } from '../../../core/services/notification.service';
import { ThemeService, LayoutTheme } from '../../../core/services/theme.service';

// Models
import { SuperPlatformRes } from '../../../model/response/super-platform.model';
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';
import { GUserSuperRes } from '../../../model/response/g-user-super-res.model';
import { NotificationRes } from '../../../model/response/notification-res.model';

export interface HomeState {
  // User data
  currentUser: GUserSuperRes | null;
  balance: number;

  // Platform data
  platforms: SuperPlatformRes[];
  selectedPlatformId: number | null;

  // Service data
  availableServices: SuperGeneralSvRes[];
  selectedService: SuperGeneralSvRes | null;

  // Form data
  formData: {
    link: string;
    quantity: number;
  };

  // UI state
  isSubmitting: boolean;

  // Stats data
  statsData: {
    completedOrders: number;
    accountBalance: number;
    accountSpent: number;
  };

  // Theme management
  currentTheme: LayoutTheme;

  // Popup notification properties
  popupNotifications: NotificationRes[];
  currentPopupIndex: number;
  showPopup: boolean;
  currentPopupTitle: string;
  currentPopupAutoDismiss: boolean;
  currentPopupDismissHours: number;
  currentPopupContent: SafeHtml;

  // Fixed notification properties
  fixedNotificationTitle: string;
  fixedNotificationContent: SafeHtml;
  hasFixedNotification: boolean;

  // Set to store IDs of temporarily hidden popups
  temporarilyHiddenPopupIds: Set<number>;
}

@Injectable({
  providedIn: 'root'
})
export class HomeLogicService {
  private subscriptions: Subscription[] = [];

  // Local storage key for hidden popup IDs
  private readonly HIDDEN_POPUPS_STORAGE_KEY = 'hiddenPopupIds';

  // State management
  private _state$ = new BehaviorSubject<HomeState>({
    currentUser: null,
    balance: 0,
    platforms: [],
    selectedPlatformId: null,
    availableServices: [],
    selectedService: null,
    formData: {
      link: '',
      quantity: 1
    },
    isSubmitting: false,
    statsData: {
      completedOrders: 0,
      accountBalance: 0,
      accountSpent: 0
    },
    currentTheme: LayoutTheme.DEFAULT,
    popupNotifications: [],
    currentPopupIndex: 0,
    showPopup: false,
    currentPopupTitle: '',
    currentPopupAutoDismiss: false,
    currentPopupDismissHours: 24,
    currentPopupContent: '',
    fixedNotificationTitle: '',
    fixedNotificationContent: '',
    hasFixedNotification: false,
    temporarilyHiddenPopupIds: new Set<number>()
  });

  public state$ = this._state$.asObservable();

  constructor(
    @Inject(PLATFORM_ID) private platformId: Object,
    private router: Router,
    private userService: UserService,
    private currencyService: CurrencyService,
    private authService: AuthService,
    private categoriesService: CategoriesService,
    private serviceSelectionService: ServiceSelectionService,
    private orderService: OrderService,
    private platformSelectionService: PlatformSelectionService,
    private notificationService: NotificationService,
    private themeService: ThemeService,
    private sanitizer: DomSanitizer
  ) {
    this.initialize();
  }

  // Getters for current state
  get currentState(): HomeState {
    return this._state$.value;
  }

  get currentUser(): GUserSuperRes | null {
    return this.currentState.currentUser;
  }

  get balance(): number {
    return this.currentState.balance;
  }

  get platforms(): SuperPlatformRes[] {
    return this.currentState.platforms;
  }

  get selectedPlatformId(): number | null {
    return this.currentState.selectedPlatformId;
  }

  get availableServices(): SuperGeneralSvRes[] {
    return this.currentState.availableServices;
  }

  get selectedService(): SuperGeneralSvRes | null {
    return this.currentState.selectedService;
  }

  get formData() {
    return this.currentState.formData;
  }

  get isSubmitting(): boolean {
    return this.currentState.isSubmitting;
  }

  get statsData() {
    return this.currentState.statsData;
  }

  // Notification getters
  get currentTheme(): LayoutTheme {
    return this.currentState.currentTheme;
  }

  get popupNotifications(): NotificationRes[] {
    return this.currentState.popupNotifications;
  }

  get showPopup(): boolean {
    return this.currentState.showPopup;
  }

  get currentPopupTitle(): string {
    return this.currentState.currentPopupTitle;
  }

  get currentPopupContent(): SafeHtml {
    return this.currentState.currentPopupContent;
  }

  get currentPopupAutoDismiss(): boolean {
    return this.currentState.currentPopupAutoDismiss;
  }

  get currentPopupDismissHours(): number {
    return this.currentState.currentPopupDismissHours;
  }

  get hasFixedNotification(): boolean {
    return this.currentState.hasFixedNotification;
  }

  get fixedNotificationTitle(): string {
    return this.currentState.fixedNotificationTitle;
  }

  get fixedNotificationContent(): SafeHtml {
    return this.currentState.fixedNotificationContent;
  }

  // Initialize service
  private initialize(): void {
    // Load hidden popup IDs from localStorage
    this.loadHiddenPopupIds();

    this.loadUserData();
    this.loadPlatforms();
    this.subscribeToServiceSelection();
    this.subscribeToThemeChanges();
    this.subscribeToNotifications();

    // Load notifications
    this.loadPopupNotifications();
    this.loadFixedNotifications();
  }

  // Update state helper
  private updateState(updates: Partial<HomeState>): void {
    const currentState = this._state$.value;
    this._state$.next({ ...currentState, ...updates });
  }

  // Data loading methods
  private loadUserData(): void {
    this.subscriptions.push(
      this.authService.auth$.subscribe((user: any) => {
        if (user) {
          // Convert UserRes to GUserSuperRes format for display
          const currentUser: GUserSuperRes = {
            id: user.id,
            name: user.name || user.user_name,
            user_name: user.user_name,
            email: user.email,
            balance: user.balance || 0,
            total_spend: user.total_order || 0
          } as GUserSuperRes;

          const balance = user.balance || 0;
          const statsData = {
            completedOrders: 156, // Mock data - replace with actual API
            accountBalance: balance,
            accountSpent: user.total_order || 0
          };

          this.updateState({
            currentUser,
            balance,
            statsData
          });
        }
      })
    );
  }

  private loadPlatforms(): void {
    this.subscriptions.push(
      this.categoriesService.getPlatforms().subscribe({
        next: (platforms: SuperPlatformRes[]) => {
          console.log('HomeLogicService - Raw platforms data:', platforms);

          // Filter out hidden platforms, sort by sort field, and convert icon strings to IconName type
          const filteredPlatforms = platforms
            .filter(platform => !platform.hide)
            .sort((a, b) => a.sort - b.sort)
            .map(platform => ({
              ...platform,
              icon: platform.icon as IconName // Convert string to IconName
            }));

          // Create "All networks" platform
          const allNetworksPlatform: SuperPlatformRes = {
            id: 0, // Use 0 as a special ID for "All networks"
            name: 'all_networks',
            icon: '' as IconName, // Use globe icon for "All networks"
            hide: false,
            sort: -1, // Set lowest sort value to always appear first
            categories: [] // Will be populated with all categories from all platforms
          };

          // Collect all categories from all platforms for the "All networks" platform
          platforms.forEach(platform => {
            if (!platform.hide) {
              // Sort categories by sort field before adding them
              const sortedCategories = [...platform.categories]
                .filter(category => !category.hide)
                .sort((a, b) => a.sort - b.sort);

              sortedCategories.forEach(category => {
                allNetworksPlatform.categories.push({...category});
              });
            }
          });

          // Sort all categories in the "All networks" platform by sort field
          allNetworksPlatform.categories.sort((a, b) => a.sort - b.sort);

          // Add "All networks" as the first platform
          const processedPlatforms = [allNetworksPlatform, ...filteredPlatforms];

          const updates: Partial<HomeState> = { platforms: processedPlatforms };

          if (processedPlatforms.length > 0) {
            updates.selectedPlatformId = processedPlatforms[0].id;
            // Load services for the first platform
            const availableServices = this.extractServicesFromPlatform(processedPlatforms[0]);
            updates.availableServices = availableServices;
          }

          this.updateState(updates);
          console.log('HomeLogicService - Processed platforms:', processedPlatforms);
        },
        error: (error: any) => {
          console.error('Error loading platforms:', error);
        }
      })
    );
  }

  private subscribeToServiceSelection(): void {
    this.subscriptions.push(
      this.serviceSelectionService.serviceSelected$.subscribe((service: SuperGeneralSvRes | undefined) => {
        this.updateState({
          selectedService: service || null
        });
      })
    );
  }

  // Platform and service selection - moved to enhanced version below

  private extractServicesFromPlatform(platform: SuperPlatformRes): SuperGeneralSvRes[] {
    const services: SuperGeneralSvRes[] = [];

    if (platform.categories && platform.categories.length > 0) {
      platform.categories.forEach(category => {
        if (category.services && category.services.length > 0) {
          services.push(...category.services);
        }
      });
    }

    console.log(`Loaded ${services.length} services for platform ${platform.name}`);
    return services;
  }

  onServiceSelected(service: SuperGeneralSvRes): void {
    this.updateState({
      selectedService: service
    });
    this.serviceSelectionService.selectService(service);
  }

  // Form handling
  updateFormData(updates: Partial<HomeState['formData']>): void {
    const currentFormData = this.currentState.formData;
    this.updateState({
      formData: { ...currentFormData, ...updates }
    });
  }

  calculateFee(): number {
    const { selectedService, formData } = this.currentState;
    if (!selectedService || !formData.quantity) {
      return 0;
    }
    return selectedService.price * formData.quantity;
  }

  onSubmitOrder(): void {
    const { selectedService, formData } = this.currentState;

    if (!selectedService || !formData.link || !formData.quantity) {
      return;
    }

    this.updateState({ isSubmitting: true });

    const orderData = {
      service_id: selectedService.id,
      link: formData.link,
      quantity: formData.quantity
    };

    this.subscriptions.push(
      this.orderService.createOrder(orderData).subscribe({
        next: (response) => {
          console.log('Order created successfully:', response);
          this.resetForm();
          this.updateState({ isSubmitting: false });
          // Show success message or redirect
        },
        error: (error) => {
          console.error('Error creating order:', error);
          this.updateState({ isSubmitting: false });
          // Show error message
        }
      })
    );
  }

  private resetForm(): void {
    this.updateState({
      formData: { link: '', quantity: 1 },
      selectedService: null
    });
    this.serviceSelectionService.selectService(undefined);
  }

  // Navigation methods
  logout(): void {
    this.authService.logout();
    this.router.navigate(['/login']);
  }

  navigateToAccount(): void {
    // Navigate to account page
    console.log('Navigate to account');
  }

  // Utility methods
  getUserDisplayName(): string {
    const user = this.currentState.currentUser;
    return user?.name || user?.user_name || 'User';
  }

  getPlatformIcon(icon: string): any {
    return ['fab', icon];
  }

  // Theme subscription
  private subscribeToThemeChanges(): void {
    this.subscriptions.push(
      this.themeService.currentLayoutTheme$.subscribe(theme => {
        this.updateState({ currentTheme: theme });
      })
    );
  }

  // Notification subscriptions
  private subscribeToNotifications(): void {
    // Subscribe to popup notifications
    this.subscriptions.push(
      this.notificationService.popupNotifications$.subscribe(notifications => {
        const state = this.currentState;
        // Filter notifications that should be shown and are not in the hidden list
        const popupNotifications = notifications.filter(n =>
          n.show === true && !state.temporarilyHiddenPopupIds.has(n.id)
        );
        console.log('HomeLogicService - Popup notifications updated:', popupNotifications);

        this.updateState({ popupNotifications });

        // Show the first popup if there are any and none is currently shown
        if (popupNotifications.length > 0 && !state.showPopup) {
          this.updateState({ currentPopupIndex: 0 });
          this.showNextPopup();
        } else if (popupNotifications.length === 0) {
          // Hide popup if there are no notifications to show
          this.updateState({ showPopup: false });
        }
      })
    );

    // Subscribe to fixed notifications
    this.subscriptions.push(
      this.notificationService.fixedNotifications$.subscribe(notifications => {
        // Find the first active fixed notification
        const activeFixedNotification = notifications.find(n => n.show === true);

        if (activeFixedNotification) {
          console.log('HomeLogicService - Active fixed notification found:', activeFixedNotification);
          const fixedNotificationTitle = activeFixedNotification.title || 'Thông báo';

          // Sanitize the HTML content
          let fixedNotificationContent: SafeHtml;
          if (activeFixedNotification.content) {
            fixedNotificationContent = this.sanitizer.bypassSecurityTrustHtml(activeFixedNotification.content);
          } else {
            fixedNotificationContent = this.sanitizer.bypassSecurityTrustHtml('');
          }

          this.updateState({
            hasFixedNotification: true,
            fixedNotificationTitle,
            fixedNotificationContent
          });
        } else {
          this.updateState({
            hasFixedNotification: false,
            fixedNotificationTitle: '',
            fixedNotificationContent: this.sanitizer.bypassSecurityTrustHtml('')
          });
        }
      })
    );
  }

  // LocalStorage methods for hidden popup IDs
  private loadHiddenPopupIds(): void {
    if (!isPlatformBrowser(this.platformId)) return;

    try {
      const hiddenIdsJson = localStorage.getItem(this.HIDDEN_POPUPS_STORAGE_KEY);
      if (hiddenIdsJson) {
        const hiddenIds = JSON.parse(hiddenIdsJson) as number[];
        console.log('HomeLogicService - Loaded hidden popup IDs from localStorage:', hiddenIds);

        // Create new Set with hidden IDs
        const temporarilyHiddenPopupIds = new Set<number>();
        hiddenIds.forEach(id => temporarilyHiddenPopupIds.add(id));

        this.updateState({ temporarilyHiddenPopupIds });
      }
    } catch (error) {
      console.error('HomeLogicService - Error loading hidden popup IDs from localStorage:', error);
      // If there's an error, clear the localStorage item
      if (isPlatformBrowser(this.platformId)) {
        localStorage.removeItem(this.HIDDEN_POPUPS_STORAGE_KEY);
      }
    }
  }

  private saveHiddenPopupIds(): void {
    if (!isPlatformBrowser(this.platformId)) return;

    try {
      const hiddenIds = Array.from(this.currentState.temporarilyHiddenPopupIds);
      localStorage.setItem(this.HIDDEN_POPUPS_STORAGE_KEY, JSON.stringify(hiddenIds));
      console.log('HomeLogicService - Saved hidden popup IDs to localStorage:', hiddenIds);
    } catch (error) {
      console.error('HomeLogicService - Error saving hidden popup IDs to localStorage:', error);
    }
  }

  // Notification loading methods
  loadPopupNotifications(): void {
    console.log('HomeLogicService - Loading popup notifications');
    this.notificationService.getPopupNotifications().subscribe({
      next: (notifications) => {
        console.log('HomeLogicService - Popup notifications loaded:', notifications);

        const state = this.currentState;
        // Filter out notifications that don't have show=true and are in the hidden set
        let visibleNotifications = notifications.filter(n =>
          n.show === true && !state.temporarilyHiddenPopupIds.has(n.id)
        );
        console.log('HomeLogicService - Visible popup notifications:', visibleNotifications);

        // Update our state
        this.updateState({ popupNotifications: visibleNotifications });

        // If we have notifications and none is currently shown, show the first one
        if (visibleNotifications.length > 0 && !state.showPopup) {
          this.updateState({ currentPopupIndex: 0 });
          this.showNextPopup();
        } else if (visibleNotifications.length === 0) {
          // Hide popup if there are no notifications to show
          this.updateState({ showPopup: false });
          console.log('HomeLogicService - No visible popup notifications to show');
        }
      },
      error: (error) => {
        console.error('HomeLogicService - Error loading popup notifications:', error);
      }
    });
  }

  loadFixedNotifications(): void {
    console.log('HomeLogicService - Loading fixed notifications');
    this.notificationService.getFixedNotifications().subscribe({
      next: (notifications) => {
        console.log('HomeLogicService - Fixed notifications loaded:', notifications);

        // Handle empty array response (no fixed notifications available)
        if (!notifications || notifications.length === 0) {
          console.log('HomeLogicService - No fixed notifications available');
          this.updateState({
            hasFixedNotification: false,
            fixedNotificationTitle: '',
            fixedNotificationContent: this.sanitizer.bypassSecurityTrustHtml('')
          });
          return;
        }

        // Find the first active fixed notification
        const activeFixedNotification = notifications.find(n => n.show === true);

        if (activeFixedNotification) {
          console.log('HomeLogicService - Active fixed notification found:', activeFixedNotification);
          const fixedNotificationTitle = activeFixedNotification.title || 'Thông báo';

          // Sanitize the HTML content
          let fixedNotificationContent: SafeHtml;
          if (activeFixedNotification.content) {
            fixedNotificationContent = this.sanitizer.bypassSecurityTrustHtml(activeFixedNotification.content);
          } else {
            fixedNotificationContent = this.sanitizer.bypassSecurityTrustHtml('');
          }

          this.updateState({
            hasFixedNotification: true,
            fixedNotificationTitle,
            fixedNotificationContent
          });
        } else {
          this.updateState({
            hasFixedNotification: false,
            fixedNotificationTitle: '',
            fixedNotificationContent: this.sanitizer.bypassSecurityTrustHtml('')
          });
        }
      },
      error: (error) => {
        // This should no longer be triggered for the {code: 200, message: 'OK'} response
        console.error('HomeLogicService - Error loading fixed notifications:', error);
        this.updateState({
          hasFixedNotification: false,
          fixedNotificationTitle: '',
          fixedNotificationContent: this.sanitizer.bypassSecurityTrustHtml('')
        });
      }
    });
  }

  // Popup management methods
  showNextPopup(): void {
    const state = this.currentState;

    // Check if there are any notifications to show
    if (!state.popupNotifications || state.popupNotifications.length === 0) {
      this.updateState({ showPopup: false });
      console.log('HomeLogicService - No popups to show');
      return;
    }

    // Check if the current index is valid
    let currentPopupIndex = state.currentPopupIndex;
    if (currentPopupIndex >= state.popupNotifications.length) {
      currentPopupIndex = 0;
      console.log('HomeLogicService - Invalid popup index, resetting to 0');

      // If we still don't have a valid index, hide the popup
      if (state.popupNotifications.length === 0) {
        this.updateState({ showPopup: false });
        console.log('HomeLogicService - No popups to show after index reset');
        return;
      }
    }

    // Get the current popup
    const popup = state.popupNotifications[currentPopupIndex];

    // Check if this popup is temporarily hidden
    if (state.temporarilyHiddenPopupIds.has(popup.id)) {
      console.log(`HomeLogicService - Popup ${popup.id} is temporarily hidden, moving to next`);

      // Move to the next popup
      currentPopupIndex = (currentPopupIndex + 1) % state.popupNotifications.length;

      // Check if we've gone through all popups
      if (currentPopupIndex === 0) {
        console.log('HomeLogicService - Checked all popups, none available to show');
        this.updateState({ showPopup: false });
        return;
      }

      // Try the next popup
      this.updateState({ currentPopupIndex });
      this.showNextPopup();
      return;
    }

    const currentPopupTitle = popup.title || 'Notification';
    const currentPopupAutoDismiss = popup.auto_dismiss || false;
    const currentPopupDismissHours = popup.dismiss_hours || 24;
    console.log('HomeLogicService - Current popup details:', popup);

    // Sanitize the content if it's a URL
    let currentPopupContent: SafeHtml;
    if (popup.content) {
      if (popup.content.startsWith('http://') || popup.content.startsWith('https://')) {
        // If it's a URL, create a link
        currentPopupContent = this.sanitizer.bypassSecurityTrustHtml(`<a href="${popup.content}" target="_blank">${popup.content}</a>`);
      } else if (popup.content.includes('<') && popup.content.includes('>')) {
        // If it already contains HTML tags, use it as is
        currentPopupContent = this.sanitizer.bypassSecurityTrustHtml(popup.content);
      } else {
        // Otherwise, wrap it in a paragraph
        currentPopupContent = this.sanitizer.bypassSecurityTrustHtml(`<p>${popup.content}</p>`);
      }
    } else {
      // Empty content
      currentPopupContent = this.sanitizer.bypassSecurityTrustHtml('');
    }

    this.updateState({
      currentPopupIndex,
      currentPopupTitle,
      currentPopupAutoDismiss,
      currentPopupDismissHours,
      currentPopupContent,
      showPopup: true
    });

    console.log(`HomeLogicService - Showing popup ${currentPopupIndex + 1}/${state.popupNotifications.length}:`, popup);
  }

  closePopup(): void {
    console.log('HomeLogicService - Closing popup');
    const state = this.currentState;

    // Get the current popup ID and add it to the temporarily hidden set
    if (state.popupNotifications.length > 0 && state.currentPopupIndex < state.popupNotifications.length) {
      const popupId = state.popupNotifications[state.currentPopupIndex].id;
      const newHiddenIds = new Set(state.temporarilyHiddenPopupIds);
      newHiddenIds.add(popupId);

      this.updateState({
        showPopup: false,
        temporarilyHiddenPopupIds: newHiddenIds
      });

      console.log(`HomeLogicService - Added popup ${popupId} to temporarily hidden set:`, Array.from(newHiddenIds));

      // Don't save to localStorage - this makes the popup only hidden for the current session
    }

    // Move to the next popup
    let currentPopupIndex = state.currentPopupIndex + 1;

    // If we've shown all popups, reset to the beginning
    if (currentPopupIndex >= state.popupNotifications.length) {
      currentPopupIndex = 0;
      console.log('HomeLogicService - Reached the end of popups, resetting to the beginning');
    }

    // Get the next popup that isn't temporarily hidden
    let nextPopupIndex = currentPopupIndex;
    let foundNextPopup = false;

    // Check if there are any popups that aren't temporarily hidden
    for (let i = 0; i < state.popupNotifications.length; i++) {
      const checkIndex = (currentPopupIndex + i) % state.popupNotifications.length;
      const popupId = state.popupNotifications[checkIndex].id;

      if (!state.temporarilyHiddenPopupIds.has(popupId)) {
        nextPopupIndex = checkIndex;
        foundNextPopup = true;
        break;
      }
    }

    // If we found a popup that isn't temporarily hidden, show it
    if (foundNextPopup) {
      this.updateState({ currentPopupIndex: nextPopupIndex });
      setTimeout(() => {
        this.showNextPopup();
      }, 500); // Small delay before showing the next popup
    } else {
      console.log('HomeLogicService - All popups are temporarily hidden');
    }
  }

  dismissPopup(): void {
    console.log('HomeLogicService - Dismissing popup');
    const state = this.currentState;

    if (state.popupNotifications.length === 0 || state.currentPopupIndex >= state.popupNotifications.length) {
      console.error('HomeLogicService - No popup to dismiss at index', state.currentPopupIndex);
      this.updateState({ showPopup: false });
      return;
    }

    // Get the current popup ID
    const popupId = state.popupNotifications[state.currentPopupIndex].id;

    // Mark the notification as viewed
    this.notificationService.markAsViewed(popupId).subscribe({
      next: (viewResponse) => {
        console.log('HomeLogicService - Popup marked as viewed successfully:', viewResponse);

        // Remove the popup from the local array
        const newPopupNotifications = state.popupNotifications.filter(n => n.id !== popupId);

        // Add to hidden popups and save to localStorage (intentionally persisting this choice)
        const newHiddenIds = new Set(state.temporarilyHiddenPopupIds);
        newHiddenIds.add(popupId);

        this.updateState({
          popupNotifications: newPopupNotifications,
          temporarilyHiddenPopupIds: newHiddenIds,
          showPopup: false
        });

        this.saveHiddenPopupIds();

        // Reset the index if needed
        let currentPopupIndex = state.currentPopupIndex;
        if (currentPopupIndex >= newPopupNotifications.length) {
          currentPopupIndex = 0;
        }
        this.updateState({ currentPopupIndex });

        // Show the next popup if there are more
        if (newPopupNotifications.length > 0) {
          setTimeout(() => {
            this.showNextPopup();
          }, 500); // Small delay before showing the next popup
        } else {
          console.log('HomeLogicService - No more popups to show after dismissal');
        }
      },
      error: (error) => {
        console.error('HomeLogicService - Error marking popup as viewed:', error);
        // Still close the popup and try to show the next one
        this.closePopup();
      }
    });
  }

  getCurrentPopupId(): number | null {
    const state = this.currentState;
    if (!state.popupNotifications ||
        state.popupNotifications.length === 0 ||
        state.currentPopupIndex >= state.popupNotifications.length ||
        !state.popupNotifications[state.currentPopupIndex]) {
      return null;
    }
    return state.popupNotifications[state.currentPopupIndex].id;
  }

  // Enhanced platform selection that also updates PlatformSelectionService
  onPlatformSelected(platform: SuperPlatformRes): void {
    const availableServices = this.extractServicesFromPlatform(platform);

    this.updateState({
      selectedPlatformId: platform.id,
      selectedService: null,
      availableServices
    });

    // Also update PlatformSelectionService for compatibility with existing components
    this.platformSelectionService.selectPlatform(platform);

    // Clear service selection
    this.serviceSelectionService.selectService(undefined);
  }

  // Cleanup
  destroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.subscriptions = [];
  }
}
