import { Injectable } from '@angular/core';
import { BehaviorSubject, Subscription } from 'rxjs';

// Services
import { SidebarService } from '../../../core/services/sidebar.service';
import { ThemeService, LayoutTheme } from '../../../core/services/theme.service';

export interface LayoutState {
  // Sidebar state
  isOpen: boolean;
  
  // Theme management
  currentTheme: LayoutTheme;
  
  // Layout configuration
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class LayoutLogicService {
  private subscriptions: Subscription[] = [];

  // State management
  private _state$ = new BehaviorSubject<LayoutState>({
    isOpen: false,
    currentTheme: LayoutTheme.DEFAULT,
    isMobile: false,
    isTablet: false,
    isDesktop: true
  });

  // Public state observable
  public readonly state$ = this._state$.asObservable();

  // Current state getter
  private get currentState(): LayoutState {
    return this._state$.value;
  }

  constructor(
    private sidebarService: SidebarService,
    private themeService: ThemeService
  ) {
    this.initialize();
  }

  // Public getters for template access
  get isOpen(): boolean {
    return this.currentState.isOpen;
  }

  get currentTheme(): LayoutTheme {
    return this.currentState.currentTheme;
  }

  get isMobile(): boolean {
    return this.currentState.isMobile;
  }

  get isTablet(): boolean {
    return this.currentState.isTablet;
  }

  get isDesktop(): boolean {
    return this.currentState.isDesktop;
  }

  // Initialize service
  private initialize(): void {
    this.subscribeToSidebarState();
    this.subscribeToThemeChanges();
    this.initializeResponsiveDetection();
  }

  // Update state helper
  private updateState(updates: Partial<LayoutState>): void {
    const currentState = this._state$.value;
    this._state$.next({ ...currentState, ...updates });
  }

  // Subscription methods
  private subscribeToSidebarState(): void {
    this.subscriptions.push(
      this.sidebarService.sidebarOpen$.subscribe(state => {
        this.updateState({ isOpen: state });
      })
    );
  }

  private subscribeToThemeChanges(): void {
    this.subscriptions.push(
      this.themeService.currentLayoutTheme$.subscribe((theme: LayoutTheme) => {
        this.updateState({ currentTheme: theme });
      })
    );
  }

  // Responsive detection
  private initializeResponsiveDetection(): void {
    this.updateResponsiveState();
    
    // Listen for window resize
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', () => {
        this.updateResponsiveState();
      });
    }
  }

  private updateResponsiveState(): void {
    if (typeof window !== 'undefined') {
      const width = window.innerWidth;
      const updates: Partial<LayoutState> = {
        isMobile: width < 768,
        isTablet: width >= 768 && width < 1024,
        isDesktop: width >= 1024
      };
      this.updateState(updates);
    }
  }

  // Public methods for component interaction
  toggleSidebar(): void {
    this.sidebarService.toggleSidebar();
  }

  closeSidebar(): void {
    if (this.currentState.isOpen) {
      this.sidebarService.setSidebarState(false);
    }
  }

  openSidebar(): void {
    this.sidebarService.setSidebarState(true);
  }

  setSidebarState(state: boolean): void {
    this.sidebarService.setSidebarState(state);
  }

  // Theme management methods
  setTheme(theme: LayoutTheme): void {
    this.themeService.setLayoutTheme(theme);
  }

  // Responsive helper methods
  isMobileDevice(): boolean {
    return this.currentState.isMobile;
  }

  isTabletDevice(): boolean {
    return this.currentState.isTablet;
  }

  isDesktopDevice(): boolean {
    return this.currentState.isDesktop;
  }

  // Layout utility methods
  getLayoutClasses(): string[] {
    const classes: string[] = [];
    
    if (this.currentState.isOpen) {
      classes.push('sidebar-open');
    }
    
    if (this.currentState.isMobile) {
      classes.push('mobile-layout');
    } else if (this.currentState.isTablet) {
      classes.push('tablet-layout');
    } else {
      classes.push('desktop-layout');
    }
    
    classes.push(`theme-${this.currentState.currentTheme.toLowerCase()}`);
    
    return classes;
  }

  // Cleanup method
  destroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.subscriptions = [];
    
    // Remove window event listeners
    if (typeof window !== 'undefined') {
      window.removeEventListener('resize', this.updateResponsiveState);
    }
  }
}
