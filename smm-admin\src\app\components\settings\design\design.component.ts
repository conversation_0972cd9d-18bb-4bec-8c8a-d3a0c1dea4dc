import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';
import { ToastService } from '../../../core/services/toast.service';
import { DesignSettingsService } from '../../../core/services/design-settings.service';
import { DesignSettingsReq } from '../../../model/request/design-settings-req.model';
import { DesignSettingsRes } from '../../../model/response/design-settings-res.model';
import { HttpClient, HttpEventType } from '@angular/common/http';
import { ConfigService } from '../../../core/services/config.service';
import { finalize } from 'rxjs/operators';

interface ColorOption {
  value: string;
  selected: boolean;
}

@Component({
  selector: 'app-design',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    IconsModule
  ],
  templateUrl: './design.component.html',
  styleUrls: ['./design.component.css']
})
export class DesignComponent implements OnInit {
  // Logo & Favicon
  logoFile: File | null = null;
  faviconFile: File | null = null;

  // Upload state
  logoPreviewUrl: string | null = null;
  faviconPreviewUrl: string | null = null;
  logoUploading: boolean = false;
  faviconUploading: boolean = false;
  uploadProgress: number = 0;
  logoUploadSuccess: boolean = false;
  faviconUploadSuccess: boolean = false;
  logoUploadError: string | null = null;
  faviconUploadError: string | null = null;

  // Domain info
  currentDomain: string = window.location.hostname;

  // Theme colors - removed unused color arrays

  textButtonColors: ColorOption[] = [
    { value: '#FFFFFF', selected: true },
    { value: '#000000', selected: false }
  ];

  // Primary colors
  primaryColors: ColorOption[] = [
    { value: '#30B0C7', selected: true },  // Default blue
    { value: '#10B981', selected: false }, // Green
    { value: '#3B82F6', selected: false }, // Blue
    { value: '#8B5CF6', selected: false }, // Purple
    { value: '#EC4899', selected: false }, // Pink
    { value: '#F59E0B', selected: false }, // Orange
    { value: '#EF4444', selected: false }, // Red
    { value: '#6B7280', selected: false }  // Gray
  ];

  // Custom primary color input
  customPrimaryColor: string = '';
  showCustomColorInput: boolean = false;

  // Dark theme toggle
  isDarkTheme: boolean = false;

  // Preview values for real-time UI updates without applying to the app
  previewPrimaryColor: string = '';
  previewHeaderStyle: string = '';
  previewSidebarStyle: string = '';
  previewTextButtonColor: string = '';

  // Header style options
  headerStyleOptions = [
    { id: 'standard', label: 'Standard', selected: true },
    { id: 'compact', label: 'Compact', selected: false },
    { id: 'modern', label: 'Modern', selected: false },
    { id: 'minimal', label: 'Minimal', selected: false }
  ];

  // Sidebar style options
  sidebarStyleOptions = [
    { id: 'standard', label: 'Standard', selected: true },
    { id: 'compact', label: 'Compact', selected: false },
    { id: 'modern', label: 'Modern', selected: false },
    { id: 'minimal', label: 'Minimal', selected: false },
    { id: 'card', label: 'Card', selected: false }
  ];

  // Removed unused order view options and social networks

  // Landing page settings
  landingTypes = [
    { id: 'modern', name: 'Modern', selected: true },
    { id: 'classic', name: 'Classic', selected: false },
    { id: 'minimal', name: 'Minimal', selected: false }
  ];

  // Add this array for built-in icons (choose your own filenames for the 30 icons)
  builtInFaviconIcons: string[] = Array.from({ length: 30 }, (_, i) => `cdn/ico/social${i + 1}.ico`);

  // Popup state for favicon selection
  showFaviconPopup: boolean = false;

  constructor(
    private toastService: ToastService,
    private designSettingsService: DesignSettingsService,
    private http: HttpClient,
    private configService: ConfigService
  ) {}

  ngOnInit(): void {
    this.loadDesignSettings();

    // Initialize preview values with defaults
    this.previewPrimaryColor = '#30B0C7'; // Default primary color
    this.previewHeaderStyle = 'standard'; // Default header style
    this.previewSidebarStyle = 'standard'; // Default sidebar style
    this.previewTextButtonColor = this.getSelectedTextButtonColor();
  }

  /**
   * Load design settings from the server
   */
  loadDesignSettings(): void {
    this.designSettingsService.getDesignSettings().subscribe({
      next: (settings) => {
        this.updateUIFromSettings(settings);
      },
      error: (error) => {
        console.error('Error loading design settings:', error);
        this.toastService.showError(error?.message || 'Failed to load design settings');
      }
    });
  }

  /**
   * Update UI components based on settings from server
   */
  updateUIFromSettings(settings: DesignSettingsRes): void {
    // Update primary color
    if (settings.color_scheme?.primary) {
      const primaryColorExists = this.primaryColors.some(color => {
        const isMatch = color.value === settings.color_scheme.primary;
        color.selected = isMatch;
        return isMatch;
      });

      // If the primary color from settings doesn't match any predefined colors,
      // set it as a custom color
      if (!primaryColorExists) {
        this.showCustomColorInput = true;
        this.customPrimaryColor = settings.color_scheme.primary;
        // Deselect all predefined colors
        this.primaryColors.forEach(color => color.selected = false);
      }

      // Set preview color
      this.previewPrimaryColor = settings.color_scheme.primary;
    }

    // Update logo and favicon preview URLs
    if (settings.logo?.url) {
      this.logoPreviewUrl = settings.logo.url;
    }

    if (settings.favicon?.url) {
      this.faviconPreviewUrl = settings.favicon.url;
    }

    // Update text button color
    if (settings.color_scheme?.text_on_buttons) {
      this.textButtonColors.forEach(color => {
        color.selected = color.value === settings.color_scheme.text_on_buttons;
      });

      // Set preview text button color
      this.previewTextButtonColor = settings.color_scheme.text_on_buttons;
    }

    // Update header style
    if (settings.header_settings?.type) {
      this.headerStyleOptions.forEach(option => {
        option.selected = option.id === settings.header_settings.type;
      });

      // Set preview header style
      this.previewHeaderStyle = settings.header_settings.type;
    }

    // Update sidebar style
    if (settings.sidebar_settings?.type) {
      this.sidebarStyleOptions.forEach(option => {
        option.selected = option.id === settings.sidebar_settings.type;
      });

      // Set preview sidebar style
      this.previewSidebarStyle = settings.sidebar_settings.type;
    }

    // Update landing type
    if (settings.landing_settings?.type) {
      this.landingTypes.forEach(type => {
        type.selected = type.id === settings.landing_settings.type;
      });
    }
  }

  // Logo & Favicon methods are now implemented below

  // Color selection methods - removed unused methods

  selectTextButtonColor(color: ColorOption): void {
    this.textButtonColors.forEach(c => c.selected = false);
    color.selected = true;

    // Update preview value only
    this.previewTextButtonColor = color.value;
  }

  // Primary color methods
  selectPrimaryColor(color: ColorOption): void {
    this.primaryColors.forEach(c => c.selected = false);
    color.selected = true;
    this.showCustomColorInput = false;

    // Update preview value only
    this.previewPrimaryColor = color.value;
  }

  toggleCustomColorInput(): void {
    this.showCustomColorInput = !this.showCustomColorInput;
    if (this.showCustomColorInput) {
      // Deselect all predefined colors
      this.primaryColors.forEach(c => c.selected = false);

      // If no custom color is set yet, use the current primary color
      if (!this.customPrimaryColor) {
        this.customPrimaryColor = this.getSelectedPrimaryColor();
      }
    }
  }

  applyCustomColor(): void {
    if (this.customPrimaryColor && this.isValidHexColor(this.customPrimaryColor)) {
      // Update preview value only
      this.previewPrimaryColor = this.customPrimaryColor;
    } else {
      this.toastService.showError('Please enter a valid hex color (e.g., #30B0C7)');
    }
  }

  isValidHexColor(color: string): boolean {
    return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color);
  }

  // Helper methods for template - removed unused methods

  getSelectedTextButtonColor(): string {
    // If we have a preview value, use it
    if (this.previewTextButtonColor) {
      return this.previewTextButtonColor;
    }
    return this.textButtonColors.find(c => c.selected)?.value || '#FFFFFF';
  }

  getSelectedPrimaryColor(): string {
    // If we have a preview value, use it
    if (this.previewPrimaryColor) {
      return this.previewPrimaryColor;
    }

    if (this.showCustomColorInput && this.customPrimaryColor) {
      return this.customPrimaryColor;
    }
    return this.primaryColors.find(c => c.selected)?.value || '#30B0C7';
  }

  // Helper methods for preview
  getPreviewHeaderStyle(): string {
    return this.previewHeaderStyle || this.headerStyleOptions.find(o => o.selected)?.id || 'standard';
  }

  getPreviewSidebarStyle(): string {
    return this.previewSidebarStyle || this.sidebarStyleOptions.find(o => o.selected)?.id || 'standard';
  }

  // Removed unused dark theme toggle method

  // Header style methods
  selectHeaderStyle(option: any): void {
    this.headerStyleOptions.forEach(opt => opt.selected = false);
    option.selected = true;

    // Update preview value only
    this.previewHeaderStyle = option.id;
  }

  // Sidebar style methods
  selectSidebarStyle(option: any): void {
    this.sidebarStyleOptions.forEach(opt => opt.selected = false);
    option.selected = true;

    // Update preview value only
    this.previewSidebarStyle = option.id;
  }

  // Removed unused order view and social network methods

  // Landing page methods
  selectLandingType(type: any): void {
    this.landingTypes.forEach(t => t.selected = false);
    type.selected = true;
  }

  // Save settings
  saveSettings(): void {
    // Get the selected primary color (either from predefined colors or custom input)
    const selectedPrimaryColor = this.getSelectedPrimaryColor();
    const selectedTextButtonColor = this.getSelectedTextButtonColor();

    // Get the selected header style
    const selectedHeaderStyle = this.getPreviewHeaderStyle();

    // Get the selected sidebar style
    const selectedSidebarStyle = this.getPreviewSidebarStyle();

    // Get the selected landing type
    const selectedLandingType = this.landingTypes.find(t => t.selected)?.id || 'modern';

    // Create the settings object with the new structure
    const settings: DesignSettingsReq = {
      color_scheme: {
        primary: selectedPrimaryColor,
        text_on_buttons: selectedTextButtonColor
      },
      header_settings: {
        type: selectedHeaderStyle as 'standard' | 'compact' | 'modern' | 'minimal'
      },
      sidebar_settings: {
        type: selectedSidebarStyle as 'standard' | 'compact' | 'modern' | 'minimal' | 'card'
      },
      landing_settings: {
        type: selectedLandingType
      }
    };

    // Add logo and favicon if they exist
    if (this.logoPreviewUrl) {
      settings.logo = {
        url: this.logoPreviewUrl,
        file_type: this.logoPreviewUrl.split('.').pop() || 'png'
      };
    }

    if (this.faviconPreviewUrl) {
      settings.favicon = {
        url: this.faviconPreviewUrl,
        file_type: this.faviconPreviewUrl.split('.').pop() || 'ico'
      };
    }

    // Apply CSS variables directly for immediate visual feedback
   // this.updateCssVariables(selectedPrimaryColor);

    // Save to localStorage first
    // const currentSettings = this.designSettingsService.getSettingsFromLocalStorage();
    // const updatedSettings = {
    //   ...currentSettings,
    //   color_scheme: settings.color_scheme,
    //   header_settings: settings.header_settings,
    //   sidebar_settings: settings.sidebar_settings,
    //   landing_settings: settings.landing_settings
    // };
    // this.designSettingsService.saveSettingsToLocalStorage(updatedSettings as DesignSettingsRes);

    // Update all settings at once using the createSettings endpoint
    this.designSettingsService.createSettings(settings).subscribe({
      next: (response) => {
        this.toastService.showSuccess('Design settings saved successfully');

        // Apply the primary color to update CSS variables
        // if (response.color_scheme?.primary) {
        //   this.updateCssVariables(response.color_scheme.primary);
        // }
      },
      error: (error) => {
        console.error('Error saving design settings:', error);
        this.toastService.showError(error?.message || 'Failed to save design settings');
      }
    });
  }

  /**
   * Update CSS variables for primary color
   */
  private updateCssVariables(primaryColor: string): void {
    if (typeof document === 'undefined') return;

    // Get or create the style element for dynamic theme styles
    let styleEl = document.getElementById('dynamic-theme-styles') as HTMLStyleElement;
    if (!styleEl) {
      styleEl = document.createElement('style');
      styleEl.id = 'dynamic-theme-styles';
      document.head.appendChild(styleEl);
    }

    // Generate related colors
    const hoverColor = this.adjustBrightness(primaryColor, -15); // Darker for hover
    const activeColor = this.adjustBrightness(primaryColor, -25); // Even darker for active
    const lightColor = this.adjustBrightness(primaryColor, 40, true); // Lighter with transparency

    // Create CSS content with all our variables
    const cssContent = `
      :root {
        --primary: ${primaryColor} !important;
        --primary-hover: ${hoverColor} !important;
        --primary-active: ${activeColor} !important;
        --primary-light: ${lightColor} !important;
        --admin-primary: ${primaryColor} !important;
      }
    `;

    // Apply the CSS
    styleEl.textContent = cssContent;

    // Also set inline styles for immediate effect
    document.documentElement.style.setProperty('--primary', primaryColor);
    document.documentElement.style.setProperty('--primary-hover', hoverColor);
    document.documentElement.style.setProperty('--primary-active', activeColor);
    document.documentElement.style.setProperty('--primary-light', lightColor);
    document.documentElement.style.setProperty('--admin-primary', primaryColor);
  }

  /**
   * Adjust brightness of a color
   */
  private adjustBrightness(color: string, percent: number, withTransparency: boolean = false): string {
    // Remove # if present
    const hex = color.replace('#', '');

    // Parse RGB values
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);

    // Calculate new values
    const newR = Math.max(0, Math.min(255, r + (r * percent / 100)));
    const newG = Math.max(0, Math.min(255, g + (g * percent / 100)));
    const newB = Math.max(0, Math.min(255, b + (b * percent / 100)));

    if (withTransparency) {
      return `rgba(${Math.round(newR)}, ${Math.round(newG)}, ${Math.round(newB)}, 0.1)`;
    }

    // Convert back to hex
    const toHex = (n: number) => {
      const hex = Math.round(n).toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    };

    return `#${toHex(newR)}${toHex(newG)}${toHex(newB)}`;
  }

  // Edit landing text
  editLandingText(): void {
    // For now, we'll use a simple prompt
    const newText = prompt('Enter new landing text:', 'The most comfortable');
    if (newText) {
      this.designSettingsService.updateLandingText(newText).subscribe({
        next: () => {
          this.toastService.showSuccess('Landing text updated successfully');
          // Update the landing title in the UI
          const landingTitleElement = document.querySelector('.landing-title');
          if (landingTitleElement) {
            landingTitleElement.textContent = newText;
          }
        },
        error: (error) => {
          console.error('Error updating landing text:', error);
          this.toastService.showError('Failed to update landing text');
        }
      });
    }
  }

  /**
   * Copy favicon URL to clipboard
   */
  copyFaviconUrl(): void {
    if (this.faviconPreviewUrl) {
      navigator.clipboard.writeText(this.faviconPreviewUrl)
        .then(() => this.toastService.showSuccess('URL copied to clipboard'))
        .catch(() => this.toastService.showError('Failed to copy URL'));
    } else {
      const faviconUrl = document.querySelector('.favicon-url') as HTMLInputElement;
      if (faviconUrl) {
        faviconUrl.select();
        document.execCommand('copy');
        this.toastService.showSuccess('URL copied to clipboard');
      }
    }
  }

  // File upload methods
  onLogoFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.logoFile = input.files[0];
      this.uploadLogo(this.logoFile);
    }
  }

  onFaviconFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.faviconFile = input.files[0];
      this.uploadFavicon(this.faviconFile);
    }
  }

  /**
   * Upload logo file to server
   */
  uploadLogo(file: File): void {
    // Reset state
    this.logoUploading = true;
    this.logoUploadSuccess = false;
    this.logoUploadError = null;
    this.uploadProgress = 0;

    // Create form data
    const formData = new FormData();
    formData.append('file', file);

    // Upload file
    this.http.post<any>(`${this.configService.apiUrl}/uploads/logo`, formData, {
      reportProgress: true,
      observe: 'events'
    }).pipe(
      finalize(() => {
        this.logoUploading = false;
      })
    ).subscribe({
      next: (event) => {
        if (event.type === HttpEventType.UploadProgress && event.total) {
          this.uploadProgress = Math.round(100 * event.loaded / event.total);
        } else if (event.type === HttpEventType.Response) {
          this.logoUploadSuccess = true;
          this.logoPreviewUrl = event.body.url;
          this.toastService.showSuccess('Logo uploaded successfully');
        }
      },
      error: (error) => {
        this.logoUploadError = 'Failed to upload logo';
        this.toastService.showError('Failed to upload logo');
        console.error('Error uploading logo:', error);
      }
    });
  }

  /**
   * Upload favicon file to server
   */
  uploadFavicon(file: File): void {
    // Reset state
    this.faviconUploading = true;
    this.faviconUploadSuccess = false;
    this.faviconUploadError = null;
    this.uploadProgress = 0;

    // Create form data
    const formData = new FormData();
    formData.append('file', file);

    // Upload file
    this.http.post<any>(`${this.configService.apiUrl}/uploads/favicon`, formData, {
      reportProgress: true,
      observe: 'events'
    }).pipe(
      finalize(() => {
        this.faviconUploading = false;
      })
    ).subscribe({
      next: (event) => {
        if (event.type === HttpEventType.UploadProgress && event.total) {
          this.uploadProgress = Math.round(100 * event.loaded / event.total);
        } else if (event.type === HttpEventType.Response) {
          this.faviconUploadSuccess = true;
          this.faviconPreviewUrl = event.body.url;
          this.toastService.showSuccess('Favicon uploaded successfully');
        }
      },
      error: (error) => {
        this.faviconUploadError = 'Failed to upload favicon';
        this.toastService.showError('Failed to upload favicon');
        console.error('Error uploading favicon:', error);
      }
    });
  }

  // Add this method to select a built-in favicon
  selectBuiltInFavicon(iconUrl: string): void {
    this.faviconPreviewUrl = iconUrl;
    this.faviconFile = null; // Clear any uploaded file
    this.faviconUploadSuccess = false;
    this.faviconUploadError = null;
    this.closeFaviconPopup();
  }

  openFaviconPopup(): void {
    this.showFaviconPopup = true;
  }

  closeFaviconPopup(): void {
    this.showFaviconPopup = false;
  }
}
