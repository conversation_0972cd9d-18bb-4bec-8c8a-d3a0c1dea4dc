# Currency Settings Component - Demo Guide

## Tính năng mới đã được implement

### 🔧 Backend Features
1. **Global Currency Sync Control**: Bật/tắt sync tự động cho toàn bộ tenant
2. **Payment Integration**: Checkbox để sync với payment system (placeholder)
3. **Individual Currency Sync**: Control sync cho từng currency riêng biệt
4. **Manual Sync Trigger**: API endpoint để trigger sync job thủ công
5. **Exchange Rate Manual Edit**: <PERSON><PERSON> thể edit rates khi sync bị tắt
6. **Last Sync Tracking**: Track thời gian sync cuối cùng

### 🎨 Frontend Features
1. **Modern Table UI**: Thay thế popup bằng table layout thân thiện
2. **Toggle Switches**: Intuitive controls cho sync settings
3. **Search Functionality**: Tìm kiếm currencies
4. **Responsive Design**: Mobile-friendly
5. **Real-time Updates**: Instant feedback khi thay đổi settings
6. **Loading States**: Clear feedback cho user actions

## Cách sử dụng

### 1. Global Sync Settings
- **Enable Currency Sync**: Bật/tắt sync tự động cho tất cả currencies
- **Sync with Payment**: Tích hợp với payment system (tương lai)
- **Sync Now Button**: Trigger manual sync ngay lập tức

### 2. Currency Management
- **Search**: Tìm kiếm currencies trong danh sách
- **Exchange Rate**: Edit rates khi sync bị tắt
- **Individual Sync**: Bật/tắt sync cho từng currency
- **Last Sync**: Xem thời gian sync cuối cùng

### 3. Logic hoạt động
- Khi **Global Sync ON**: 
  - Rates được sync tự động
  - Không thể edit rates manual
  - Individual sync controls hoạt động
  
- Khi **Global Sync OFF**:
  - Có thể edit rates manual
  - Individual sync controls bị disable
  - Manual sync button bị disable

- Khi **Individual Sync OFF**:
  - Currency đó không được sync
  - Có thể edit rate manual cho currency đó

## API Endpoints

### GET /v1/tenant-currencies
Lấy thông tin currencies và sync settings

### PUT /v1/tenant-currencies
Cập nhật currency settings và sync options

### POST /v1/tenant-currencies/sync
Trigger manual currency sync job

## Database Schema

### Tenant Table
- `currency_sync_enabled`: Boolean - Global sync control
- `sync_with_payment`: Boolean - Payment integration flag
- `last_currency_sync`: Timestamp - Last sync time

### Currency Table
- `sync_enabled`: Boolean - Individual currency sync control
- `last_sync`: Timestamp - Last sync time for this currency

## Translation Keys Cần Thêm

```json
{
  "Currency Settings": "Cài đặt tiền tệ",
  "Manage currencies and synchronization settings": "Quản lý tiền tệ và cài đặt đồng bộ",
  "Synchronization Settings": "Cài đặt đồng bộ",
  "Configure automatic currency rate updates": "Cấu hình cập nhật tỷ giá tự động",
  "Sync Now": "Đồng bộ ngay",
  "Syncing...": "Đang đồng bộ...",
  "Enable Currency Sync": "Bật đồng bộ tiền tệ",
  "Automatically update exchange rates": "Tự động cập nhật tỷ giá",
  "Sync with Payment": "Đồng bộ với thanh toán",
  "Synchronize with payment system": "Đồng bộ với hệ thống thanh toán",
  "Last synced": "Đồng bộ lần cuối",
  "Search currencies...": "Tìm kiếm tiền tệ...",
  "Available Currencies": "Tiền tệ khả dụng",
  "Manage currency rates and sync settings": "Quản lý tỷ giá và cài đặt đồng bộ",
  "Exchange Rate": "Tỷ giá",
  "Sync": "Đồng bộ",
  "Last Sync": "Đồng bộ cuối",
  "Actions": "Thao tác",
  "Base": "Cơ sở",
  "Always synced": "Luôn đồng bộ",
  "Rate is automatically synced": "Tỷ giá được đồng bộ tự động",
  "Update": "Cập nhật",
  "Loading currencies...": "Đang tải tiền tệ...",
  "No currencies found": "Không tìm thấy tiền tệ",
  "Never": "Chưa bao giờ"
}
```

## Testing Checklist

- [ ] Global sync toggle hoạt động
- [ ] Payment sync toggle hoạt động  
- [ ] Individual currency sync toggles hoạt động
- [ ] Manual sync button trigger API
- [ ] Exchange rate editing khi sync tắt
- [ ] Search functionality
- [ ] Responsive design trên mobile
- [ ] Loading states hiển thị đúng
- [ ] Error handling với toast messages
- [ ] Last sync time hiển thị đúng format
