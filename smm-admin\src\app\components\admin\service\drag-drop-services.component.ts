import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AdminServiceService } from '../../../core/services/admin-service.service';
import { SuperPlatformRes } from '../../../model/response/super-platform.model';
import { SuperCategoryRes } from '../../../model/response/super-category.model';
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';
import { TranslateModule } from '@ngx-translate/core';
import { ServiceLabelComponent } from '../../common/service-label/service-label.component';
import { SocialIconComponent } from '../../common/social-icon/social-icon.component';
import { IconName } from '@fortawesome/fontawesome-svg-core';
import { Router } from '@angular/router';
import { IconsModule } from '../../../icons/icons.module';

// Extended interface for categories with platform info
interface ExtendedCategoryRes extends SuperCategoryRes {
  platformIcon?: string;
  isAllPlatforms?: boolean;
  isAllCategories?: boolean;
}

@Component({
  selector: 'app-drag-drop-services',
  standalone: true,
  imports: [
    CommonModule,
    IconsModule,
    TranslateModule,
    ServiceLabelComponent,
    SocialIconComponent
  ],
  templateUrl: './drag-drop-services.component.html',
  styleUrls: ['./drag-drop-services.component.css']
})
export class DragDropServicesComponent implements OnInit {
  platforms: SuperPlatformRes[] = [];
  categories: ExtendedCategoryRes[] = [];
  servicesByCategory: { [categoryId: string]: SuperGeneralSvRes[] } = {};
  loading = true;

  // Drag and drop state
  draggedService: SuperGeneralSvRes | null = null;
  draggedServiceIndex: number = -1;
  draggedServiceCategoryId: string = '';
  dropTargetCategoryId: string = '';
  dropTargetIndex: number = -1;

  constructor(
    private adminService: AdminServiceService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadData();
  }

  loadData(): void {
    this.loading = true;
    this.adminService.getPlatformsWithServices().subscribe({
      next: (platforms) => {
        this.platforms = platforms;

        // Extract all categories
        const allCategories: ExtendedCategoryRes[] = [];
        platforms.forEach(platform => {
          // Sort categories by sort field before processing them
          const sortedCategories = [...platform.categories].sort((a, b) => a.sort - b.sort);

          sortedCategories.forEach(category => {
            // Add platform info to category for display purposes
            const categoryWithPlatform: ExtendedCategoryRes = {
              ...category,
              platformName: platform.name,
              platformIcon: platform.icon
            };
            allCategories.push(categoryWithPlatform);
          });
        });
        this.categories = allCategories.filter(category => !category.hide);

        // Subscribe to services by category
        this.adminService.servicesByCategory$.subscribe(servicesByCategory => {
          this.servicesByCategory = servicesByCategory;
          this.loading = false;
        });
      },
      error: (error) => {
        console.error('Error loading platforms:', error);
        this.loading = false;
      }
    });
  }

  // Map service to GService format for display

  // Drag and drop event handlers
  onDragStart(event: DragEvent, service: SuperGeneralSvRes, index: number, categoryId: string): void {
    if (!event.dataTransfer) return;

    this.draggedService = service;
    this.draggedServiceIndex = index;
    this.draggedServiceCategoryId = categoryId;

    // Set data for the drag operation
    event.dataTransfer.setData('text/plain', JSON.stringify({
      serviceId: service.id,
      categoryId: categoryId,
      index: index
    }));

    // Add a class to the dragged element for styling
    const element = event.target as HTMLElement;
    element.classList.add('dragging');

    // Set the drag image to be the entire row
    event.dataTransfer.effectAllowed = 'move';
  }

  onDragOver(event: DragEvent, categoryId: string, index: number): void {
    event.preventDefault();

    // Update the drop target information
    this.dropTargetCategoryId = categoryId;
    this.dropTargetIndex = index;

    // Add visual indication of drop target
    const element = event.target as HTMLElement;
    const dropZone = element.closest('.service-row') as HTMLElement;

    if (dropZone) {
      // Remove highlight from all potential drop zones
      document.querySelectorAll('.drop-target').forEach(el => {
        el.classList.remove('drop-target');
      });

      // Add highlight to current drop zone
      dropZone.classList.add('drop-target');
    }
  }

  onDragEnter(event: DragEvent): void {
    event.preventDefault();
    const element = event.target as HTMLElement;
    const dropZone = element.closest('.service-row') as HTMLElement;

    if (dropZone) {
      dropZone.classList.add('drag-enter');
    }
  }

  onDragLeave(event: DragEvent): void {
    const element = event.target as HTMLElement;
    const dropZone = element.closest('.service-row') as HTMLElement;

    if (dropZone) {
      dropZone.classList.remove('drag-enter');
    }
  }

  onDrop(event: DragEvent, categoryId: string, index: number): void {
    event.preventDefault();

    // Remove all drag-related classes
    document.querySelectorAll('.dragging, .drop-target, .drag-enter').forEach(el => {
      el.classList.remove('dragging', 'drop-target', 'drag-enter');
    });

    // Ensure we have a dragged service
    if (!this.draggedService) return;

    // Handle the drop
    this.handleServiceDrop(this.draggedServiceCategoryId, categoryId, this.draggedServiceIndex, index);

    // Reset drag state
    this.draggedService = null;
    this.draggedServiceIndex = -1;
    this.draggedServiceCategoryId = '';
    this.dropTargetCategoryId = '';
    this.dropTargetIndex = -1;
  }

  onDragEnd(event: DragEvent): void {
    // Remove all drag-related classes
    document.querySelectorAll('.dragging, .drop-target, .drag-enter').forEach(el => {
      el.classList.remove('dragging', 'drop-target', 'drag-enter');
    });

    // Reset drag state
    this.draggedService = null;
    this.draggedServiceIndex = -1;
    this.draggedServiceCategoryId = '';
    this.dropTargetCategoryId = '';
    this.dropTargetIndex = -1;
  }

  // Handle the actual reordering of services
  handleServiceDrop(sourceCategoryId: string, targetCategoryId: string, sourceIndex: number, targetIndex: number): void {
    console.log(`Moving service from category ${sourceCategoryId}, index ${sourceIndex} to category ${targetCategoryId}, index ${targetIndex}`);

    // If source and target categories are the same, reorder within the category
    if (sourceCategoryId === targetCategoryId) {
      // Adjust the target index if needed
      // When moving an item down, the target index needs to be adjusted because
      // the item was already removed from the array
      const adjustedTargetIndex = targetIndex > sourceIndex ? targetIndex - 1 : targetIndex;

      // Use the AdminServiceService to reorder the service
      this.adminService.reorderServiceInCategory(sourceCategoryId, sourceIndex, adjustedTargetIndex);
    } else {
      // If source and target categories are different, move the service to the new category
      this.adminService.moveServiceToCategory(sourceCategoryId, targetCategoryId, sourceIndex, targetIndex);
    }

    console.log('Service moved successfully');
  }

  // Navigate back to the main services page
  navigateToServices(): void {
    this.router.navigate(['/panel/services']);
  }
}
