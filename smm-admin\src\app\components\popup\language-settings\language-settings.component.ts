import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';

import { ToastService } from '../../../core/services/toast.service';
import { TenantSettingsService } from '../../../core/services/tenant-settings.service';
import { TenantLanguageSettingsRes } from '../../../model/response/tenant-language-settings-res.model';
import { TenantLanguageSettingsReq } from '../../../model/request/tenant-language-settings-req.model';
import { LanguageSelectorComponent } from '../language-selector/language-selector.component';

@Component({
  selector: 'app-language-settings',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IconsModule,
    LanguageSelectorComponent
  ],
  templateUrl: './language-settings.component.html',
  styleUrl: './language-settings.component.css'
})
export class LanguageSettingsComponent implements OnInit {
  @Output() close = new EventEmitter<void>();

  isLoading = false;
  selectedLanguage = 'vi';
  selectedAvailableLanguages: string[] = ['vi', 'en'];
  showLanguageSelector = false;

  allLanguages = [
    { code: 'ad', name: 'Andorra', flag: 'fi fi-ad' },
    { code: 'ae', name: 'United Arab Emirates', flag: 'fi fi-ae' },
    { code: 'af', name: 'Afghanistan', flag: 'fi fi-af' },
    { code: 'ag', name: 'Antigua and Barbuda', flag: 'fi fi-ag' },
    { code: 'ai', name: 'Anguilla', flag: 'fi fi-ai' },
    { code: 'al', name: 'Albania', flag: 'fi fi-al' },
    { code: 'am', name: 'Armenia', flag: 'fi fi-am' },
    { code: 'ao', name: 'Angola', flag: 'fi fi-ao' },
    { code: 'aq', name: 'Antarctica', flag: 'fi fi-aq' },
    { code: 'ar', name: 'Argentina', flag: 'fi fi-ar' },
    { code: 'as', name: 'American Samoa', flag: 'fi fi-as' },
    { code: 'at', name: 'Austria', flag: 'fi fi-at' },
    { code: 'au', name: 'Australia', flag: 'fi fi-au' },
    { code: 'aw', name: 'Aruba', flag: 'fi fi-aw' },
    { code: 'ax', name: 'Åland Islands', flag: 'fi fi-ax' },
    { code: 'az', name: 'Azerbaijan', flag: 'fi fi-az' },
    { code: 'ba', name: 'Bosnia and Herzegovina', flag: 'fi fi-ba' },
    { code: 'bb', name: 'Barbados', flag: 'fi fi-bb' },
    { code: 'bd', name: 'Bangladesh', flag: 'fi fi-bd' },
    { code: 'be', name: 'Belgium', flag: 'fi fi-be' },
    { code: 'bf', name: 'Burkina Faso', flag: 'fi fi-bf' },
    { code: 'bg', name: 'Bulgaria', flag: 'fi fi-bg' },
    { code: 'bh', name: 'Bahrain', flag: 'fi fi-bh' },
    { code: 'bi', name: 'Burundi', flag: 'fi fi-bi' },
    { code: 'bj', name: 'Benin', flag: 'fi fi-bj' },
    { code: 'bl', name: 'Saint Barthélemy', flag: 'fi fi-bl' },
    { code: 'bm', name: 'Bermuda', flag: 'fi fi-bm' },
    { code: 'bn', name: 'Brunei', flag: 'fi fi-bn' },
    { code: 'bo', name: 'Bolivia', flag: 'fi fi-bo' },
    { code: 'bq', name: 'Caribbean Netherlands', flag: 'fi fi-bq' },
    { code: 'br', name: 'Brazil', flag: 'fi fi-br' },
    { code: 'bs', name: 'Bahamas', flag: 'fi fi-bs' },
    { code: 'bt', name: 'Bhutan', flag: 'fi fi-bt' },
    { code: 'bv', name: 'Bouvet Island', flag: 'fi fi-bv' },
    { code: 'bw', name: 'Botswana', flag: 'fi fi-bw' },
    { code: 'by', name: 'Belarus', flag: 'fi fi-by' },
    { code: 'bz', name: 'Belize', flag: 'fi fi-bz' },
    { code: 'ca', name: 'Canada', flag: 'fi fi-ca' },
    { code: 'cc', name: 'Cocos Islands', flag: 'fi fi-cc' },
    { code: 'cd', name: 'Democratic Republic of the Congo', flag: 'fi fi-cd' },
    { code: 'cf', name: 'Central African Republic', flag: 'fi fi-cf' },
    { code: 'cg', name: 'Republic of the Congo', flag: 'fi fi-cg' },
    { code: 'ch', name: 'Switzerland', flag: 'fi fi-ch' },
    { code: 'ci', name: 'Côte d\'Ivoire', flag: 'fi fi-ci' },
    { code: 'ck', name: 'Cook Islands', flag: 'fi fi-ck' },
    { code: 'cl', name: 'Chile', flag: 'fi fi-cl' },
    { code: 'cm', name: 'Cameroon', flag: 'fi fi-cm' },
    { code: 'cn', name: 'China', flag: 'fi fi-cn' },
    { code: 'co', name: 'Colombia', flag: 'fi fi-co' },
    { code: 'cr', name: 'Costa Rica', flag: 'fi fi-cr' },
    { code: 'cu', name: 'Cuba', flag: 'fi fi-cu' },
    { code: 'cv', name: 'Cape Verde', flag: 'fi fi-cv' },
    { code: 'cw', name: 'Curaçao', flag: 'fi fi-cw' },
    { code: 'cx', name: 'Christmas Island', flag: 'fi fi-cx' },
    { code: 'cy', name: 'Cyprus', flag: 'fi fi-cy' },
    { code: 'cz', name: 'Czech Republic', flag: 'fi fi-cz' },
    { code: 'de', name: 'Germany', flag: 'fi fi-de' },
    { code: 'dj', name: 'Djibouti', flag: 'fi fi-dj' },
    { code: 'dk', name: 'Denmark', flag: 'fi fi-dk' },
    { code: 'dm', name: 'Dominica', flag: 'fi fi-dm' },
    { code: 'do', name: 'Dominican Republic', flag: 'fi fi-do' },
    { code: 'dz', name: 'Algeria', flag: 'fi fi-dz' },
    { code: 'ec', name: 'Ecuador', flag: 'fi fi-ec' },
    { code: 'ee', name: 'Estonia', flag: 'fi fi-ee' },
    { code: 'eg', name: 'Egypt', flag: 'fi fi-eg' },
    { code: 'eh', name: 'Western Sahara', flag: 'fi fi-eh' },
    { code: 'er', name: 'Eritrea', flag: 'fi fi-er' },
    { code: 'es', name: 'Spain', flag: 'fi fi-es' },
    { code: 'et', name: 'Ethiopia', flag: 'fi fi-et' },
    { code: 'fi', name: 'Finland', flag: 'fi fi-fi' },
    { code: 'fj', name: 'Fiji', flag: 'fi fi-fj' },
    { code: 'fk', name: 'Falkland Islands', flag: 'fi fi-fk' },
    { code: 'fm', name: 'Micronesia', flag: 'fi fi-fm' },
    { code: 'fo', name: 'Faroe Islands', flag: 'fi fi-fo' },
    { code: 'fr', name: 'France', flag: 'fi fi-fr' },
    { code: 'ga', name: 'Gabon', flag: 'fi fi-ga' },
    { code: 'gb', name: 'United Kingdom', flag: 'fi fi-gb' },
    { code: 'gd', name: 'Grenada', flag: 'fi fi-gd' },
    { code: 'ge', name: 'Georgia', flag: 'fi fi-ge' },
    { code: 'gf', name: 'French Guiana', flag: 'fi fi-gf' },
    { code: 'gg', name: 'Guernsey', flag: 'fi fi-gg' },
    { code: 'gh', name: 'Ghana', flag: 'fi fi-gh' },
    { code: 'gi', name: 'Gibraltar', flag: 'fi fi-gi' },
    { code: 'gl', name: 'Greenland', flag: 'fi fi-gl' },
    { code: 'gm', name: 'Gambia', flag: 'fi fi-gm' },
    { code: 'gn', name: 'Guinea', flag: 'fi fi-gn' },
    { code: 'gp', name: 'Guadeloupe', flag: 'fi fi-gp' },
    { code: 'gq', name: 'Equatorial Guinea', flag: 'fi fi-gq' },
    { code: 'gr', name: 'Greece', flag: 'fi fi-gr' },
    { code: 'gs', name: 'South Georgia', flag: 'fi fi-gs' },
    { code: 'gt', name: 'Guatemala', flag: 'fi fi-gt' },
    { code: 'gu', name: 'Guam', flag: 'fi fi-gu' },
    { code: 'gw', name: 'Guinea-Bissau', flag: 'fi fi-gw' },
    { code: 'gy', name: 'Guyana', flag: 'fi fi-gy' },
    { code: 'hk', name: 'Hong Kong', flag: 'fi fi-hk' },
    { code: 'hm', name: 'Heard Island', flag: 'fi fi-hm' },
    { code: 'hn', name: 'Honduras', flag: 'fi fi-hn' },
    { code: 'hr', name: 'Croatia', flag: 'fi fi-hr' },
    { code: 'ht', name: 'Haiti', flag: 'fi fi-ht' },
    { code: 'hu', name: 'Hungary', flag: 'fi fi-hu' },
    { code: 'id', name: 'Indonesia', flag: 'fi fi-id' },
    { code: 'ie', name: 'Ireland', flag: 'fi fi-ie' },
    { code: 'il', name: 'Israel', flag: 'fi fi-il' },
    { code: 'im', name: 'Isle of Man', flag: 'fi fi-im' },
    { code: 'in', name: 'India', flag: 'fi fi-in' },
    { code: 'io', name: 'British Indian Ocean Territory', flag: 'fi fi-io' },
    { code: 'iq', name: 'Iraq', flag: 'fi fi-iq' },
    { code: 'ir', name: 'Iran', flag: 'fi fi-ir' },
    { code: 'is', name: 'Iceland', flag: 'fi fi-is' },
    { code: 'it', name: 'Italy', flag: 'fi fi-it' },
    { code: 'je', name: 'Jersey', flag: 'fi fi-je' },
    { code: 'jm', name: 'Jamaica', flag: 'fi fi-jm' },
    { code: 'jo', name: 'Jordan', flag: 'fi fi-jo' },
    { code: 'jp', name: 'Japan', flag: 'fi fi-jp' },
    { code: 'ke', name: 'Kenya', flag: 'fi fi-ke' },
    { code: 'kg', name: 'Kyrgyzstan', flag: 'fi fi-kg' },
    { code: 'kh', name: 'Cambodia', flag: 'fi fi-kh' },
    { code: 'ki', name: 'Kiribati', flag: 'fi fi-ki' },
    { code: 'km', name: 'Comoros', flag: 'fi fi-km' },
    { code: 'kn', name: 'Saint Kitts and Nevis', flag: 'fi fi-kn' },
    { code: 'kp', name: 'North Korea', flag: 'fi fi-kp' },
    { code: 'kr', name: 'South Korea', flag: 'fi fi-kr' },
    { code: 'kw', name: 'Kuwait', flag: 'fi fi-kw' },
    { code: 'ky', name: 'Cayman Islands', flag: 'fi fi-ky' },
    { code: 'kz', name: 'Kazakhstan', flag: 'fi fi-kz' },
    { code: 'la', name: 'Laos', flag: 'fi fi-la' },
    { code: 'lb', name: 'Lebanon', flag: 'fi fi-lb' },
    { code: 'lc', name: 'Saint Lucia', flag: 'fi fi-lc' },
    { code: 'li', name: 'Liechtenstein', flag: 'fi fi-li' },
    { code: 'lk', name: 'Sri Lanka', flag: 'fi fi-lk' },
    { code: 'lr', name: 'Liberia', flag: 'fi fi-lr' },
    { code: 'ls', name: 'Lesotho', flag: 'fi fi-ls' },
    { code: 'lt', name: 'Lithuania', flag: 'fi fi-lt' },
    { code: 'lu', name: 'Luxembourg', flag: 'fi fi-lu' },
    { code: 'lv', name: 'Latvia', flag: 'fi fi-lv' },
    { code: 'ly', name: 'Libya', flag: 'fi fi-ly' },
    { code: 'ma', name: 'Morocco', flag: 'fi fi-ma' },
    { code: 'mc', name: 'Monaco', flag: 'fi fi-mc' },
    { code: 'md', name: 'Moldova', flag: 'fi fi-md' },
    { code: 'me', name: 'Montenegro', flag: 'fi fi-me' },
    { code: 'mf', name: 'Saint Martin', flag: 'fi fi-mf' },
    { code: 'mg', name: 'Madagascar', flag: 'fi fi-mg' },
    { code: 'mh', name: 'Marshall Islands', flag: 'fi fi-mh' },
    { code: 'mk', name: 'North Macedonia', flag: 'fi fi-mk' },
    { code: 'ml', name: 'Mali', flag: 'fi fi-ml' },
    { code: 'mm', name: 'Myanmar', flag: 'fi fi-mm' },
    { code: 'mn', name: 'Mongolia', flag: 'fi fi-mn' },
    { code: 'mo', name: 'Macao', flag: 'fi fi-mo' },
    { code: 'mp', name: 'Northern Mariana Islands', flag: 'fi fi-mp' },
    { code: 'mq', name: 'Martinique', flag: 'fi fi-mq' },
    { code: 'mr', name: 'Mauritania', flag: 'fi fi-mr' },
    { code: 'ms', name: 'Montserrat', flag: 'fi fi-ms' },
    { code: 'mt', name: 'Malta', flag: 'fi fi-mt' },
    { code: 'mu', name: 'Mauritius', flag: 'fi fi-mu' },
    { code: 'mv', name: 'Maldives', flag: 'fi fi-mv' },
    { code: 'mw', name: 'Malawi', flag: 'fi fi-mw' },
    { code: 'mx', name: 'Mexico', flag: 'fi fi-mx' },
    { code: 'my', name: 'Malaysia', flag: 'fi fi-my' },
    { code: 'mz', name: 'Mozambique', flag: 'fi fi-mz' },
    { code: 'na', name: 'Namibia', flag: 'fi fi-na' },
    { code: 'nc', name: 'New Caledonia', flag: 'fi fi-nc' },
    { code: 'ne', name: 'Niger', flag: 'fi fi-ne' },
    { code: 'nf', name: 'Norfolk Island', flag: 'fi fi-nf' },
    { code: 'ng', name: 'Nigeria', flag: 'fi fi-ng' },
    { code: 'ni', name: 'Nicaragua', flag: 'fi fi-ni' },
    { code: 'nl', name: 'Netherlands', flag: 'fi fi-nl' },
    { code: 'no', name: 'Norway', flag: 'fi fi-no' },
    { code: 'np', name: 'Nepal', flag: 'fi fi-np' },
    { code: 'nr', name: 'Nauru', flag: 'fi fi-nr' },
    { code: 'nu', name: 'Niue', flag: 'fi fi-nu' },
    { code: 'nz', name: 'New Zealand', flag: 'fi fi-nz' },
    { code: 'om', name: 'Oman', flag: 'fi fi-om' },
    { code: 'pa', name: 'Panama', flag: 'fi fi-pa' },
    { code: 'pe', name: 'Peru', flag: 'fi fi-pe' },
    { code: 'pf', name: 'French Polynesia', flag: 'fi fi-pf' },
    { code: 'pg', name: 'Papua New Guinea', flag: 'fi fi-pg' },
    { code: 'ph', name: 'Philippines', flag: 'fi fi-ph' },
    { code: 'pk', name: 'Pakistan', flag: 'fi fi-pk' },
    { code: 'pl', name: 'Poland', flag: 'fi fi-pl' },
    { code: 'pm', name: 'Saint Pierre and Miquelon', flag: 'fi fi-pm' },
    { code: 'pn', name: 'Pitcairn Islands', flag: 'fi fi-pn' },
    { code: 'pr', name: 'Puerto Rico', flag: 'fi fi-pr' },
    { code: 'ps', name: 'Palestine', flag: 'fi fi-ps' },
    { code: 'pt', name: 'Portugal', flag: 'fi fi-pt' },
    { code: 'pw', name: 'Palau', flag: 'fi fi-pw' },
    { code: 'py', name: 'Paraguay', flag: 'fi fi-py' },
    { code: 'qa', name: 'Qatar', flag: 'fi fi-qa' },
    { code: 're', name: 'Réunion', flag: 'fi fi-re' },
    { code: 'ro', name: 'Romania', flag: 'fi fi-ro' },
    { code: 'rs', name: 'Serbia', flag: 'fi fi-rs' },
    { code: 'ru', name: 'Russia', flag: 'fi fi-ru' },
    { code: 'rw', name: 'Rwanda', flag: 'fi fi-rw' },
    { code: 'sa', name: 'Saudi Arabia', flag: 'fi fi-sa' },
    { code: 'sb', name: 'Solomon Islands', flag: 'fi fi-sb' },
    { code: 'sc', name: 'Seychelles', flag: 'fi fi-sc' },
    { code: 'sd', name: 'Sudan', flag: 'fi fi-sd' },
    { code: 'se', name: 'Sweden', flag: 'fi fi-se' },
    { code: 'sg', name: 'Singapore', flag: 'fi fi-sg' },
    { code: 'sh', name: 'Saint Helena', flag: 'fi fi-sh' },
    { code: 'si', name: 'Slovenia', flag: 'fi fi-si' },
    { code: 'sj', name: 'Svalbard and Jan Mayen', flag: 'fi fi-sj' },
    { code: 'sk', name: 'Slovakia', flag: 'fi fi-sk' },
    { code: 'sl', name: 'Sierra Leone', flag: 'fi fi-sl' },
    { code: 'sm', name: 'San Marino', flag: 'fi fi-sm' },
    { code: 'sn', name: 'Senegal', flag: 'fi fi-sn' },
    { code: 'so', name: 'Somalia', flag: 'fi fi-so' },
    { code: 'sr', name: 'Suriname', flag: 'fi fi-sr' },
    { code: 'ss', name: 'South Sudan', flag: 'fi fi-ss' },
    { code: 'st', name: 'São Tomé and Príncipe', flag: 'fi fi-st' },
    { code: 'sv', name: 'El Salvador', flag: 'fi fi-sv' },
    { code: 'sx', name: 'Sint Maarten', flag: 'fi fi-sx' },
    { code: 'sy', name: 'Syria', flag: 'fi fi-sy' },
    { code: 'sz', name: 'Eswatini', flag: 'fi fi-sz' },
    { code: 'tc', name: 'Turks and Caicos Islands', flag: 'fi fi-tc' },
    { code: 'td', name: 'Chad', flag: 'fi fi-td' },
    { code: 'tf', name: 'French Southern Territories', flag: 'fi fi-tf' },
    { code: 'tg', name: 'Togo', flag: 'fi fi-tg' },
    { code: 'th', name: 'Thailand', flag: 'fi fi-th' },
    { code: 'tj', name: 'Tajikistan', flag: 'fi fi-tj' },
    { code: 'tk', name: 'Tokelau', flag: 'fi fi-tk' },
    { code: 'tl', name: 'Timor-Leste', flag: 'fi fi-tl' },
    { code: 'tm', name: 'Turkmenistan', flag: 'fi fi-tm' },
    { code: 'tn', name: 'Tunisia', flag: 'fi fi-tn' },
    { code: 'to', name: 'Tonga', flag: 'fi fi-to' },
    { code: 'tr', name: 'Turkey', flag: 'fi fi-tr' },
    { code: 'tt', name: 'Trinidad and Tobago', flag: 'fi fi-tt' },
    { code: 'tv', name: 'Tuvalu', flag: 'fi fi-tv' },
    { code: 'tw', name: 'Taiwan', flag: 'fi fi-tw' },
    { code: 'tz', name: 'Tanzania', flag: 'fi fi-tz' },
    { code: 'ua', name: 'Ukraine', flag: 'fi fi-ua' },
    { code: 'ug', name: 'Uganda', flag: 'fi fi-ug' },
    { code: 'um', name: 'U.S. Minor Outlying Islands', flag: 'fi fi-um' },
    { code: 'en', name: 'English', flag: 'fi fi-us' },
    { code: 'uy', name: 'Uruguay', flag: 'fi fi-uy' },
    { code: 'uz', name: 'Uzbekistan', flag: 'fi fi-uz' },
    { code: 'va', name: 'Vatican City', flag: 'fi fi-va' },
    { code: 'vc', name: 'Saint Vincent and the Grenadines', flag: 'fi fi-vc' },
    { code: 've', name: 'Venezuela', flag: 'fi fi-ve' },
    { code: 'vg', name: 'British Virgin Islands', flag: 'fi fi-vg' },
    { code: 'vi', name: 'Tiếng Việt', flag: 'fi fi-vn' },
    { code: 'vu', name: 'Vanuatu', flag: 'fi fi-vu' },
    { code: 'wf', name: 'Wallis and Futuna', flag: 'fi fi-wf' },
    { code: 'ws', name: 'Samoa', flag: 'fi fi-ws' },
    { code: 'ye', name: 'Yemen', flag: 'fi fi-ye' },
    { code: 'yt', name: 'Mayotte', flag: 'fi fi-yt' },
    { code: 'za', name: 'South Africa', flag: 'fi fi-za' },
    { code: 'zm', name: 'Zambia', flag: 'fi fi-zm' },
    { code: 'zw', name: 'Zimbabwe', flag: 'fi fi-zw' }
  ];

  constructor(
    private tenantSettingsService: TenantSettingsService,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    this.loadCurrentSettings();
  }

  loadCurrentSettings(): void {
    this.isLoading = true;
    this.tenantSettingsService.getLanguageSettings().subscribe({
      next: (response: TenantLanguageSettingsRes) => {
        this.selectedLanguage = response.default_language || 'vi';
        this.selectedAvailableLanguages = response.available_languages || ['vi', 'en'];
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading language settings:', error);
        this.toastService.showError(error?.message || 'Failed to load language settings');
        this.isLoading = false;
      }
    });
  }

  selectLanguage(languageCode: string): void {
    this.selectedLanguage = languageCode;
  }

  addLanguage(languageCode: string): void {
    if (!this.selectedAvailableLanguages.includes(languageCode)) {
      this.selectedAvailableLanguages.push(languageCode);
      // If this is the first language, make it default
      if (this.selectedAvailableLanguages.length === 1) {
        this.selectedLanguage = languageCode;
      }
    }
    this.showLanguageSelector = false;
  }

  removeLanguage(languageCode: string): void {
    if (this.selectedAvailableLanguages.length <= 1) {
      this.toastService.showError('At least one language must be selected');
      return;
    }

    const index = this.selectedAvailableLanguages.indexOf(languageCode);
    if (index > -1) {
      this.selectedAvailableLanguages.splice(index, 1);
      // If removed language was the default, reset default to first available
      if (this.selectedLanguage === languageCode && this.selectedAvailableLanguages.length > 0) {
        this.selectedLanguage = this.selectedAvailableLanguages[0];
      }
    }
  }

  getLanguageByCode(code: string) {
    return this.allLanguages.find(lang => lang.code === code);
  }

  saveSettings(): void {
    if (this.selectedAvailableLanguages.length === 0) {
      this.toastService.showError('Please select at least one available language');
      return;
    }

    if (!this.selectedAvailableLanguages.includes(this.selectedLanguage)) {
      this.toastService.showError('Default language must be one of the available languages');
      return;
    }

    this.isLoading = true;

    const request: TenantLanguageSettingsReq = {
      default_language: this.selectedLanguage,
      available_languages: this.selectedAvailableLanguages
    };

    this.tenantSettingsService.updateLanguageSettings(request).subscribe({
      next: () => {
        this.toastService.showSuccess('Language settings updated successfully');
        this.isLoading = false;
        this.close.emit();
      },
      error: (error) => {
        console.error('Error updating language settings:', error);
        this.toastService.showError(error?.message || 'Failed to update language settings');
        this.isLoading = false;
      }
    });
  }

  onClose(): void {
    this.close.emit();
  }
}
