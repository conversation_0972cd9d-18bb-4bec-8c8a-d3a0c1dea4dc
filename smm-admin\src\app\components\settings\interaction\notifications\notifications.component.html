<div class="notifications-container">
  <div class="header-section">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-800">{{ 'notifications.title' | translate }}</h1>
    </div>
    <p class="text-gray-600 mb-8">{{ 'notifications.description' | translate }}</p>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="loading" class="flex justify-center items-center py-8">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)]"></div>
  </div>



  <!-- Popup Notifications Section -->
  <div class="section-container mb-8" *ngIf="!loading">
    <div class="section-header">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold text-gray-800">{{ 'notifications.popup_notifications' | translate }}</h2>
        <button
          (click)="addPopupNotification()"
          class="add-button flex items-center gap-2 bg-[var(--primary)] text-white px-4 py-2 rounded-lg hover:bg-[#2599a8] transition-colors">
          <fa-icon [icon]="['fas', 'plus']"></fa-icon>
          <span>{{ 'notifications.add_popup' | translate }}</span>
        </button>
      </div>
      <p class="text-gray-600 mb-4">{{ 'notifications.popup_description' | translate }}</p>
    </div>

    <div class="notification-list">
      <div *ngIf="popupNotifications.length === 0" class="empty-state">
        <p>{{ 'notifications.no_popup_notifications' | translate }}</p>
      </div>

      <div *ngFor="let notification of popupNotifications" class="notification-item">
        <div class="notification-content">
          <div class="notification-icon" [ngClass]="getNotificationTypeClass(notification.type)">
            <fa-icon [icon]="['fas', getNotificationTypeIcon(notification.type)]"></fa-icon>
          </div>
          <div class="notification-details">
            <h3 class="notification-title">{{ notification.title || ('notifications.notification' | translate) }}</h3>
            <div>
              <p class="notification-text" [class.collapsed]="!expandedContents[notification.id]" [innerHTML]="notification.content"></p>
              <span *ngIf="isContentLong(notification.content)" class="content-toggle" (click)="toggleContentExpansion(notification.id)">
                {{ expandedContents[notification.id] ? ('notifications.show_less' | translate) : ('notifications.show_more' | translate) }}
              </span>
            </div>
            <div class="notification-meta">
              <span class="meta-item" *ngIf="notification.offer_ending_type">
                <fa-icon [icon]="['fas', 'clock']" class="meta-icon"></fa-icon>
                {{ 'notifications.expires' | translate }}: {{ formatDate(notification.offer_end_date) }}
              </span>
              <span *ngIf="notification.only_for_customers" class="meta-item">
                <fa-icon [icon]="['fas', 'user-check']" class="meta-icon"></fa-icon>
                {{ 'notifications.only_for_customers' | translate }}
              </span>
            </div>
          </div>
        </div>
        <div class="notification-actions">
          <app-toggle-switch
            [isChecked]="notification.show"
            (toggled)="toggleNotificationStatus(notification)"
            [circleColor]="'#FFF'"
            [toggledBgColor]="'var(--primary)'">
          </app-toggle-switch>
          <button class="action-button edit" title="{{ 'Edit' | translate }}" (click)="editNotification(notification)">
            <fa-icon [icon]="['fas', 'edit']"></fa-icon>
          </button>
          <button class="action-button delete" title="{{ 'Delete' | translate }}" (click)="deleteNotification('popup', notification)">
            <fa-icon [icon]="['fas', 'trash']"></fa-icon>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Fixed Notifications Section -->
  <div class="section-container" *ngIf="!loading">
    <div class="section-header">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold text-gray-800">{{ 'notifications.fixed_notifications' | translate }}</h2>
        <button
          (click)="addFixedNotification()"
          class="add-button flex items-center gap-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
          <fa-icon [icon]="['fas', 'plus']"></fa-icon>
          <span>{{ 'notifications.add_fixed' | translate }}</span>
        </button>
      </div>
      <p class="text-gray-600 mb-4">{{ 'notifications.fixed_description' | translate }}</p>
    </div>

    <div class="notification-list">
      <div *ngIf="fixedNotifications.length === 0" class="empty-state">
        <p>{{ 'notifications.no_fixed_notifications' | translate }}</p>
      </div>

      <div *ngFor="let notification of fixedNotifications" class="notification-item">
        <div class="notification-content">
          <div class="notification-icon" [ngClass]="getNotificationTypeClass(notification.type)">
            <fa-icon [icon]="['fas', getNotificationTypeIcon(notification.type)]"></fa-icon>
          </div>
          <div class="notification-details">
            <h3 class="notification-title">{{ notification.title || ('notifications.notification' | translate) }}</h3>
            <div>
              <p class="notification-text" [class.collapsed]="!expandedContents[notification.id]" [innerHTML]="notification.content"></p>
              <span *ngIf="isContentLong(notification.content)" class="content-toggle" (click)="toggleContentExpansion(notification.id)">
                {{ expandedContents[notification.id] ? ('notifications.show_less' | translate) : ('notifications.show_more' | translate) }}
              </span>
            </div>
            <div class="notification-meta">
              <span class="meta-item" *ngIf="notification.offer_ending_type">
                <fa-icon [icon]="['fas', 'clock']" class="meta-icon"></fa-icon>
                {{ 'notifications.expires' | translate }}: {{ formatDate(notification.offer_end_date) }}
              </span>
              <span *ngIf="notification.only_for_customers" class="meta-item">
                <fa-icon [icon]="['fas', 'user-check']" class="meta-icon"></fa-icon>
                {{ 'notifications.only_for_customers' | translate }}
              </span>
            </div>
          </div>
        </div>
        <div class="notification-actions">
          <app-toggle-switch
            [isChecked]="notification.show"
            (toggled)="toggleNotificationStatus(notification)"
            [circleColor]="'#FFF'"
            [toggledBgColor]="'var(--primary)'">
          </app-toggle-switch>
          <button class="action-button edit" title="{{ 'Edit' | translate }}" (click)="editNotification(notification)">
            <fa-icon [icon]="['fas', 'edit']"></fa-icon>
          </button>
          <button class="action-button delete" title="{{ 'Delete' | translate }}" (click)="deleteNotification('fixed', notification)">
            <fa-icon [icon]="['fas', 'trash']"></fa-icon>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<app-delete-confirmation
  *ngIf="showDeleteConfirmation && notificationToDelete"
  [itemName]="notificationToDelete.title || ('notifications.this_notification' | translate)"
  [isLoading]="isDeleting"
  (close)="closeDeleteConfirmation()"
  (confirm)="confirmDeleteNotification()">
</app-delete-confirmation>