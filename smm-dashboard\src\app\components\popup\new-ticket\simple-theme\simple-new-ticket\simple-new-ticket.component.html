<div class="simple-overlay" (click)="onOverlayClick($event)">
  <div class="simple-modal">
    <!-- Header -->
    <div class="modal-header">
      <h2 class="modal-title">{{ 'ticket.new_ticket' | translate }}</h2>
      <button (click)="onClose()" class="close-btn">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    </div>

    <!-- Content -->
    <div class="modal-content">
      <!-- Error message -->
      <div *ngIf="submitError" class="error-message">
        {{ submitError }}
      </div>

      <!-- Ticket Type Dropdown -->
      <div class="form-group">
        <label class="form-label">{{ 'ticket.type' | translate }}</label>
        <app-lite-dropdown
          [options]="translatedOptions"
          (selected)="selectOption($event)"
          [customClassDropdown]="'simple-dropdown'"
        ></app-lite-dropdown>
      </div>

      <!-- Order Form -->
      <div class="form-group" *ngIf="selectedForm === 'Order'">
        <label class="form-label">{{ 'ticket.orders_id' | translate }}</label>
        <input
          [(ngModel)]="orderId"
          placeholder="{{ 'ticket.orders_id' | translate }}"
          class="simple-input"
        />
        <app-lite-dropdown
          [options]="orderOptions"
          (selected)="selectOrderOption($event)"
          [customClassDropdown]="'simple-dropdown'"
        ></app-lite-dropdown>
      </div>

      <!-- Payment Form -->
      <div class="form-group" *ngIf="selectedForm === 'Payment'">
        <label class="form-label">{{ 'ticket.payment' | translate }}</label>
        <app-lite-dropdown
          [customClassDropdown]="'simple-dropdown'"
          [options]="paymentOptions"
          (selected)="selectPaymentOption($event)"
        ></app-lite-dropdown>
      </div>

      <!-- Request Form -->
      <div class="form-group" *ngIf="selectedForm === 'Request'">
        <label class="form-label">{{ 'ticket.request' | translate }}</label>
        <app-lite-dropdown
          [customClassDropdown]="'simple-dropdown'"
          [options]="requestOptions"
          (selected)="selectRequestOption($event)"
        ></app-lite-dropdown>
      </div>

      <!-- Point Form -->
      <div class="form-group" *ngIf="selectedForm === 'Point'">
        <label class="form-label">{{ 'ticket.point' | translate }}</label>
        <app-lite-dropdown
          [customClassDropdown]="'simple-dropdown'"
          [options]="pointOptions"
          (selected)="selectPointOption($event)"
        ></app-lite-dropdown>
      </div>

      <!-- Message -->
      <div class="form-group">
        <label class="form-label">{{ 'ticket.message' | translate }}</label>
        <textarea
          [(ngModel)]="message"
          class="simple-textarea"
          rows="6"
          placeholder="{{ 'ticket.message' | translate }}"
        ></textarea>
      </div>

      <!-- Submit Button -->
      <button
        [disabled]="isSubmitting"
        (click)="onSubmit()"
        class="simple-submit-btn"
      >
        <span *ngIf="isSubmitting" class="loading-spinner">
          <svg class="spinner" viewBox="0 0 24 24">
            <circle class="path" cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="4"></circle>
          </svg>
        </span>
        {{ 'ticket.submit_now' | translate }}
      </button>
    </div>
  </div>
</div>
