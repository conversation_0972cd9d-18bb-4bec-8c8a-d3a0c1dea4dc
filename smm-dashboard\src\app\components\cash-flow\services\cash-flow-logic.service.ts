import { Injectable } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { BehaviorSubject, Subscription } from 'rxjs';

// Services
import { TransactionService } from '../../../core/services/transaction.service';
import { CurrencyService } from '../../../core/services/currency.service';
import { ThemeService, LayoutTheme } from '../../../core/services/theme.service';

// Models
import { MyTransactionRes, TransactionSearchReq } from '../../../model/response/my-transaction.model';
import { TransactionFilterReq } from '../../../model/request/transaction-filter-req.model';

export interface CashFlowState {
  // Data
  transactions: MyTransactionRes[];
  
  // Forms
  filterForm: FormGroup;
  
  // UI state
  loading: boolean;
  
  // Pagination
  currentPage: number;
  pageSize: number;
  totalElements: number;
  totalPages: number;
  
  // Selection
  selectedTransactions: number[];
  selectAll: boolean;
  
  // Filter options
  transactionTypeOptions: string[];
  statusOptions: string[];
  dateRange: { start: Date | null; end: Date | null };
  
  // Theme management
  currentTheme: LayoutTheme;
}

@Injectable({
  providedIn: 'root'
})
export class CashFlowLogicService {
  private subscriptions: Subscription[] = [];

  // State management
  private _state$ = new BehaviorSubject<CashFlowState>({
    transactions: [],
    filterForm: this.fb.group({
      dateRange: [null],
      type: [''],
      status: [''],
      orderId: ['']
    }),
    loading: false,
    currentPage: 0,
    pageSize: 10,
    totalElements: 0,
    totalPages: 0,
    selectedTransactions: [],
    selectAll: false,
    transactionTypeOptions: [
      'All transaction type',
      'Deposit',
      'Bonus',
      'Spent',
      'Refund'
    ],
    statusOptions: [
      'All Status',
      'Completed',
      'Pending',
      'Failed',
      'Cancelled'
    ],
    dateRange: { start: null, end: null },
    currentTheme: LayoutTheme.DEFAULT
  });

  // Public state observable
  public readonly state$ = this._state$.asObservable();

  // Current state getter
  private get currentState(): CashFlowState {
    return this._state$.value;
  }

  constructor(
    private fb: FormBuilder,
    private transactionService: TransactionService,
    private currencyService: CurrencyService,
    private themeService: ThemeService
  ) {
    this.initialize();
  }

  private initialize(): void {
    // Subscribe to theme changes
    const themeSub = this.themeService.currentLayoutTheme$.subscribe((theme: LayoutTheme) => {
      this.updateState({ currentTheme: theme });
    });
    this.subscriptions.push(themeSub);

    // Load initial data
    this.loadTransactions();
  }

  private updateState(partialState: Partial<CashFlowState>): void {
    this._state$.next({ ...this.currentState, ...partialState });
  }

  // Public getters for template access
  get transactions(): MyTransactionRes[] {
    return this.currentState.transactions;
  }

  get filterForm(): FormGroup {
    return this.currentState.filterForm;
  }

  get loading(): boolean {
    return this.currentState.loading;
  }

  get currentPage(): number {
    return this.currentState.currentPage;
  }

  get pageSize(): number {
    return this.currentState.pageSize;
  }

  get totalElements(): number {
    return this.currentState.totalElements;
  }

  get totalPages(): number {
    return this.currentState.totalPages;
  }

  get selectedTransactions(): number[] {
    return this.currentState.selectedTransactions;
  }

  get selectAll(): boolean {
    return this.currentState.selectAll;
  }

  get transactionTypeOptions(): string[] {
    return this.currentState.transactionTypeOptions;
  }

  get statusOptions(): string[] {
    return this.currentState.statusOptions;
  }

  get dateRange(): { start: Date | null; end: Date | null } {
    return this.currentState.dateRange;
  }

  // Public methods for component interaction
  loadTransactions(): void {
    this.updateState({ loading: true });

    const filter: TransactionSearchReq = this.buildFilterFromForm();

    this.transactionService.searchTransactions(filter, this.currentState.currentPage, this.currentState.pageSize).subscribe({
      next: (result) => {
        this.updateState({
          transactions: result.content as MyTransactionRes[],
          currentPage: result.number,
          pageSize: result.size,
          totalElements: result.total_elements,
          totalPages: result.total_pages,
          loading: false
        });
      },
      error: (error) => {
        console.error('Error loading transactions:', error);
        this.updateState({ loading: false });
      }
    });
  }

  onSearch(): void {
    this.updateState({ currentPage: 0 });
    this.loadTransactions();
  }

  onPageChange(page: number): void {
    this.updateState({ currentPage: page });
    this.loadTransactions();
  }

  onPageSizeChange(size: number): void {
    this.updateState({ 
      pageSize: size,
      currentPage: 0 
    });
    this.loadTransactions();
  }

  onTransactionTypeSelected(type: string): void {
    this.currentState.filterForm.patchValue({ type });
  }

  onStatusSelected(status: string): void {
    this.currentState.filterForm.patchValue({ status });
  }

  onDateRangeChanged(dateRange: { start: Date | null; end: Date | null }): void {
    this.updateState({ dateRange });
    // Also update the form control to keep them in sync
    this.currentState.filterForm.patchValue({ dateRange });
  }

  toggleAllTransactions(): void {
    const selectAll = !this.currentState.selectAll;
    const selectedTransactions = selectAll 
      ? this.currentState.transactions.map(transaction => transaction.id)
      : [];
    
    this.updateState({ 
      selectAll,
      selectedTransactions 
    });
  }

  toggleTransaction(transactionId: number): void {
    const selectedTransactions = [...this.currentState.selectedTransactions];
    const index = selectedTransactions.indexOf(transactionId);
    
    if (index > -1) {
      selectedTransactions.splice(index, 1);
    } else {
      selectedTransactions.push(transactionId);
    }
    
    const selectAll = selectedTransactions.length === this.currentState.transactions.length;
    
    this.updateState({ 
      selectedTransactions,
      selectAll 
    });
  }

  isSelected(transactionId: number): boolean {
    return this.currentState.selectedTransactions.includes(transactionId);
  }

  getChangeClass(amount: number): string {
    if (amount > 0) return 'text-green';
    if (amount < 0) return 'text-red';
    return '';
  }

  getBalanceComponents(transaction: MyTransactionRes): { previous: number, change: number, current: number } {
    const previousBalance = transaction.balance - transaction.change;
    return {
      previous: previousBalance,
      change: transaction.change,
      current: transaction.balance
    };
  }

  formatNumber(value: number): string {
    return this.currencyService.formatPrice(value);
  }

  private buildFilterFromForm(): TransactionSearchReq {
    const formValues = this.currentState.filterForm.value;
    const filter: TransactionSearchReq = {};

    // Handle date range
    if (this.currentState.dateRange.start && this.currentState.dateRange.end) {
      filter.from = this.currentState.dateRange.start.toISOString().split('T')[0];
      filter.to = this.currentState.dateRange.end.toISOString().split('T')[0];
    }

    // Handle transaction type - backend expects array of types
    if (formValues.type && formValues.type !== 'All transaction type') {
      filter.types = [formValues.type];
    }

    // Handle order ID
    if (formValues.orderId) {
      filter.orderId = Number(formValues.orderId);
    }

    return filter;
  }

  // Additional methods for compatibility
  updateDateRange(dateRange: { startDate: Date | null; endDate: Date | null }): void {
    this.updateState({
      dateRange: {
        start: dateRange.startDate,
        end: dateRange.endDate
      }
    });
    // Also update the form control to keep them in sync
    this.currentState.filterForm.patchValue({ dateRange });
  }

  updateTransactionType(type: string): void {
    this.currentState.filterForm.patchValue({ type });
  }

  search(): void {
    this.updateState({ currentPage: 0 });
    this.loadTransactions();
  }

  changePage(page: number): void {
    this.onPageChange(page);
  }

  changePageSize(size: number): void {
    this.onPageSizeChange(size);
  }

  resetFilters(): void {
    // Reset form to initial values
    const resetForm = this.fb.group({
      dateRange: [null],
      type: [''],
      status: [''],
      orderId: ['']
    });

    // Reset state
    this.updateState({
      filterForm: resetForm,
      dateRange: { start: null, end: null },
      currentPage: 0,
      selectedTransactions: [],
      selectAll: false
    });

    // Reload transactions with reset filters
    this.loadTransactions();
  }

  destroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.subscriptions = [];
  }
}
