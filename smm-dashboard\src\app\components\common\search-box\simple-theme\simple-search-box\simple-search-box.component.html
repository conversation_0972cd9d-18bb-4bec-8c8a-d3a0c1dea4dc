<div class="simple-search-container" [ngClass]="containerClass">
  <div class="simple-search-wrapper">
    <input
      type="text"
      [(ngModel)]="searchValue"
      (ngModelChange)="updateSearchValue($event)"
      (keyup.enter)="search()"
      [placeholder]="placeholder"
      [ngClass]="['simple-search-input', inputClass]"
    />
    
    <button
      (click)="search()"
      [ngClass]="['simple-search-button', buttonClass]"
    >
      <span *ngIf="buttonIcon === 'search'" class="simple-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
          <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/>
        </svg>
      </span>
      <span *ngIf="buttonIcon === 'edit'" class="simple-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
          <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708L10.5 8.207l-3-3L12.146.146zM11.207 9l-3-3L2.5 11.707V13.5a.5.5 0 0 0 .5.5h1.793L11.207 9z"/>
        </svg>
      </span>
      <span *ngIf="!['search', 'edit'].includes(buttonIcon) || showButtonText" class="simple-button-text">
        {{ buttonText }}
      </span>
    </button>
  </div>
</div>
