package tndung.vnfb.smm.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
// import tndung.vnfb.smm.config.service.TimeZoneInterceptor;
import tndung.vnfb.smm.anotation.TenantCheck;
import tndung.vnfb.smm.constant.enums.OrderStatus;
import tndung.vnfb.smm.dto.ApiResponseEntity;
import tndung.vnfb.smm.dto.request.*;
import tndung.vnfb.smm.dto.response.OrderRes;
import tndung.vnfb.smm.dto.response.SuperOrderRes;
import tndung.vnfb.smm.service.OrderService;

import javax.validation.Valid;
import java.time.LocalDateTime;

//@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/v1/orders")
@RequiredArgsConstructor
public class GOrderController {
    private final OrderService orderService;

    @GetMapping("/{id}/fetch")
    @PreAuthorize("hasAnyRole('ROLE_USER' ,'ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<OrderRes> fetch(@PathVariable Long id) {
        return ApiResponseEntity.success(orderService.fetchStatus(id));
    }

    @PutMapping("/{id}/status")
    @PreAuthorize("hasAnyRole('ROLE_PANEL','ROLE_ADMIN_PANEL' )")
    @TenantCheck
    public ApiResponseEntity<OrderRes> updateStatus(
            @PathVariable Long id,
            @RequestParam OrderStatus status,
            @RequestParam(required = false) Integer remains) {
        return ApiResponseEntity.success(orderService.updateOrderStatus(id, status, remains));
    }




    @PatchMapping("/{id}/start-count")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<OrderRes> updateStatus(@PathVariable Long id, @RequestBody OrderUpdateStartCountReq req) {
        return ApiResponseEntity.success(orderService.updateStartCount(id, req.getStartCount()));
    }

    @PatchMapping("/{id}/link")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<OrderRes> updateLink(@PathVariable Long id, @RequestBody OrderUpdateLinkReq req) {
        return ApiResponseEntity.success(orderService.updateStartCount(id, req.getLink()));
    }

    @PatchMapping("/{id}/api-order-id")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<OrderRes> updateStatus(@PathVariable Long id, @RequestBody OrderUpdateApiOrderIdReq req) {
        return ApiResponseEntity.success(orderService.updateApiOderId(id, req.getApiOrderId()));
    }

    @GetMapping("/search")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<Page<SuperOrderRes>> search(SearchOrderReq req, Pageable pageable) {
        return ApiResponseEntity.success(orderService.search(req, pageable));
    }

    @GetMapping("/me/search")
    @PreAuthorize("hasAnyRole('ROLE_USER' ,'ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<Page<OrderRes>> searchMyOrder( MyOrderSearchReq req, Pageable pageable) {
        return ApiResponseEntity.success(orderService.searchMyOrder(req, pageable));
    }

    @PostMapping
    @PreAuthorize("hasAnyRole('ROLE_USER' ,'ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<OrderRes> addOrder(@RequestBody OrderReq req) {
        return ApiResponseEntity.success(orderService.add(req));
    }


    @PostMapping("/{id}/refill")
    @PreAuthorize("hasAnyRole('ROLE_USER' ,'ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<OrderRes> refill(@PathVariable Long id) {
        return ApiResponseEntity.success(orderService.refill(id));
    }
    @PostMapping("/{id}/cancel")
    @PreAuthorize("hasAnyRole('ROLE_USER' ,'ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<OrderRes> cancel(@PathVariable Long id) {
        return ApiResponseEntity.success(orderService.cancel(id));
    }

    @PatchMapping("/{id}/note")
    @PreAuthorize("hasAnyRole('ROLE_USER' ,'ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<OrderRes> updateNote(@PathVariable Long id, @RequestBody OrderUpdateNoteReq req) {
        return ApiResponseEntity.success(orderService.updateNote(id, req.getNote()));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<String> delete(@PathVariable Long id) {
        orderService.delete(id);
        return ApiResponseEntity.success();
    }

    @PostMapping("/test")
    public ApiResponseEntity<LocalDateTime> test() {
        return ApiResponseEntity.success(LocalDateTime.now());
    }
}
