<div class="overlay-black" (click)="onOverlayClick($event)">
  <div class="layout-container">
    <button type="button" class="close-button" (click)="onClose()">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <line x1="18" y1="6" x2="6" y2="18"></line>
        <line x1="6" y1="6" x2="18" y2="18"></line>
      </svg>
    </button>
    
    <div class="icon-container">
      <fa-icon icon="plug" class="header-icon"></fa-icon>
    </div>
    
    <div class="header-section gap-4">
      <h1 class="title">{{ 'integrations.custom.title' | translate }}</h1>
      <p class="description">{{ 'integrations.custom.description' | translate }}</p>
    </div>

    <div class="form-container">
      <form [formGroup]="customIntegrationForm" (ngSubmit)="onSubmit()">
        <!-- Error Message -->
        <div *ngIf="errorMessage" class="error-message">
          {{ errorMessage }}
        </div>

        <!-- Key Field -->
        <div class="form-group">
          <label for="key" class="form-label">{{ 'integrations.custom.key' | translate }}</label>
          <input
            type="text"
            id="key"
            formControlName="key"
            class="form-input"
            [placeholder]="'integrations.custom.key_placeholder' | translate">
          <div *ngIf="customIntegrationForm.get('key')?.invalid && customIntegrationForm.get('key')?.touched" class="field-error">
            <span *ngIf="customIntegrationForm.get('key')?.errors?.['required']">
              {{ 'integrations.custom.key_required' | translate }}
            </span>
            <span *ngIf="customIntegrationForm.get('key')?.errors?.['pattern']">
              {{ 'integrations.custom.key_pattern' | translate }}
            </span>
          </div>
        </div>

        <!-- Value Field -->
        <div class="form-group">
          <label for="value" class="form-label">{{ 'integrations.custom.value' | translate }}</label>
          <input
            type="url"
            id="value"
            formControlName="value"
            class="form-input"
            [placeholder]="'integrations.custom.value_placeholder' | translate">
          <div *ngIf="customIntegrationForm.get('value')?.invalid && customIntegrationForm.get('value')?.touched" class="field-error">
            <span *ngIf="customIntegrationForm.get('value')?.errors?.['required']">
              {{ 'integrations.custom.value_required' | translate }}
            </span>
            <span *ngIf="customIntegrationForm.get('value')?.errors?.['pattern']">
              {{ 'integrations.custom.value_pattern' | translate }}
            </span>
          </div>
        </div>

        <!-- Position Field -->
        <div class="form-group">
          <label for="position" class="form-label">{{ 'integrations.custom.position' | translate }}</label>
          <select id="position" formControlName="position" class="form-select">
            <option *ngFor="let option of positionOptions" [value]="option.value">
              {{ option.label | translate }}
            </option>
          </select>
        </div>

        <!-- Icon Selection -->
        <div class="form-group">
          <label class="form-label">{{ 'integrations.custom.icon' | translate }}</label>
          
          <!-- Upload Icon Button -->
          <div class="upload-icon-section">
            <input
              type="file"
              #fileInput
              accept="image/png"
              (change)="onFileSelected($event)"
              style="display: none;">
            <button
              type="button"
              class="upload-icon-btn"
              (click)="fileInput.click()"
              [disabled]="isUploading">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="7 10 12 15 17 10"></polyline>
                <line x1="12" y1="15" x2="12" y2="3"></line>
              </svg>
              {{ isUploading ? ('integrations.custom.uploading' | translate) : ('integrations.custom.upload_icon' | translate) }}
            </button>
          </div>

          <div class="icon-selector">
            <div class="selected-icon" (click)="toggleIconGrid($event); $event.stopPropagation()">
              <div class="icon-display">
                <span *ngIf="selectedIcon.id === 0" class="no-icon-text">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="folder-icon">
                    <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
                  </svg>
                  {{ selectedIcon.value }}
                </span>
                <span *ngIf="selectedIcon.id !== 0 && !isImageUrl(selectedIcon.value) && selectedIcon.value !== 'no-icon' " class="icon-name">
                  <fa-icon [icon]="getFontAwesomeIcon(selectedIcon.value)" [ngStyle]="{ color: getIconColor(selectedIcon.name) }" class="mr-2"></fa-icon>
                  {{ selectedIcon.value }}
                </span>
                <span *ngIf="selectedIcon.id !== 0 && isImageUrl(selectedIcon.value)" class="icon-name">
                  <img [src]="selectedIcon.value" alt="Icon" class="uploaded-icon-preview mr-2">
                  {{ selectedIcon.name }}
                </span>
              </div>
              <div class="dropdown-arrow">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="6 9 12 15 18 9"></polyline>
                </svg>
              </div>
            </div>

            <!-- Icon Grid -->
            <div *ngIf="showIconGrid" class="icon-grid-container" (click)="$event.stopPropagation()">
              <div class="icon-grid-header">
                <h3>{{ 'integrations.custom.select_icon' | translate }}</h3>
                <button type="button" class="icon-grid-close-btn" (click)="closeIconGrid(); $event.stopPropagation()">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </svg>
                </button>
              </div>
              <div class="icon-grid">
                <div
                  *ngFor="let icon of brandIcons"
                  class="icon-grid-item"
                  [class.selected]="selectedIcon.value === icon.value"
                  (click)="selectIcon(icon); $event.stopPropagation()">
                  <fa-icon
                    *ngIf="!isImageUrl(icon.value) && icon.value !== 'no-icon'"
                    [icon]="getFontAwesomeIcon(icon.value)"
                    [ngStyle]="{ color: getIconColor(icon.name) }">
                  </fa-icon>
                  <img
                    *ngIf="isImageUrl(icon.value)"
                    [src]="icon.value"
                    alt="Icon"
                    class="uploaded-icon-grid">
                  <span *ngIf="icon.value === 'no-icon'" class="no-icon-grid">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
                    </svg>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Submit button -->
        <button
          type="submit"
          class="save-button"
          [disabled]="isSubmitting || customIntegrationForm.invalid">
          {{ isSubmitting ? ('integrations.custom.creating' | translate) : ('integrations.custom.create' | translate) }}
        </button>
      </form>
    </div>
  </div>
</div>
