-- Add is_deleted column to platform and g_order tables for soft delete functionality
DO $$
BEGIN
    -- Add is_deleted column to platform table
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'platform' 
        AND column_name = 'is_deleted'
    ) THEN
        -- Add the column
        ALTER TABLE platform ADD COLUMN is_deleted BOOLEAN NOT NULL DEFAULT FALSE;
        
        -- Create index for better performance on queries filtering by is_deleted
        CREATE INDEX IF NOT EXISTS idx_platform_is_deleted ON platform(is_deleted);
        
        -- Create composite index for tenant_id and is_deleted for better performance
        CREATE INDEX IF NOT EXISTS idx_platform_tenant_is_deleted ON platform(tenant_id, is_deleted);
        
        -- Add comment
        COMMENT ON COLUMN platform.is_deleted IS 'Soft delete flag - true if platform is deleted, false if active';
    END IF;

    -- Add is_deleted column to g_order table
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'g_order' 
        AND column_name = 'is_deleted'
    ) THEN
        -- Add the column
        ALTER TABLE g_order ADD COLUMN is_deleted BO<PERSON>EAN NOT NULL DEFAULT FALSE;
        
        -- Create index for better performance on queries filtering by is_deleted
        CREATE INDEX IF NOT EXISTS idx_g_order_is_deleted ON g_order(is_deleted);
        
        -- Create composite index for tenant_id and is_deleted for better performance
        CREATE INDEX IF NOT EXISTS idx_g_order_tenant_is_deleted ON g_order(tenant_id, is_deleted);
        
        -- Add comment
        COMMENT ON COLUMN g_order.is_deleted IS 'Soft delete flag - true if order is deleted, false if active';
    END IF;
END $$;
