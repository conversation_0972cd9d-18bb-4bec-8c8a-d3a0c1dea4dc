<div class="overlay-black">
  <div class="service-layout-container md:p-0 bg-white rounded-2xl shadow-lg">
    <!-- Close button -->
    <div class="bg-white  rounded-2xl p-3 flex justify-end">
      <button type="button" class="close-button" (click)="closePopup()">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    </div>

    <div class="p-5 flex flex-col gap-5">
      <!-- Service Type Section -->
      <div class="section">
        <h2 class="section-title text-gray-700 font-medium text-lg">{{ 'popup.new_service.service_type' | translate }}</h2>
        <div class="service-type-box bg-[#f5f7fc] rounded-lg p-4">
          <div class="flex items-center justify-between w-full mb-2">
            <div>
              <h3 class="font-medium text-gray-800">{{ serviceType }}</h3>
              <p class="text-sm text-gray-600">{{ serviceTypeDescription }}</p>
            </div>
            <button class="change-btn text-[#0095f6] hover:text-white hover:bg-[#0095f6] border border-[#0095f6] px-4 py-1 rounded-lg transition-colors" (click)="onChangeClick()">{{ 'popup.new_service.change' | translate }}</button>
          </div>


        </div>
      </div>

      <!-- Provider Section - Only shown in Provider mode -->
      <div class="section" *ngIf="serviceType === 'Provider'">
        <h2 class="section-title text-gray-700 font-medium text-lg">{{ 'popup.new_service.provider' | translate }}</h2>
        <div class="grid grid-cols-1 gap-4">
          <div>
            <label class="text-sm text-gray-600 mb-2 block">{{ 'popup.new_service.provider' | translate }}</label>
            <app-icon-dropdown
              [options]="providerOptions"
              [selectedOption]="selectedProvider ? {id: selectedProvider.id.toString(), label: selectedProvider.name, icon: '', sort: 0} : undefined"
              (selected)="onProviderSelected($event)"
              [customClassButton]="'bg-[#f5f7fc] border-none rounded-lg text-gray-800 p-3'"
              [customClassDropdown]="'w-full'">
            </app-icon-dropdown>
          </div>
        </div>
      </div>

      <!-- Service ID Section - Only shown in Provider mode -->
      <div class="section" *ngIf="serviceType === 'Provider'">
        <div class="grid grid-cols-1 gap-4">
          <div>
            <label class="text-sm text-gray-600 mb-2 block">{{ 'popup.new_service.service_id' | translate }}</label>
            <div *ngIf="isLoadingServices" class="flex items-center justify-center p-4 bg-[#f5f7fc] rounded-lg">
              <div class="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-[#0095f6] mr-2"></div>
              <span class="text-gray-600">{{ 'popup.new_service.loading_services' | translate }}</span>
            </div>
            <div *ngIf="!isLoadingServices" class="relative">
              <!-- Search input field -->
              <div class="mb-2">
                <div class="flex gap-2">
                  <div class="relative flex-grow">
                    <input
                      type="text"
                      [(ngModel)]="serviceSearchQuery"
                      (blur)="onServiceSearchBlur()"
                      (keyup.enter)="onServiceSearchBlur()"
                      placeholder="{{ 'popup.new_service.enter_search_hint' | translate }}"
                      class="form-input bg-[#f5f7fc] border-none rounded-lg p-3 text-gray-800 w-full pr-10">
                    <!-- <fa-icon
                      [icon]="['fas', 'search']"
                      class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                    </fa-icon> -->
                  </div>
                  <button
                    type="button"
                    (click)="onServiceSearchBlur()"
                    class="bg-[#0095f6] text-white rounded-lg px-3 flex items-center justify-center">
                    <fa-icon [icon]="['fas', 'search']"></fa-icon>
                  </button>
                </div>
              </div>
              <!-- Service dropdown with filtered options -->
              <app-icon-dropdown
                [options]="filteredServiceIdOptions"
                [selectedOption]="selectedServiceId ? selectedServiceId : undefined"
                (selected)="onServiceIdSelected($event)"
                [customClassButton]="'bg-[#f5f7fc] border-none rounded-lg text-gray-800 p-3'"
                [customClassDropdown]="'w-full'"
                [placeholder]="'popup.new_service.select_service' | translate">
              </app-icon-dropdown>
            </div>
          </div>
        </div>
      </div>

      <!-- Description Section -->
      <div class="section">
        <h2 class="section-title text-gray-700 font-medium text-lg">{{ 'popup.new_service.description' | translate }}</h2>

        <!-- Service name -->
        <div class="mb-4">
          <label class="text-sm text-gray-600 mb-2 block">{{ 'popup.new_service.service_name' | translate }}</label>
          <input type="text" [ngModel]="serviceName" (ngModelChange)="onServiceNameChange($event)" class="form-input bg-[#f5f7fc] border-none rounded-lg p-2 text-gray-800 w-full" placeholder="{{ 'popup.new_service.enter_service_name' | translate }}">
        </div>



        <!-- Service type -->
        <div class="mb-4">
          <label class="text-sm text-gray-600 mb-2 block">{{ 'popup.new_service.service_type_label' | translate }}</label>
          <app-lite-dropdown
            [options]="serviceTypeOptions"
            [selectedOption]="serviceTypeOption"
            (selected)="onServiceTypeSelected($event)"
            [customClassButton]="'bg-[#f5f7fc] border-none rounded-lg text-gray-800'"
            [customClassDropdown]="'w-full'">
          </app-lite-dropdown>
        </div>

        <!-- Category -->
        <div class="mb-4">
          <label class="text-sm text-gray-600 mb-2 block">{{ 'popup.new_service.category' | translate }}</label>
          <div class="relative">
            <app-icon-dropdown
              [options]="categoryOptions"
              [selectedOption]="selectedCategory"
              (selected)="onCategorySelected($event)"
              [customClassButton]="'bg-[#f5f7fc] border-none rounded-lg text-gray-800 p-3'"
              [customClassDropdown]="'w-full'">
            </app-icon-dropdown>
          </div>
        </div>

        <!-- Description -->
        <div class="mb-4">
          <label class="text-sm text-gray-600 mb-2 block">{{ 'popup.new_service.description_label' | translate }}</label>
          <textarea [(ngModel)]="description" rows="7" class="h-auto form-input bg-[#f5f7fc] border-none rounded-lg p-2 text-gray-800 w-full" placeholder="{{ 'popup.new_service.enter_service_description' | translate }}"></textarea>
        </div>

        <!-- User-friendly description -->
        <div class="mb-4">
          <div class="bg-[#f5f7fc] rounded-lg overflow-hidden">
            <!-- Header with toggle -->
            <div class="p-3 flex justify-between items-center border-b border-gray-200 cursor-pointer" (click)="toggleUserFriendlyDescription()">
              <div class="flex items-center gap-2">
                <fa-icon [icon]="['fas', 'user']" class="text-blue-500 text-lg"></fa-icon>
                <span class="font-medium text-gray-800">{{ 'popup.new_service.user_friendly_description' | translate }}</span>
              </div>
              <fa-icon [icon]="userFriendlyDescription ? 'chevron-up' : 'chevron-down'" class="text-gray-500"></fa-icon>
            </div>

            <!-- Expanded content -->
            <div *ngIf="userFriendlyDescription" class="p-4 border-b border-gray-200">
              <!-- Speed per day -->
              <div class="mb-4">
                <label class="text-sm text-gray-600 mb-2 block">{{ 'popup.new_service.speed_per_day' | translate }}</label>
                <input type="text" [(ngModel)]="speedPerDay" class="form-input bg-white border border-gray-200 rounded-lg p-2 text-gray-800 w-full" placeholder="{{ 'popup.new_service.speed_hint' | translate }}">
              </div>

              <!-- Drops percentage -->
              <!-- <div class="mb-4">
                <label class="text-sm text-gray-600 mb-2 block">Drops percentage</label>
                <input type="text" [(ngModel)]="dropsPercentage" class="form-input bg-white border border-gray-200 rounded-lg p-2 text-gray-800 w-full" placeholder="Mention in %">
              </div> -->

              <!-- Guarantee -->
              <!-- <div class="flex justify-between items-center mb-4">
                <label class="text-sm text-gray-800 font-medium">Guarantee</label>
                <app-toggle-switch
                  [isChecked]="guarantee"
                  (toggled)="guarantee = $event"
                  [circleColor]="'#FFF'"
                  [toggledBgColor]="'var(--primary)'">
                </app-toggle-switch>
              </div> -->

              <!-- Link sample -->
              <div class="mb-4">
                <label class="text-sm text-gray-600 mb-2 block">{{ 'popup.new_service.link_sample' | translate }}</label>
                <input type="text" [(ngModel)]="linkSample" class="form-input bg-white border border-gray-200 rounded-lg p-2 text-gray-800 w-full" placeholder="{{ 'popup.new_service.enter_link_sample' | translate }}">
              </div>

              <!-- Service Labels -->
              <div>
                <label class="text-sm text-gray-600 mb-2 block">{{ 'popup.new_service.service_labels' | translate }}</label>

                <!-- Current labels -->
                <div class="flex flex-wrap gap-2 mb-3" *ngIf="serviceLabels.length > 0">
                  <div *ngFor="let label of serviceLabels; let i = index" class="flex items-center mb-2">
                    <app-tag-label [text]="label.text" [color]="label.type === 'Green' ? '#289d35' : '#cf0616'"></app-tag-label>
                    <button class="ml-1 text-gray-500 hover:text-red-500" (click)="removeServiceLabel(i)">
                      <fa-icon [icon]="['fas', 'times']" class="text-lg"></fa-icon>
                    </button>
                  </div>
                </div>

                <!-- Predefined labels -->
                <div class="mb-3">
                  <label class="text-sm text-gray-600 mb-2 block">{{ 'popup.new_service.common_labels' | translate }}</label>
                  <div class="grid grid-cols-4 gap-2">
                    <div *ngFor="let label of predefinedLabels">
                      <button
                        class="w-full px-2 py-1 text-xs rounded-lg border flex items-center justify-center"
                        [ngStyle]="{
                          'background-color': label.type === 'Green' ? '#e6f4ea' : '#fce8e6',
                          'border-color': label.type === 'Green' ? '#289d35' : '#cf0616',
                          'color': label.type === 'Green' ? '#289d35' : '#cf0616'
                        }"
                        (click)="addPredefinedLabel(label)">
                        <fa-icon
                          [icon]="label.type === 'Green' ? ['fas', 'check'] : ['fas', 'exclamation']"
                          class="mr-1">
                        </fa-icon>
                        {{ label.text }}
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Add custom label -->
                <div class="mb-2">
                  <label class="text-sm text-gray-600 mb-2 block">{{ 'popup.new_service.add_custom_label' | translate }}</label>
                  <div class="flex gap-2 items-center">
                    <input
                      type="text"
                      [(ngModel)]="newLabelText"
                      (keyup.enter)="addServiceLabel()"
                      class="form-input bg-white border border-gray-200 rounded-lg p-2 text-gray-800 w-1/2"
                      placeholder="{{ 'popup.new_service.enter_label_text' | translate }}"
                    >

                    <!-- Label type selection -->
                    <div class="flex gap-2">
                      <button
                        class="px-3 py-2 rounded-lg border transition-colors flex items-center"
                        [ngClass]="selectedLabelType === 'Green' ? 'bg-[#0095f6] text-white border-[#0095f6]' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-100'"
                        (click)="setLabelType('Green')">
                        <fa-icon [icon]="['fas', 'check']" class="mr-1"></fa-icon> {{ 'popup.new_service.green' | translate }}
                      </button>
                      <button
                        class="px-3 py-2 rounded-lg border transition-colors flex items-center"
                        [ngClass]="selectedLabelType === 'Red' ? 'bg-[#0095f6] text-white border-[#0095f6]' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-100'"
                        (click)="setLabelType('Red')">
                        <fa-icon [icon]="['fas', 'exclamation']" class="mr-1"></fa-icon> {{ 'popup.new_service.red' | translate }}
                      </button>
                    </div>

                    <!-- Add button -->
                    <button
                      class="px-3 py-2 bg-[#0095f6] text-white rounded-lg hover:bg-[#0077c2] transition-colors flex items-center"
                      (click)="addServiceLabel()">
                      <fa-icon [icon]="['fas', 'plus']"></fa-icon>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Options Section -->
      <div class="section">
        <h2 class="section-title text-gray-700 font-medium text-lg">{{ 'popup.new_service.options' | translate }}</h2>

        <!-- Sync All Toggle - Only shown for Provider services -->
        <div *ngIf="serviceType === 'Provider'" class="mb-4 bg-[#f5f7fc] rounded-lg p-3">
          <div class="flex justify-between items-center">
            <div class="flex items-center gap-2">
              <fa-icon [icon]="['fas', 'sync']" class="text-blue-500"></fa-icon>
              <span class="text-gray-800 font-medium">{{ 'popup.new_service.sync_all_settings' | translate }}</span>
              <fa-icon [icon]="['fas', 'question-circle']" class="text-gray-400 ml-1 cursor-help" title="Enable synchronization for all service properties"></fa-icon>
            </div>
            <app-toggle-switch
              [isChecked]="syncAll"
              (toggled)="toggleSyncAll($event)"
              [circleColor]="'#FFF'"
              [toggledBgColor]="'var(--primary)'">
            </app-toggle-switch>
          </div>
          <div class="mt-2 text-sm text-gray-500 ml-6">
            <p>{{ 'popup.new_service.sync_description' | translate }}</p>
          </div>
        </div>

        <!-- Min/Max values -->
        <div class="mb-4 bg-[#f5f7fc] rounded-lg p-3">
          <div class="flex justify-between items-center mb-2">
            <div class="flex items-center gap-2">
              <fa-icon [icon]="['fas', 'arrows-alt-v']" class="text-blue-500"></fa-icon>
              <span class="text-gray-800 font-medium">{{ 'popup.new_service.min_max_label' | translate }}</span>
            </div>
          </div>

          <!-- Sync toggle for Provider services - shown as a sub-option -->
          <div *ngIf="serviceType === 'Provider'" class="mb-3 flex justify-between items-center bg-white p-2 rounded-lg border border-gray-100">
            <div class="flex items-center gap-2">
              <fa-icon [icon]="['fas', 'sync-alt']" class="text-blue-400 text-sm"></fa-icon>
              <span class="text-gray-600 text-sm">{{ 'popup.new_service.sync_with_provider' | translate }}</span>
            </div>
            <app-toggle-switch
              [isChecked]="syncMinMax"
              (toggled)="toggleSyncMinMax($event)"
              [circleColor]="'#FFF'"
              [toggledBgColor]="'var(--primary)'">
            </app-toggle-switch>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="field-container">
              <input type="number" [value]="minValue" (input)="onMinChange($event)" [disabled]="syncMinMax" class="form-input bg-white border border-gray-100 rounded-lg p-2 text-gray-800 w-full no-spinner" placeholder="{{ 'popup.new_service.min' | translate }}">
            </div>
            <div class="field-container">
              <input type="number" [value]="maxValue" (input)="onMaxChange($event)" [disabled]="syncMinMax" class="form-input bg-white border border-gray-100 rounded-lg p-2 text-gray-800 w-full no-spinner" placeholder="{{ 'popup.new_service.max' | translate }}">
            </div>
          </div>

          <!-- Service Status Sync toggle for Provider services -->
          <div *ngIf="serviceType === 'Provider'" class="mt-3 flex justify-between items-center bg-white p-2 rounded-lg border border-gray-100">
            <div class="flex items-center gap-2">
              <fa-icon [icon]="['fas', 'sync-alt']" class="text-blue-400 text-sm"></fa-icon>
              <span class="text-gray-600 text-sm">{{ 'popup.new_service.sync_service_status' | translate }}</span>
              <fa-icon [icon]="['fas', 'question-circle']" class="text-gray-400 ml-1 cursor-help text-xs" title="{{ 'popup.new_service.sync_status_description' | translate }}"></fa-icon>
            </div>
            <app-toggle-switch
              [isChecked]="syncStatus"
              (toggled)="toggleSyncStatus($event)"
              [circleColor]="'#FFF'"
              [toggledBgColor]="'var(--primary)'">
            </app-toggle-switch>
          </div>
        </div>

        <!-- Additional options dropdown -->
        <div class="bg-[#f5f7fc] rounded-lg overflow-hidden mb-4">
          <!-- Header with toggle -->
          <div class="p-3 flex justify-between items-center border-b border-gray-200 cursor-pointer" (click)="toggleAdditionalOptions()">
            <div class="flex items-center gap-2">
              <fa-icon [icon]="['fas', 'cog']" class="text-blue-500 text-lg"></fa-icon>
              <span class="font-medium text-gray-800">{{ 'popup.new_service.additional_options' | translate }}</span>
            </div>
            <fa-icon [icon]="additionalOptions ? 'chevron-up' : 'chevron-down'" class="text-gray-500"></fa-icon>
          </div>

          <!-- Expanded content -->
          <div *ngIf="additionalOptions" class="border-b">
            <!-- Cancel button -->
            <div class="p-3 border-b">
              <div class="flex justify-between items-center mb-2">
                <div class="flex items-center">
                  <div class="form-check mr-2">
                    <input
                      type="checkbox"
                      class="checkbox-label "
                      [checked]="cancelButton"
                      [disabled]="syncCancel"
                      (change)="onCancelButtonChange($event)">
                  </div>
                  <div class="flex items-center gap-2">
                    <fa-icon [icon]="['fas', 'times-circle']" class="text-blue-500"></fa-icon>
                    <span class="text-gray-800 font-medium">{{ 'popup.new_service.cancel_button' | translate }}</span>
                    <fa-icon [icon]="['fas', 'question-circle']" class="text-gray-400 ml-1 cursor-help" title="{{ 'popup.new_service.cancel_button_description' | translate }}"></fa-icon>
                  </div>
                </div>

                <!-- Sync toggle for Provider services - inline -->
                <div *ngIf="serviceType === 'Provider'" class="flex items-center gap-2 bg-white px-3 py-1 rounded-lg border border-gray-100">
                  <div class="flex items-center gap-2">
                    <fa-icon [icon]="['fas', 'sync-alt']" class="text-blue-400 text-sm"></fa-icon>
                    <span class="text-gray-600 text-sm">{{ 'popup.new_service.sync_with_provider' | translate }}</span>
                  </div>
                  <app-toggle-switch
                    [isChecked]="syncCancel"
                    (toggled)="toggleSyncCancel($event)"
                    [circleColor]="'#FFF'"
                    [toggledBgColor]="'var(--primary)'">
                  </app-toggle-switch>
                </div>
              </div>
            </div>

            <!-- Refill -->
            <div class="p-3 border-b">
              <div class="flex justify-between items-center mb-2">
                <div class="flex items-center">
                  <div class="form-check mr-2">
                    <input
                      type="checkbox"
                      class="checkbox-label "
                      [checked]="refill"
                      [disabled]="syncRefill"
                      (change)="onRefillChange($event)">
                  </div>
                  <div class="flex items-center gap-2">
                    <fa-icon [icon]="['fas', 'sync']" class="text-blue-500"></fa-icon>
                    <span class="text-gray-800 font-medium">{{ 'popup.new_service.refill' | translate }}</span>
                    <fa-icon [icon]="['fas', 'question-circle']" class="text-gray-400 ml-1 cursor-help" title="{{ 'popup.new_service.refill_description' | translate }}"></fa-icon>
                  </div>
                </div>

                <!-- Sync toggle for Provider services - inline -->
                <div *ngIf="serviceType === 'Provider'" class="flex items-center gap-2 bg-white px-3 py-1 rounded-lg border border-gray-100">
                  <div class="flex items-center gap-2">
                    <fa-icon [icon]="['fas', 'sync-alt']" class="text-blue-400 text-sm"></fa-icon>
                    <span class="text-gray-600 text-sm">{{ 'popup.new_service.sync_with_provider' | translate }}</span>
                  </div>
                  <app-toggle-switch
                    [isChecked]="syncRefill"
                    (toggled)="toggleSyncRefill($event)"
                    [circleColor]="'#FFF'"
                    [toggledBgColor]="'var(--primary)'">
                  </app-toggle-switch>
                </div>
              </div>

              <!-- Refill days input field - only shown when refill is enabled -->
              <div *ngIf="refill" class="mt-3 ml-6">
                <label class="text-sm text-gray-600 mb-2 block">{{ 'popup.new_service.refill_days_label' | translate }}</label>
                <input
                  type="number"
                  [(ngModel)]="refillDays"
                  [disabled]="syncRefill"
                  class="toggle-input text-gray-800 no-spinner bg-white border border-gray-100 rounded-lg p-2 w-full"
                  placeholder="{{ 'popup.new_service.refill_days_hint' | translate }}">
              </div>
            </div>

            <!-- Overflow -->
            <div class="p-3 border-b">
              <div class="flex items-center mb-2">
                <div class="form-check mr-2">
                  <input
                    type="checkbox"
                    class="checkbox-label "
                    [checked]="overflow"
                    (change)="onOverflowChange($event)">
                </div>
                <div class="flex items-center gap-2">
                  <fa-icon [icon]="['fas', 'exclamation-circle']" class="text-blue-500"></fa-icon>
                  <span class="text-gray-800 font-medium">{{ 'popup.new_service.overflow' | translate }}</span>
                  <fa-icon [icon]="['fas', 'question-circle']" class="text-gray-400 ml-1 cursor-help" title="{{ 'popup.new_service.overflow_description' | translate }}"></fa-icon>
                </div>
              </div>

              <!-- Overflow value input field - only shown when overflow is enabled -->
              <div *ngIf="overflow" class="mt-3 ml-6">
                <input
                  type="number"
                  [(ngModel)]="overflowValue"
                  class="toggle-input text-gray-800 no-spinner bg-white border border-gray-100 rounded-lg p-2 w-full"
                  placeholder="0">
              </div>
            </div>

            <!-- 1 order in 1 hand -->

          </div>
        </div>
      </div>

      <!-- Price Section -->
      <div class="section">
        <h2 class="section-title text-gray-700 font-medium text-lg">{{ 'popup.new_service.price' | translate }}</h2>

        <!-- Provider mode price section -->
        <div class="bg-[#f5f7fc] rounded-lg overflow-hidden" *ngIf="serviceType === 'Provider'">
          <!-- Provider price and extra price sections -->
          <!-- Provider price -->
          <div class="flex justify-between items-center p-3 border-b border-gray-200">
            <span class="text-gray-800">{{ 'popup.new_service.provider_price' | translate }}</span>
            <span class="font-medium text-gray-800">${{ providerPrice }}</span>
          </div>

          <!-- Your extra price - shown when fixed price is disabled -->
          <div *ngIf="!fixedPrice" class="flex justify-between items-center p-3 border-b border-gray-200">
            <span class="text-gray-800">{{ 'popup.new_service.extra_price_percentage' | translate }}</span>
            <div class="w-32">
              <div class="relative">
                <input type="number" [(ngModel)]="extraPricePercentage" (ngModelChange)="onExtraPriceChange()" class="form-input bg-white no-spinner border border-gray-200 rounded-lg p-2 text-gray-800 w-full text-right !pr-8">
                <span class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500">%</span>
              </div>
            </div>
          </div>

          <!-- Your fixed price input - shown when fixed price is enabled -->
          <div *ngIf="fixedPrice" class="flex justify-between items-center p-3 border-b border-gray-200">
            <span class="text-gray-800">{{ 'popup.new_service.fixed_price' | translate }}</span>
            <div class="w-32">
              <div class="relative">
                <input
                  type="number"
                  [(ngModel)]="finalPrice"
                  class="form-input bg-white no-spinner border border-gray-200 rounded-lg p-2 text-gray-800 w-full text-right !pr-8"
                  placeholder="0.29"
                  step="0.000001">
                <span class="absolute font-bold right-3 top-1/2 -translate-y-1/2 text-gray-500">$</span>
              </div>
            </div>
          </div>

          <!-- Your price on panel - shown when fixed price is disabled -->
          <div *ngIf="!fixedPrice" class="flex justify-between items-center p-3 border-b border-gray-200">
            <span class="text-gray-800">{{ 'popup.new_service.price_on_panel' | translate }}</span>
            <span class="font-medium text-green-600">${{ formatBalance(finalPrice) }}</span>
          </div>

          <!-- Fixed price toggle - always at the end -->
          <div class="flex justify-between items-center p-3 border-b border-gray-200">
            <div class="flex items-center gap-2">
              <fa-icon [icon]="['fas', 'lock']" class="text-blue-500"></fa-icon>
              <span class="text-gray-800">{{ 'popup.new_service.fixed_price' | translate }}</span>
            </div>
            <app-toggle-switch
              [isChecked]="fixedPrice"
              (toggled)="fixedPrice = $event"
              [circleColor]="'#FFF'"
              [toggledBgColor]="'var(--primary)'">
            </app-toggle-switch>
          </div>

          <!-- Info message -->
          <div *ngIf="fixedPrice" class="p-3 bg-blue-50 text-sm text-blue-800 flex items-start">
            <fa-icon [icon]="['fas', 'info-circle']" class="mr-2 mt-1"></fa-icon>
            <span>Now the price is updated from the provider and multiplied by your percentage. This protects you from price increases.</span>
          </div>
        </div>

        <!-- Manual mode price section -->
        <div class="bg-[#f5f7fc] rounded-lg overflow-hidden" *ngIf="serviceType === 'Manual'">
          <div class="flex justify-between items-center p-3 border-b border-gray-200">
            <span class="text-gray-800">{{ 'popup.new_service.fixed_price' | translate }}</span>
            <div class="w-24">
              <div class="relative">
                <input
                  type="number"
                  [(ngModel)]="finalPrice"
                  class="form-input bg-white no-spinner border border-gray-200 rounded-lg p-2 text-gray-800 w-full text-right !pr-8"
                  placeholder="0.29"
                  step="0.000001">
                <span class="absolute font-bold right-3 top-1/2 -translate-y-1/2 text-gray-500">$</span>
              </div>
            </div>
          </div>
        </div>

      </div>

      <!-- Save Button -->
      <button
        (click)="onSave()"
        [disabled]="isSubmitting"
        class="save-btn bg-[#0095f6] hover:bg-[#0077c2] text-white font-medium py-3 px-4 rounded-lg w-full mt-4 flex items-center justify-center disabled:opacity-70 disabled:cursor-not-allowed">
        <div *ngIf="isSubmitting" class="inline-block w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
        {{ isSubmitting ? ('Saving...' | translate) : ('Save' | translate) }}
      </button>
    </div>
  </div>
</div>
