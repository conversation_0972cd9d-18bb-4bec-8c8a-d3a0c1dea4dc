spring:
  redis:
    host: v-redis
    port: 6379
    password: dung12345678a@*
    database: 3
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: org.postgresql.Driver
    url: ******************************************
    username: postgres
    password: dung12345678a@
    hikari:
      maximum-pool-size: 5
      minimum-idle: 1
      auto-commit: false
#      connection-test-query: SELECT 1 FROM DUAL

# Domain manager configuration
domain-manager:
  url: http://domain-manager:3000

# File upload configuration for production
file:
  upload:
    dir: /app/uploads
    max-size: 10MB
  cdn:
    url: http://${server.address:localhost}/cdn
