-- Change user_agent column in login_history table from varchar(255) to text
-- This fixes the "value too long for type character varying(255)" error when saving login history

-- First check if the column exists and is of type varchar
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'login_history' 
        AND column_name = 'user_agent'
        AND data_type = 'character varying'
    ) THEN
        -- Alter the column type to text
        ALTER TABLE login_history ALTER COLUMN user_agent TYPE text;
    END IF;
END $$;

-- Add a comment to explain the change
COMMENT ON COLUMN login_history.user_agent IS 'User agent string from the browser, stored as text to handle long values';
