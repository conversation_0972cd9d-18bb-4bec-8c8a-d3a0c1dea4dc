import { Injectable, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface LoadingState {
  size: 'sm' | 'md' | 'lg';
  overlay: boolean;
  fullScreen: boolean;
  message: string;
  transparent: boolean;
}

export interface LoadingInputs {
  size: 'sm' | 'md' | 'lg';
  overlay: boolean;
  fullScreen: boolean;
  message: string;
  transparent: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class LoadingLogicService implements OnDestroy {
  private stateSubject = new BehaviorSubject<LoadingState>({
    size: 'md',
    overlay: false,
    fullScreen: false,
    message: '',
    transparent: false
  });

  public state$ = this.stateSubject.asObservable();

  constructor() {}

  updateInputs(inputs: LoadingInputs): void {
    this.updateState(inputs);
  }

  private updateState(partialState: Partial<LoadingState>): void {
    const currentState = this.stateSubject.value;
    this.stateSubject.next({ ...currentState, ...partialState });
  }

  ngOnDestroy(): void {
    this.destroy();
  }

  destroy(): void {
    // Cleanup if needed
  }
}
