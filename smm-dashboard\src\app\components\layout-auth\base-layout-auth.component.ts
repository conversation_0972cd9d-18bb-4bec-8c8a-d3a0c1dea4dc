import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Observable } from 'rxjs';

// Services
import { LayoutAuthLogicService, LayoutAuthState } from './services/layout-auth-logic.service';

@Component({
  template: '', // Base component has no template
  standalone: true
})
export class BaseLayoutAuthComponent implements OnInit, OnDestroy {
  // Expose state as observable for template
  layoutAuthState$: Observable<LayoutAuthState>;

  constructor(protected layoutAuthLogicService: LayoutAuthLogicService) {
    this.layoutAuthState$ = this.layoutAuthLogicService.state$;
  }

  ngOnInit(): void {
    // LayoutAuthLogicService handles all initialization
  }

  ngOnDestroy(): void {
    // LayoutAuthLogicService is singleton, no cleanup needed
  }

  // Delegate methods to LayoutAuthLogicService for template compatibility
  changeLanguage(languageCode: string): void {
    this.layoutAuthLogicService.changeLanguage(languageCode);
  }

  getCurrentLanguage(): string {
    return this.layoutAuthLogicService.getCurrentLanguage();
  }

  getAvailableLanguages(): { code: string; name: string; flag: string }[] {
    return this.layoutAuthLogicService.getAvailableLanguages();
  }
}
