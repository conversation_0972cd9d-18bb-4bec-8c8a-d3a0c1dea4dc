import { Injectable, inject } from '@angular/core';
import { CategoriesService } from '../../core/services/categories.service';
import { FavoritesService } from '../../core/services/favorites.service';
import { SuperPlatformRes } from '../../model/response/super-platform.model';
import { IconBaseModel } from '../../model/base-model';
import { SuperCategoryRes } from '../../model/response/super-category.model';

// Extended interface for our dropdown options
interface ExtendedIconBaseModel extends IconBaseModel {
  platformId?: string;
}

// Extended interface for categories with platform info
interface ExtendedCategoryRes extends SuperCategoryRes {
  platformIcon?: string;
  isAllPlatforms?: boolean;
  isAllCategories?: boolean;
}

@Injectable()
export class ServicesLogicService {
  // All platforms data from API
  allPlatforms: SuperPlatformRes[] = [];

  // Platform dropdown options
  platformOptions: ExtendedIconBaseModel[] = [];
  selectedPlatform: ExtendedIconBaseModel | undefined;

  // Category dropdown options for the selected platform
  categoryOptions: ExtendedIconBaseModel[] = [];
  selectedCategory: ExtendedIconBaseModel | undefined;

  // Currently displayed category
  currentCategory: ExtendedCategoryRes | null = null;

  loading = false;

  // Store favorite service IDs
  favoriteServiceIds: number[] = [];

  // Track loading state for individual service IDs
  private loadingServiceIds = new Set<number>();

  // Property to store filtered categories for display
  displayCategories: ExtendedCategoryRes[] = [];

  // Properties for filtering
  private searchTerm: string = '';
  private isFilterApplied: boolean = false;
  private unfilteredDisplayCategories: ExtendedCategoryRes[] = [];
  private unfilteredCurrentCategory: ExtendedCategoryRes | null = null;

  // Store the original data before filtering
  private originalAllPlatforms: SuperPlatformRes[] = [];

  // Store the selected options for delayed filtering
  private pendingPlatform: IconBaseModel | null = null;
  private pendingCategory: ExtendedIconBaseModel | null = null;

  // Flag to control whether to update display on selection
  private updateDisplayOnSelection: boolean = false;

  private categoriesService = inject(CategoriesService);
  private favoritesService = inject(FavoritesService);

  constructor() {}

  loadFavorites(): void {
    this.favoritesService.favorites$.subscribe(favorites => {
      this.favoriteServiceIds = favorites;
    });
  }

  loadPlatforms(): void {
    this.loading = true;
    this.categoriesService.getPlatforms().subscribe({
      next: (platforms) => {
        // Store all platforms data and sort by sort field
        this.allPlatforms = platforms
          .filter(platform => !platform.hide)
          .sort((a, b) => a.sort - b.sort);

        // Store original data for filtering
        this.originalAllPlatforms = [...this.allPlatforms];

        // Create platform options for dropdown
        this.platformOptions = [
          // Add an "All Platforms" option at the top without an icon
          {
            id: 'all',
            label: 'filter.all_platforms',
            sort: 0,
            icon: '' // No icon
          },
          // Add all platforms (preserving the sort order)
          ...this.allPlatforms.map(platform => ({
            id: platform.id.toString(),
            label: platform.name,
            icon: platform.icon,
            sort: platform.sort // Use the platform's sort value
          }))
        ];

        // Select "All Platforms" by default
        if (this.platformOptions.length > 0) {
          // Store the selection without updating display
          this.selectedPlatform = this.platformOptions[0];
          this.pendingPlatform = this.platformOptions[0];

          // Update category options
          this.updateCategoryOptions(this.platformOptions[0]);

          // Apply selections to display all data initially
          this.applySelections();
        }

        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading platforms:', error);
        this.loading = false;
      }
    });
  }

  onPlatformSelected(platform: IconBaseModel): void {
    this.selectedPlatform = platform;
    this.pendingPlatform = platform;

    // Reset filter state when a new platform is selected
    if (this.isFilterApplied) {
      // If filtering was previously applied, we need to restore original state
      this.displayCategories = [...this.unfilteredDisplayCategories];
      this.currentCategory = this.unfilteredCurrentCategory;
      this.isFilterApplied = false;
    }

    // Always update the category options when platform changes
    this.updateCategoryOptions(platform);

    // Only proceed with updating the display if the flag is set
    if (!this.updateDisplayOnSelection) {
      return;
    }

    // Select first category by default if available
    if (this.categoryOptions.length > 0) {
      this.onCategorySelected(this.categoryOptions[0]);
    } else {
      this.currentCategory = null;
    }
  }

  // Method to update category options without displaying results
  private updateCategoryOptions(platform: IconBaseModel): void {
    if (platform.id === 'all') {
      // If "All Platforms" is selected, only show "All Categories" option
      const allCategoriesOption = {
        id: 'all',
        label: 'filter.all_categories',
        sort: 0,
        icon: '' // No icon
      };

      this.categoryOptions = [allCategoriesOption];

      // Select "All Categories" by default - create new object to avoid reference sharing
      this.selectedCategory = { ...allCategoriesOption };
      this.pendingCategory = { ...allCategoriesOption };
    } else {
      // Find the selected platform in allPlatforms
      const selectedPlatformData = this.allPlatforms.find(p => p.id.toString() === platform.id);

      if (selectedPlatformData) {
        // Create category options for the selected platform
        this.categoryOptions = [
          // Add an "All Categories" option for this platform without an icon
          {
            id: `${selectedPlatformData.id}_all`,
            label: 'filter.all_categories',
            icon: '',
            platformId: selectedPlatformData.id.toString(),
            sort: 0
          },
          // Add all categories for this platform
          ...selectedPlatformData.categories
            .filter(category => !category.hide)
            .sort((a, b) => a.sort - b.sort)
            .map(category => ({
              id: category.id.toString(),
              label: category.name,
              icon: selectedPlatformData.icon,
              platformId: selectedPlatformData.id.toString(),
              sort: category.sort
            }))
        ];

        // Select first category by default if available
        if (this.categoryOptions.length > 0) {
          this.selectedCategory = this.categoryOptions[0];
          this.pendingCategory = this.categoryOptions[0];
        }
      }
    }
  }

  onCategorySelected(category: ExtendedIconBaseModel): void {
    this.selectedCategory = category;
    this.pendingCategory = category;

    // Only proceed with updating the display if the flag is set
    if (!this.updateDisplayOnSelection) {
      return;
    }

    this.currentCategory = null;
    this.displayCategories = [];

    // Reset filter state when a new category is selected
    if (this.isFilterApplied) {
      // If filtering was previously applied, we need to restore original state
      this.displayCategories = [...this.unfilteredDisplayCategories];
      this.currentCategory = this.unfilteredCurrentCategory;
      this.isFilterApplied = false;
    }

    // Handle "All Categories" option
    if (category.id === 'all') {
      // When "All Categories" is selected, always show all categories from all platforms
      // This provides a comprehensive view regardless of platform selection
      this.allPlatforms.forEach(platform => {
        // Sort categories by sort field before adding them
        const sortedCategories = [...platform.categories]
          .filter(category => !category.hide)
          .sort((a, b) => a.sort - b.sort);

        sortedCategories.forEach(category => {
          // Add each category to the display list with platform info
          const categoryWithPlatform = {
            ...category,
            platformName: platform.name,
            platformIcon: platform.icon
          };
          this.displayCategories.push(categoryWithPlatform);
        });
      });
      this.displayCategories.sort((a, b) => a.sort - b.sort);
      return;
    }

    // Handle platform-specific "All Categories" option
    if (category.id.includes('_all') && category.platformId) {
      const platformId = category.platformId;
      const platform = this.allPlatforms.find(p => p.id.toString() === platformId);

      if (platform) {
        // For platform-specific "All Categories", display all categories from this platform
        // Sort categories by sort field before adding them
        const sortedCategories = [...platform.categories]
          .filter(category => !category.hide)
          .sort((a, b) => a.sort - b.sort);

        sortedCategories.forEach(category => {
          // Add each category to the display list with platform info
          const categoryWithPlatform = {
            ...category,
            platformName: platform.name,
            platformIcon: platform.icon,
            // Add translation keys for use in the template
            isAllPlatforms: false,
            isAllCategories: false
          };
          this.displayCategories.push(categoryWithPlatform);
        });
        return;
      }
    }

    // Handle combined platform_category ID format
    if (category.id.includes('_') && !category.id.includes('_all')) {
      const [platformId, categoryId] = category.id.split('_');
      const platform = this.allPlatforms.find(p => p.id.toString() === platformId);

      if (platform) {
        const foundCategory = platform.categories.find(c =>
          c.id.toString() === categoryId
        );

        if (foundCategory) {
          // Add platform info to the category
          const categoryWithPlatform = {
            ...foundCategory,
            platformName: platform.name,
            platformIcon: platform.icon,
            // Add translation keys for use in the template
            isAllPlatforms: false,
            isAllCategories: false
          };
          this.currentCategory = categoryWithPlatform;
        }
        return;
      }
    }

    // Standard category selection (when a specific platform is selected)
    const selectedPlatformData = this.allPlatforms.find(p =>
      this.selectedPlatform && p.id.toString() === this.selectedPlatform.id
    );

    if (selectedPlatformData) {
      // Find the selected category in the selected platform
      const foundCategory = selectedPlatformData.categories.find(c =>
        c.id.toString() === category.id
      );

      if (foundCategory) {
        // Add platform info to the category
        const categoryWithPlatform = {
          ...foundCategory,
          platformName: selectedPlatformData.name,
          platformIcon: selectedPlatformData.icon,
          // Add translation keys for use in the template
          isAllPlatforms: false,
          isAllCategories: false
        };
        this.currentCategory = categoryWithPlatform;
      }
    }
  }

  // Helper methods for displaying platform and category info
  showPlatformCategoryInfo(category: ExtendedIconBaseModel | undefined, _service: any): boolean {
    if (!category || !category.id) return false;
    return category.id === 'all' || category.id.includes('_all');
  }

  getPlatformName(service: any): string {
    return service.platformName || '';
  }

  getCategoryName(service: any): string {
    return service.categoryName ? `| ${service.categoryName}` : '';
  }

  // Method to apply the selected platform and category
  private applySelections(): void {
    // Temporarily enable display updates
    this.updateDisplayOnSelection = true;

    // Reset display containers
    this.displayCategories = [];
    this.currentCategory = null;

    // Apply the pending platform selection if available
    if (this.pendingPlatform) {
      // This will update the category options and select the first category
      const platform = this.pendingPlatform;

      if (platform.id === 'all') {
        // If "All Platforms" is selected
        if (!this.pendingCategory || this.pendingCategory.id === 'all') {
          // If "All Categories" is also selected, show all categories from all platforms
          this.allPlatforms.forEach(platformData => {
            // Sort categories by sort field before adding them
            const sortedCategories = [...platformData.categories]
              .filter(category => !category.hide)
              .sort((a, b) => a.sort - b.sort);

            sortedCategories.forEach(category => {
              // Add each category to the display list with platform info
              const categoryWithPlatform = {
                ...category,
                platformName: platformData.name,
                platformIcon: platformData.icon,
                isAllPlatforms: false,
                isAllCategories: false
              };
              this.displayCategories.push(categoryWithPlatform);
            });
          });
        } else if (this.pendingCategory.id.includes('_')) {
          // If a specific category is selected with "All Platforms"
          const [platformId, categoryId] = this.pendingCategory.id.split('_');
          const platform = this.allPlatforms.find(p => p.id.toString() === platformId);

          if (platform) {
            const foundCategory = platform.categories.find(c =>
              c.id.toString() === categoryId
            );

            if (foundCategory) {
              // Add platform info to the category
              const categoryWithPlatform = {
                ...foundCategory,
                platformName: platform.name,
                platformIcon: platform.icon,
                isAllPlatforms: false,
                isAllCategories: false
              };
              this.currentCategory = categoryWithPlatform;
            }
          }
        }
      } else {
        // A specific platform is selected
        const selectedPlatformData = this.allPlatforms.find(p => p.id.toString() === platform.id);

        if (selectedPlatformData && this.pendingCategory) {
          const category = this.pendingCategory;

          // Handle platform-specific "All Categories" option
          if (category.id.includes('_all') || category.id === 'all') {
            // Show all categories from this platform
            selectedPlatformData.categories
              .filter(category => !category.hide)
              .forEach(category => {
                // Add each category to the display list with platform info
                const categoryWithPlatform = {
                  ...category,
                  platformName: selectedPlatformData.name,
                  platformIcon: selectedPlatformData.icon,
                  isAllPlatforms: false,
                  isAllCategories: false
                };
                this.displayCategories.push(categoryWithPlatform);
              });
          }
          // Handle specific category selection
          else {
            // Try to find the category directly
            let foundCategory = selectedPlatformData.categories.find(c =>
              c.id.toString() === category.id
            );

            // If not found directly, check if it's a combined ID
            if (!foundCategory && category.id.includes('_')) {
              const [platformId, categoryId] = category.id.split('_');
              if (platformId === selectedPlatformData.id.toString()) {
                foundCategory = selectedPlatformData.categories.find(c =>
                  c.id.toString() === categoryId
                );
              }
            }

            if (foundCategory) {
              // Add platform info to the category
              const categoryWithPlatform = {
                ...foundCategory,
                platformName: selectedPlatformData.name,
                platformIcon: selectedPlatformData.icon,
                isAllPlatforms: false,
                isAllCategories: false
              };
              this.currentCategory = categoryWithPlatform;
            }
          }
        }
      }
    }

    // Disable display updates again
    this.updateDisplayOnSelection = false;
  }

  // Filter method - called when the Filter button is clicked
  applyFilter(searchText: string): void {
    // First apply the pending selections
    this.applySelections();

    // Then apply the search filter
    this.searchTerm = searchText.toLowerCase().trim();

    // If this is the first time filtering, save the unfiltered state
    if (!this.isFilterApplied) {
      this.unfilteredDisplayCategories = [...this.displayCategories];
      this.unfilteredCurrentCategory = this.currentCategory;
      this.isFilterApplied = true;
    }

    // Only apply search filter if there's a search term
    if (this.searchTerm) {
      // Filter logic for displayCategories
      if (this.displayCategories.length > 0) {
        this.displayCategories = this.displayCategories.map(category => {
          // Create a new category object with filtered services
          const filteredCategory = { ...category };
          // Only filter by service name, not description
          filteredCategory.services = category.services.filter(service =>
            service.name.toLowerCase().includes(this.searchTerm)
          );
          return filteredCategory;
        }).filter(category => category.services.length > 0); // Only keep categories with matching services
      }

      // Filter logic for currentCategory
      if (this.currentCategory) {
        // Only filter by service name, not description
        const filteredServices = this.currentCategory.services.filter(service =>
          service.name.toLowerCase().includes(this.searchTerm)
        );

        if (filteredServices.length > 0) {
          // Create a new category object with filtered services
          this.currentCategory = {
            ...this.currentCategory,
            services: filteredServices
          };
        } else {
          // No matching services in current category
          this.currentCategory = null;
        }
      }
    }
  }

  // Reset method - called when the Reset button is clicked
  resetFilter(searchInput: HTMLInputElement): void {
    // Clear the search input
    searchInput.value = '';
    this.searchTerm = '';

    // If filtering was applied, restore the original state
    if (this.isFilterApplied) {
      this.displayCategories = [...this.unfilteredDisplayCategories];
      this.currentCategory = this.unfilteredCurrentCategory;
      this.isFilterApplied = false;
    }

    // Reset pending selections
    this.pendingPlatform = null;
    this.pendingCategory = null;

    // Temporarily enable display updates for resetting dropdowns
    this.updateDisplayOnSelection = true;

    // Reset dropdowns to default selections
    if (this.platformOptions.length > 0) {
      this.onPlatformSelected(this.platformOptions[0]); // Select 'All Platforms'
    }

    // Apply the reset to display the default view
    this.applySelections();

    // Disable display updates again
    this.updateDisplayOnSelection = false;
  }

  // Check if a service is in loading state
  isServiceLoading(serviceId: number): boolean {
    return this.loadingServiceIds.has(serviceId);
  }

  // Handle favorite toggle
  onToggleFavorite(serviceId: number | null): void {
    if(!serviceId) return;
    // Add to loading set
    this.loadingServiceIds.add(serviceId);

    this.favoritesService.toggleFavorite(serviceId).subscribe({
      next: () => {
        // The favorites list will be updated automatically via the BehaviorSubject
      },
      error: (error) => {
        console.error('Error toggling favorite:', error);
      },
      complete: () => {
        // Remove from loading set when complete
        this.loadingServiceIds.delete(serviceId);
      }
    });
  }

  // Handle order button click
  orderService(serviceId: number): void {
    if (!serviceId) return;

    console.log('Ordering service with ID:', serviceId);
    // Navigate to the new order page with the service ID as a query parameter
    // Use window.location.href for direct navigation
    window.location.href = `/dashboard/new?serviceId=${serviceId}`;
  }
}
