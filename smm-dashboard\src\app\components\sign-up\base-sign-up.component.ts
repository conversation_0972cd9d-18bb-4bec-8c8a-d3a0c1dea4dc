import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { AbstractControl } from '@angular/forms';
import { Observable } from 'rxjs';

// Services
import { SignUpLogicService, SignUpState } from './services/sign-up-logic.service';

@Component({
  template: '', // Base component has no template
  standalone: true
})
export class BaseSignUpComponent implements OnInit, OnDestroy {
  // Expose state as observable for template
  signUpState$: Observable<SignUpState>;

  constructor(protected signUpLogicService: SignUpLogicService) {
    this.signUpState$ = this.signUpLogicService.state$;
  }

  ngOnInit(): void {
    // SignUpLogicService handles all initialization
  }

  ngOnDestroy(): void {
    // SignUpLogicService is singleton, no cleanup needed
  }

  // Delegate methods to SignUpLogicService for template compatibility
  onSubmit(): void {
    this.signUpLogicService.onSubmit();
  }

  login(): void {
    this.signUpLogicService.login();
  }

  // Getters for form controls (used in template)
  get email(): AbstractControl | null {
    return this.signUpLogicService.email;
  }

  get username(): AbstractControl | null {
    return this.signUpLogicService.username;
  }

  get password(): AbstractControl | null {
    return this.signUpLogicService.password;
  }

  get confirmPassword(): AbstractControl | null {
    return this.signUpLogicService.confirmPassword;
  }

  get termsAgreed(): AbstractControl | null {
    return this.signUpLogicService.termsAgreed;
  }
}
