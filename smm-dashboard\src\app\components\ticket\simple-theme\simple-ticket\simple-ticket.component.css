/* Simple Ticket Theme - Clean Design matching Header/Sidebar */
.simple-ticket-container {
  padding: 1.5rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #f8fafc;
  min-height: calc(100vh - 80px);
}

/* Header Section */
.ticket-header {
  margin-bottom: 1.5rem;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(30, 41, 59, 0.08);
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.header-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(30, 41, 59, 0.2);
}

.header-icon svg {
  width: 20px;
  height: 20px;
}

.header-text {
  flex: 1;
}

.header-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.header-subtitle {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
}

.new-ticket-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  color: white;
  border: none;
  border-radius: 10px;
  padding: 0.75rem 1.25rem;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(30, 41, 59, 0.2);
}

.new-ticket-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(30, 41, 59, 0.3);
}

.btn-icon {
  width: 14px;
  height: 14px;
}

/* Search Section */
.search-section {
  margin-bottom: 1.5rem;
}

.search-container {
  background: white;
  border-radius: 12px;
  padding:3rem;
  box-shadow: 0 2px 8px rgba(30, 41, 59, 0.06);
  border: 1px solid rgba(148, 163, 184, 0.1);
  margin-bottom: 0;
}

/* Custom Search Box Styling for Simple Ticket */
.simple-ticket-search-container {
  width: 100%;
  max-width: 100%;
  margin: 0;
}

.simple-ticket-search-input {
  background: #f8fafc !important;
  border: 1px solid rgba(148, 163, 184, 0.2) !important;
  color: #1e293b !important;
  font-size: 0.875rem !important;
  padding: 12px 16px !important;
  height: 46px !important;
}

.simple-ticket-search-input:focus {
  background: white !important;
  border-color: #1e293b !important;
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(30, 41, 59, 0.1) !important;
}

.simple-ticket-search-button {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%) !important;
  color: white !important;
  font-weight: 600 !important;
  border: none !important;
  transition: all 0.3s ease !important;
  padding: 12px 20px !important;
  height: 46px !important;
  min-width: 100px !important;
}

.simple-ticket-search-button:hover {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(30, 41, 59, 0.2) !important;
}

/* Content Section */
.content-section {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(30, 41, 59, 0.08);
  border: 1px solid rgba(148, 163, 184, 0.1);
  min-height: 400px;
}

/* Loading */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

/* Tickets Grid */
.tickets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1rem;
}

/* Empty State */
.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 2rem 1rem;
}

.empty-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  color: white;
}

.empty-icon svg {
  width: 28px;
  height: 28px;
}

.empty-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.empty-subtitle {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0 0 1.5rem 0;
}

.empty-action-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  color: white;
  border: none;
  border-radius: 10px;
  padding: 0.75rem 1.25rem;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(30, 41, 59, 0.2);
}

.empty-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(30, 41, 59, 0.3);
}

/* Ticket Cards */
.ticket-card {
  background: white;
  border-radius: 12px;
  padding: 1.25rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(148, 163, 184, 0.1);
  box-shadow: 0 2px 8px rgba(30, 41, 59, 0.06);
}

.ticket-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(30, 41, 59, 0.12);
  border-color: rgba(30, 41, 59, 0.2);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.ticket-id .id-label {
  font-weight: 700;
  color: #1e293b;
  font-size: 1rem;
}

.card-content {
  margin-bottom: 1rem;
}

.ticket-subject {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.ticket-description {
  color: #64748b;
  line-height: 1.5;
  margin: 0;
  font-size: 0.875rem;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 0.75rem;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.update-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  font-size: 0.75rem;
}

.time-icon {
  width: 12px;
  height: 12px;
}

.card-action {
  color: #1e293b;
  transition: all 0.3s ease;
}

.ticket-card:hover .card-action {
  transform: translateX(2px);
}

.action-icon {
  width: 14px;
  height: 14px;
}

/* Pagination */
.pagination-section {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
}

.pagination-btn,
.pagination-btn-active,
.pagination-btn-disabled {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.pagination-btn {
  background: white;
  color: #64748b;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.pagination-btn:hover {
  background: #f8fafc;
  border-color: #1e293b;
  color: #1e293b;
}

.pagination-btn-active {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(30, 41, 59, 0.2);
}

.pagination-btn-disabled {
  background: #f8fafc;
  color: #cbd5e0;
  border: 1px solid rgba(148, 163, 184, 0.1);
  cursor: not-allowed;
}

.pagination-dots {
  color: #64748b;
  font-weight: 600;
  padding: 0 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .simple-ticket-container {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .header-text {
    order: 1;
  }

  .header-icon {
    order: 0;
  }

  .new-ticket-btn {
    order: 2;
  }

  .tickets-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .ticket-card {
    padding: 1rem;
  }

  .header-title {
    font-size: 1.25rem;
  }

  .simple-ticket-search-container {
    max-width: 100%;
  }

  .search-container {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .simple-ticket-search-input {
    font-size: 16px !important; /* Prevent zoom on iOS */
    height: 44px !important;
    padding: 10px 14px !important;
  }

  .simple-ticket-search-button {
    height: 44px !important;
    padding: 10px 16px !important;
    min-width: 80px !important;
  }
}
