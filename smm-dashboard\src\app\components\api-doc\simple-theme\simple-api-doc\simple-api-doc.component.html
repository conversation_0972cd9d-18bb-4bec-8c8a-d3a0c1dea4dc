<div class="simple-api-doc-container">
  <!-- Header -->
  <div class="page-header">
    <h1 class="page-title">{{ 'api_doc.title' | translate }}</h1>
  </div>

  <div class="content-layout">
    <!-- Services Sidebar -->
    <div class="sidebar">
      <div class="sidebar-content">
        <h3 class="sidebar-title">{{ 'api_doc.services' | translate }}</h3>
        <ul class="services-list">
          <li *ngFor="let serviceId of services"
              class="service-item"
              [class.active]="selectedService === serviceId"
              (click)="selectService(serviceId)">
            {{ serviceData[serviceId].name }}
          </li>
        </ul>
      </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
      <!-- API Details -->
      <div class="content-card">
        <h3 class="card-title">{{ 'api_doc.api_details' | translate }}</h3>
        <div class="details-grid">
          <div class="detail-row">
            <span class="detail-label">{{ 'api_doc.api_url' | translate }}</span>
            <span class="detail-value">{{ apiDetails.url }}</span>
          </div>

          <div class="detail-row">
            <span class="detail-label">{{ 'api_doc.api_key' | translate }}</span>
            <div class="api-key-row">
              <span class="detail-value api-key">{{ apiDetails.key }}</span>
              <button
                class="refresh-button"
                [class.loading]="isGeneratingApiKey"
                [disabled]="isGeneratingApiKey"
                (click)="generateApiKey()"
                title="Generate new API key">
                <fa-icon [icon]="['fas', 'sync-alt']" [spin]="isGeneratingApiKey"></fa-icon>
              </button>
            </div>
          </div>

          <div class="detail-row">
            <span class="detail-label">{{ 'api_doc.https_method' | translate }}</span>
            <span class="detail-value method">{{ apiDetails.method }}</span>
          </div>

          <div class="detail-row">
            <span class="detail-label">{{ 'api_doc.content_type' | translate }}</span>
            <span class="detail-value">{{ apiDetails.contentType }}</span>
          </div>

          <div class="detail-row">
            <span class="detail-label">{{ 'api_doc.responsive' | translate }}</span>
            <span class="detail-value">{{ apiDetails.responsive }}</span>
          </div>
        </div>
      </div>

      <!-- Service Info -->
      <div class="content-card">
        <h3 class="card-title">{{ serviceData[selectedService].name }}</h3>
      </div>

      <!-- Parameters -->
      <div class="content-card">
        <h4 class="card-title">{{ 'api_doc.parameters' | translate }}</h4>
        <div class="table-wrapper">
          <table class="params-table">
            <thead>
              <tr>
                <th>{{ 'api_doc.parameter' | translate }}</th>
                <th>{{ 'api_doc.description' | translate }}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let param of parameters">
                <td class="param-name">{{ param.name }}</td>
                <td class="param-description">{{ param.description }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Example Response -->
      <div class="content-card">
        <h4 class="card-title">{{ 'api_doc.example_responsive' | translate }}</h4>
        <div class="code-wrapper">
          <pre class="code-block"><code>{{ codeExample }}</code></pre>
        </div>
      </div>
    </div>
  </div>
</div>
