/* Simple Service Label Styles */

.simple-no-service-message {
  @apply text-center py-3 px-4 text-gray-600 bg-gray-50 rounded-lg border border-gray-200;
  font-size: 0.875rem;
  font-weight: 500;
}

.simple-header-container {
  @apply mb-2;
}

.simple-service-row {
  @apply flex items-center gap-3;
}

.simple-icon-container {
  @apply flex-shrink-0;
}

.simple-service-icon {
  @apply w-6 h-6;
}

.simple-service-name {
  @apply flex-1 overflow-hidden;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  
  /* Text truncation */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Full Service Card */
.simple-service-card {
  @apply bg-white rounded-lg  ;

}


.simple-service-header {
  @apply flex items-center gap-3 mb-3;
}

.simple-service-id {
  @apply bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-semibold;
  min-width: fit-content;
}

.simple-service-title {
  @apply flex-1 font-medium text-gray-900;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

/* Price Section */
.simple-price-section {
  @apply flex items-center gap-2 mb-3;
  flex-wrap: wrap;
}

.simple-original-price {
  @apply text-gray-500 line-through;
  font-size: 0.875rem;
  font-weight: 500;
}

.simple-discount-badge {
  @apply bg-red-500 text-white px-2 py-1 rounded text-xs font-semibold;
}

.simple-final-price {
  @apply text-green-600 font-bold;
  font-size: 1rem;
}

/* Tags Section */
.simple-tags-section {
  @apply flex flex-wrap gap-2;
}

.simple-tag {
  @apply text-xs;
}

/* Responsive Design */
@media (max-width: 640px) {
  .simple-service-card {
    @apply p-3;
  }
  
  .simple-service-header {
    @apply gap-2;
  }
  
  .simple-service-title {
    font-size: 0.8125rem;
  }
  
  .simple-price-section {
    @apply gap-1;
  }
  
  .simple-final-price {
    font-size: 0.875rem;
  }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  .simple-service-card {
    @apply bg-gray-800 border-gray-700;
  }
  
  .simple-service-title {
    @apply text-gray-100;
  }
  
  .simple-service-name {
    color: #d1d5db;
  }
  
  .simple-no-service-message {
    @apply text-gray-300 bg-gray-800 border-gray-700;
  }
}
