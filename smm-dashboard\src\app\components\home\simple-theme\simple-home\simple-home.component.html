<!-- Simple Classic Theme Layout -->
<div *ngIf="homeState$ | async as homeState">

  <!-- Fixed Notification Banner -->
  <div class="notification-banner" *ngIf="homeState.hasFixedNotification">
    <app-simple-notification-home
      [title]="homeState.fixedNotificationTitle"
      [content]="homeState.fixedNotificationContent">
    </app-simple-notification-home>
  </div>

  <!-- Platform Selection -->
  <section class="platforms-section">
    <h2 class="section-title">
      {{ 'simple_theme.platforms.select' | translate }}
    </h2>

    <div class="platforms-grid">
      <div *ngFor="let platform of homeState.platforms; trackBy: trackByPlatformId"
           class="platform-card"
           [class.platform-selected]="homeState.selectedPlatformId === platform.id"
           (click)="onPlatformSelected(platform)">

        <div class="platform-content">
          <div class="platform-icon">
            <app-social-icon
              *ngIf="platform.icon"
              [icon]="platform.icon">
            </app-social-icon>
          </div>
          <div class="platform-details">
            <h3 class="platform-name">{{ platform.name | translate }}</h3>
            <p class="platform-services">{{ platform.categories.length || 0 }} {{ 'platform.services' | translate }}</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Main Content -->
  <main class="main-content">
    <div class="content-wrapper">

      <!-- Left Column - Order Form -->
      <div class="content-primary">
        <div class="panel order-panel">
          <h3 class="panel-title">
            {{ 'simple_theme.order.new' | translate }}
          </h3>
          <div class="panel-body">
            <app-simple-new-order></app-simple-new-order>
          </div>
        </div>
      </div>

      <!-- Right Column - Service Info & Favorites -->
      <div class="content-secondary">

        <!-- Service Information Panel -->
        <div class="panel service-panel">
          <h3 class="panel-title">
            {{ 'simple_theme.service.information' | translate }}
          </h3>
          <div class="panel-body">
            <app-simple-service-info></app-simple-service-info>
          </div>
        </div>

        <!-- Favorite Services Panel -->
      
      </div>
    </div>
  </main>

</div>
