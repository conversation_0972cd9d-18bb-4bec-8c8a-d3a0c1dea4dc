import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TagLabelComponent } from '../../tag-label/tag-label.component';
import { IconsModule } from '../../../../icons/icons.module';
import { SocialIconComponent } from "../../social-icon/social-icon.component";
import { TranslateModule } from '@ngx-translate/core';
import { CurrencyConvertPipe } from '../../../../core/pipes/currency-convert.pipe';
import { BaseServiceLabelComponent } from '../base-service-label.component';
import { ServiceLabelLogicService } from '../service-label-logic.service';

@Component({
  selector: 'app-simple-service-label',
  standalone: true,
  imports: [CommonModule, TagLabelComponent, IconsModule, SocialIconComponent, TranslateModule, CurrencyConvertPipe],
  templateUrl: './simple-service-label.component.html',
  styleUrls: ['./simple-service-label.component.css']
})
export class SimpleServiceLabelComponent extends BaseServiceLabelComponent {

  constructor(serviceLabelLogicService: ServiceLabelLogicService) {
    super(serviceLabelLogicService);
  }
}
