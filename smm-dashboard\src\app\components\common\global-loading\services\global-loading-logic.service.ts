import { Injectable, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { LoadingService } from '../../../../core/services/loading.service';

export interface GlobalLoadingState {
  isLoading: boolean;
  loadingMessage: string;
}

@Injectable({
  providedIn: 'root'
})
export class GlobalLoadingLogicService implements OnDestroy {
  private stateSubject = new BehaviorSubject<GlobalLoadingState>({
    isLoading: false,
    loadingMessage: ''
  });

  public state$ = this.stateSubject.asObservable();
  private subscription = new Subscription();

  constructor(private loadingService: LoadingService) {
    this.initializeSubscriptions();
  }

  private initializeSubscriptions(): void {
    // Subscribe to loading state changes
    this.subscription.add(
      this.loadingService.loading$.subscribe(loading => {
        this.updateState({ isLoading: loading });
      })
    );

    // Subscribe to loading message changes
    this.subscription.add(
      this.loadingService.loadingMessage$.subscribe(message => {
        this.updateState({ loadingMessage: message });
      })
    );
  }

  private updateState(partialState: Partial<GlobalLoadingState>): void {
    const currentState = this.stateSubject.value;
    this.stateSubject.next({ ...currentState, ...partialState });
  }

  ngOnDestroy(): void {
    this.destroy();
  }

  destroy(): void {
    this.subscription.unsubscribe();
  }
}
