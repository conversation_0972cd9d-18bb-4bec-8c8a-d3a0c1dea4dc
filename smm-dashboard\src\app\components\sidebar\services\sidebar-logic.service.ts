import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { BehaviorSubject, Subscription } from 'rxjs';

// Services
import { SidebarService } from '../../../core/services/sidebar.service';
import { NavigationService } from '../../../core/services/navigation.service';
import { ThemeService, LayoutTheme } from '../../../core/services/theme.service';
import { AppAssetsService } from '../../../core/services/app-assets.service';

// Data
import { sideNavItems } from '../../../data/side-nav-dashboard.data';

export interface SidebarState {
  // UI state
  isOpen: boolean;
  currentSidebarStyle: string;
  logoUrl: string;
  
  // Menu data
  menuSections: any[];
  
  // Theme management
  currentTheme: LayoutTheme;
}

@Injectable({
  providedIn: 'root'
})
export class SidebarLogicService {
  private subscriptions: Subscription[] = [];

  // State management
  private _state$ = new BehaviorSubject<SidebarState>({
    isOpen: false,
    currentSidebarStyle: 'standard',
    logoUrl: 'assets/images/logo.png',
    menuSections: sideNavItems,
    currentTheme: LayoutTheme.DEFAULT
  });

  // Public state observable
  public readonly state$ = this._state$.asObservable();

  // Current state getter
  private get currentState(): SidebarState {
    return this._state$.value;
  }

  constructor(
    private router: Router,
    private sidebarService: SidebarService,
    private navigationService: NavigationService,
    private themeService: ThemeService,
    private appAssetsService: AppAssetsService
  ) {
    this.initialize();
  }

  // Public getters for template access
  get isOpen(): boolean {
    return this.currentState.isOpen;
  }

  get currentSidebarStyle(): string {
    return this.currentState.currentSidebarStyle;
  }

  get logoUrl(): string {
    return this.currentState.logoUrl;
  }

  get menuSections(): any[] {
    return this.currentState.menuSections;
  }

  get currentTheme(): LayoutTheme {
    return this.currentState.currentTheme;
  }

  // Initialize service
  private initialize(): void {
    this.subscribeToNavigationChanges();
    this.subscribeToSidebarStyleChanges();
    this.subscribeToThemeChanges();
    this.subscribeToLogoChanges();

    // Set initial active state based on current URL
    this.setInitialActiveState();
  }

  // Update state helper
  private updateState(updates: Partial<SidebarState>): void {
    const currentState = this._state$.value;
    this._state$.next({ ...currentState, ...updates });
  }

  // Subscription methods
  private subscribeToNavigationChanges(): void {
    this.subscriptions.push(
      this.navigationService
        .currentURL$()
        .subscribe((currentURL) =>
          this.determineIfActive(currentURL.replace(/#.*$/, ''))
        )
    );
  }

  private subscribeToSidebarStyleChanges(): void {
    this.subscriptions.push(
      this.themeService.sidebarStyle$.subscribe(style => {
        this.updateState({ currentSidebarStyle: style });
      })
    );
  }

  private subscribeToThemeChanges(): void {
    this.subscriptions.push(
      this.themeService.currentLayoutTheme$.subscribe((theme: LayoutTheme) => {
        this.updateState({ currentTheme: theme });
      })
    );
  }

  private subscribeToLogoChanges(): void {
    this.subscriptions.push(
      this.appAssetsService.logoUrl$.subscribe(url => {
        this.updateState({ logoUrl: url });
      })
    );
  }

  // Helper methods
  private setInitialActiveState(): void {
    // Get current URL and set initial active state
    const currentUrl = this.router.url.replace(/#.*$/, '');
    this.determineIfActive(currentUrl);
  }

  private determineIfActive(url: string): void {
    const updatedMenuSections = this.currentState.menuSections.map(section => ({
      ...section,
      items: section.items.map((item: any) => ({
        ...item,
        isActive: item.link === url
      }))
    }));

    this.updateState({ menuSections: updatedMenuSections });
  }

  // Public methods for component interaction
  setIsOpen(isOpen: boolean): void {
    this.updateState({ isOpen });
  }

  toggleSidebar(): void {
    this.sidebarService.toggleSidebar();
  }

  navigateTo(link: string): void {
    this.router.navigate([link]);
    this.sidebarService.setSidebarState(false);
  }

  handleMenuClick(event: MouseEvent, link: string): void {
    // Check if Ctrl key is pressed, middle mouse button is clicked, or auxclick event
    if (event.ctrlKey || event.metaKey || event.button === 1 || event.type === 'auxclick') {
      // Prevent default navigation
      event.preventDefault();
      event.stopPropagation();

      // Open in new tab
      const url = this.router.serializeUrl(this.router.createUrlTree([link]));
      window.open(url, '_blank');
    } else if (event.type === 'click' && event.button === 0) {
      // Normal left click - let routerLink handle it, but close sidebar on mobile
      this.sidebarService.setSidebarState(false);
    }
  }

  onLogoClick(): void {
    this.router.navigate(['/']);
    this.sidebarService.setSidebarState(false);
  }

  // Cleanup method
  destroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.subscriptions = [];
  }
}
