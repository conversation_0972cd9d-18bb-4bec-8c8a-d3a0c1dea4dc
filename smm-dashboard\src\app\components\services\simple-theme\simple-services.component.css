/* Simple Services Theme */
.simple-services-container {
  padding: 1rem;
  background: #f8fafc;
  min-height: 100vh;
}

/* Header */
.simple-header {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.simple-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

/* Filters */
.simple-filters {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.simple-filter-row {
  display: grid;
  grid-template-columns: 1fr 1fr 2fr;
  gap: 1rem;
  align-items: end;
}

.simple-filter-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.simple-search-item {
  grid-column: span 1;
}

.simple-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.simple-search-container {
  display: flex;
  gap: 0.5rem;
}

.simple-search-input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
}

.simple-search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.simple-search-actions {
  display: flex;
  gap: 0.5rem;
}

/* Buttons */
.simple-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.simple-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.simple-btn-primary {
  background: #3b82f6;
  color: white;
}

.simple-btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.simple-btn-secondary {
  background: #6b7280;
  color: white;
}

.simple-btn-secondary:hover:not(:disabled) {
  background: #4b5563;
}

.simple-order-btn {
  width: 100%;
  justify-content: center;
}

/* Services Content */
.simple-services-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Category Section */
.simple-category-section {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.simple-category-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f3f4f6;
}

.simple-category-info {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.simple-platform-icon {
  width: 3rem;
  height: 3rem;
  flex-shrink: 0;
}

.simple-category-details {
  flex: 1;
}

.simple-category-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.simple-category-desc {
  color: #6b7280;
  margin: 0 0 0.75rem 0;
  line-height: 1.5;
}

.simple-category-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
}

.simple-platform-name {
  color: #3b82f6;
  font-weight: 500;
}

.simple-service-count {
  color: #6b7280;
}

/* Services Grid */
.simple-services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

/* Service Card */
.simple-service-card {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.2s;
}

.simple-service-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.simple-service-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.simple-service-info {
  flex: 1;
}

.simple-service-name {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
  line-height: 1.4;
}

.simple-service-id {
  font-size: 0.75rem;
  color: #6b7280;
}

.simple-service-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.simple-service-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
}

.simple-service-desc {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.4;
  margin-bottom: 0.5rem;
}

.simple-price {
  font-weight: 600;
  color: #059669;
}

.simple-service-labels {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-top: 0.5rem;
}

.simple-label-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  margin-right: 0.5rem;
  margin-bottom: 0.25rem;
}

.simple-label-success {
  background-color: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.simple-label-warning {
  background-color: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

.simple-service-actions {
  margin-top: auto;
}

/* Categories List */
.simple-categories-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Empty State */
.simple-empty-state {
  text-align: center;
  padding: 3rem 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.simple-empty-icon {
  font-size: 3rem;
  color: #d1d5db;
  margin-bottom: 1rem;
}

.simple-empty-state h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.5rem 0;
}

.simple-empty-state p {
  color: #6b7280;
  margin: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .simple-services-container {
    padding: 0.5rem;
  }
  
  .simple-filter-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .simple-search-item {
    grid-column: span 1;
  }
  
  .simple-search-container {
    flex-direction: column;
  }
  
  .simple-search-actions {
    justify-content: stretch;
  }
  
  .simple-search-actions .simple-btn {
    flex: 1;
  }
  
  .simple-services-grid {
    grid-template-columns: 1fr;
  }
  
  .simple-category-info {
    flex-direction: column;
    text-align: center;
  }
  
  .simple-category-meta {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .simple-service-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .simple-service-labels {
    justify-content: center;
  }
}
