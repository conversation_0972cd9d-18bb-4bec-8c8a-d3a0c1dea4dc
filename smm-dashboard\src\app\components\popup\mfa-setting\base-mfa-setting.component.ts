import { Component, EventEmitter, Output, ElementRef, OnInit, OnD<PERSON>roy, inject } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Subscription } from 'rxjs';
import { MfaSettingLogicService, MfaSettingState } from './mfa-setting-logic.service';

@Component({
  template: ''
})
export abstract class BaseMfaSettingComponent implements OnInit, OnDestroy {
  @Output() close = new EventEmitter<void>();
  @Output() enabled = new EventEmitter<void>();

  protected mfaLogic = inject(MfaSettingLogicService);
  protected elementRef = inject(ElementRef);
  
  private subscription = new Subscription();
  
  // State properties
  mfaForm: FormGroup = this.mfaLogic.currentState.mfaForm;
  isLoading = false;
  qrCodeImage = '';
  secretKey = '';
  showPassword = false;

  ngOnInit(): void {
    // Subscribe to state changes
    this.subscription.add(
      this.mfaLogic.state$.subscribe((state: MfaSettingState) => {
        this.mfaForm = state.mfaForm;
        this.isLoading = state.isLoading;
        this.qrCodeImage = state.qrCodeImage;
        this.secretKey = state.secretKey;
        this.showPassword = state.showPassword;
      })
    );

    // Initialize MFA setting
    this.mfaLogic.initialize();
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  /**
   * Generate MFA QR code and secret key
   */
  generateMfaCode(): void {
    this.mfaLogic.generateMfaCode();
  }

  /**
   * Verify MFA code and enable 2FA
   */
  onSubmit(): void {
    this.mfaLogic.verifyMfa(() => {
      this.enabled.emit();
      this.onClose();
    });
  }

  /**
   * Prevent event propagation when clicking inside the popup
   */
  onPopupClick(event: MouseEvent): void {
    event.stopPropagation();
  }

  /**
   * Close the popup
   */
  onClose(): void {
    console.log('MFA Setting - onClose called');
    this.close.emit();
  }

  /**
   * Toggle password visibility
   */
  togglePasswordVisibility(): void {
    this.mfaLogic.togglePasswordVisibility();
  }
}
