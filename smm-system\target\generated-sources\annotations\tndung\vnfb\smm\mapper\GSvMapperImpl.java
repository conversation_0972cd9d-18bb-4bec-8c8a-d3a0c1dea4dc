package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.processing.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.constant.enums.Currency;
import tndung.vnfb.smm.dto.ServiceLabel;
import tndung.vnfb.smm.dto.request.ServiceReq;
import tndung.vnfb.smm.dto.response.ApiProviderRes;
import tndung.vnfb.smm.dto.response.service.ServiceRes;
import tndung.vnfb.smm.dto.response.service.SuperServiceRes;
import tndung.vnfb.smm.dto.response.smm.SMMServiceRes;
import tndung.vnfb.smm.dto.response.smm.SMMServiceRes.SMMServiceResBuilder;
import tndung.vnfb.smm.entity.ApiProvider;
import tndung.vnfb.smm.entity.Category;
import tndung.vnfb.smm.entity.GService;
import tndung.vnfb.smm.entity.Platform;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class GSvMapperImpl implements GSvMapper {

    @Autowired
    private SpecialPriceMapper specialPriceMapper;

    @Override
    public void update(ServiceReq serviceReq, GService generalService) {
        if ( serviceReq == null ) {
            return;
        }

        generalService.setAddType( serviceReq.getAddType() );
        generalService.setApiServiceId( serviceReq.getApiServiceId() );
        generalService.setAutoSync( serviceReq.getAutoSync() );
        generalService.setCancelButton( serviceReq.getCancelButton() );
        generalService.setDescription( serviceReq.getDescription() );
        generalService.setIsFixedPrice( serviceReq.getIsFixedPrice() );
        generalService.setIsOverflow( serviceReq.getIsOverflow() );
        if ( generalService.getLabels() != null ) {
            List<ServiceLabel> list = serviceReq.getLabels();
            if ( list != null ) {
                generalService.getLabels().clear();
                generalService.getLabels().addAll( list );
            }
            else {
                generalService.setLabels( null );
            }
        }
        else {
            List<ServiceLabel> list = serviceReq.getLabels();
            if ( list != null ) {
                generalService.setLabels( new ArrayList<ServiceLabel>( list ) );
            }
        }
        generalService.setLimitFrom( serviceReq.getLimitFrom() );
        generalService.setLimitTo( serviceReq.getLimitTo() );
        generalService.setMax( serviceReq.getMax() );
        generalService.setMin( serviceReq.getMin() );
        generalService.setName( serviceReq.getName() );
        generalService.setOriginalPrice( serviceReq.getOriginalPrice() );
        generalService.setOverflow( serviceReq.getOverflow() );
        generalService.setPercent( serviceReq.getPercent() );
        generalService.setPercent1( serviceReq.getPercent1() );
        generalService.setPercent2( serviceReq.getPercent2() );
        generalService.setPrice( serviceReq.getPrice() );
        generalService.setPrice1( serviceReq.getPrice1() );
        generalService.setPrice2( serviceReq.getPrice2() );
        generalService.setRefill( serviceReq.getRefill() );
        generalService.setRefillDays( serviceReq.getRefillDays() );
        generalService.setSampleLink( serviceReq.getSampleLink() );
        generalService.setSpeedPerDay( serviceReq.getSpeedPerDay() );
        generalService.setSyncCancel( serviceReq.getSyncCancel() );
        generalService.setSyncMinMax( serviceReq.getSyncMinMax() );
        generalService.setSyncRefill( serviceReq.getSyncRefill() );
        generalService.setSyncStatus( serviceReq.getSyncStatus() );
        generalService.setType( serviceReq.getType() );
    }

    @Override
    public GService toEntity(ServiceReq req) {
        if ( req == null ) {
            return null;
        }

        GService gService = new GService();

        gService.setAddType( req.getAddType() );
        gService.setApiServiceId( req.getApiServiceId() );
        gService.setAutoSync( req.getAutoSync() );
        gService.setCancelButton( req.getCancelButton() );
        gService.setDescription( req.getDescription() );
        gService.setIsFixedPrice( req.getIsFixedPrice() );
        gService.setIsOverflow( req.getIsOverflow() );
        List<ServiceLabel> list = req.getLabels();
        if ( list != null ) {
            gService.setLabels( new ArrayList<ServiceLabel>( list ) );
        }
        gService.setLimitFrom( req.getLimitFrom() );
        gService.setLimitTo( req.getLimitTo() );
        gService.setMax( req.getMax() );
        gService.setMin( req.getMin() );
        gService.setName( req.getName() );
        gService.setOriginalPrice( req.getOriginalPrice() );
        gService.setOverflow( req.getOverflow() );
        gService.setPercent( req.getPercent() );
        gService.setPercent1( req.getPercent1() );
        gService.setPercent2( req.getPercent2() );
        gService.setPrice( req.getPrice() );
        gService.setPrice1( req.getPrice1() );
        gService.setPrice2( req.getPrice2() );
        gService.setRefill( req.getRefill() );
        gService.setRefillDays( req.getRefillDays() );
        gService.setSampleLink( req.getSampleLink() );
        gService.setSpeedPerDay( req.getSpeedPerDay() );
        gService.setSyncCancel( req.getSyncCancel() );
        gService.setSyncMinMax( req.getSyncMinMax() );
        gService.setSyncRefill( req.getSyncRefill() );
        gService.setSyncStatus( req.getSyncStatus() );
        gService.setType( req.getType() );

        return gService;
    }

    @Override
    public SMMServiceRes toSMM(GService entity) {
        if ( entity == null ) {
            return null;
        }

        SMMServiceResBuilder sMMServiceRes = SMMServiceRes.builder();

        sMMServiceRes.category( entityCategoryName( entity ) );
        if ( entity.getPrice() != null ) {
            sMMServiceRes.rate( entity.getPrice().doubleValue() );
        }
        sMMServiceRes.refill( entity.getRefill() );
        if ( entity.getId() != null ) {
            sMMServiceRes.service( entity.getId().intValue() );
        }
        sMMServiceRes.cancel( entity.getCancelButton() );
        sMMServiceRes.description( entity.getDescription() );
        sMMServiceRes.max( entity.getMax() );
        sMMServiceRes.min( entity.getMin() );
        sMMServiceRes.name( entity.getName() );
        if ( entity.getType() != null ) {
            sMMServiceRes.type( entity.getType().name() );
        }

        return sMMServiceRes.build();
    }

    @Override
    public List<SMMServiceRes> toSMM(List<GService> entity) {
        if ( entity == null ) {
            return null;
        }

        List<SMMServiceRes> list = new ArrayList<SMMServiceRes>( entity.size() );
        for ( GService gService : entity ) {
            list.add( toSMM( gService ) );
        }

        return list;
    }

    @Override
    public ServiceRes toRes(GService entity) {
        if ( entity == null ) {
            return null;
        }

        ServiceRes serviceRes = new ServiceRes();

        serviceRes.setIcon( entityCategoryPlatformIcon( entity ) );
        serviceRes.setAddType( entity.getAddType() );
        serviceRes.setAverageTime( entity.getAverageTime() );
        serviceRes.setCancelButton( entity.getCancelButton() );
        serviceRes.setDescription( entity.getDescription() );
        if ( entity.getId() != null ) {
            serviceRes.setId( entity.getId().intValue() );
        }
        serviceRes.setIsFixedPrice( entity.getIsFixedPrice() );
        serviceRes.setIsOverflow( entity.getIsOverflow() );
        List<ServiceLabel> list = entity.getLabels();
        if ( list != null ) {
            serviceRes.setLabels( new ArrayList<ServiceLabel>( list ) );
        }
        serviceRes.setMax( entity.getMax() );
        serviceRes.setMin( entity.getMin() );
        serviceRes.setName( entity.getName() );
        serviceRes.setOverflow( entity.getOverflow() );
        serviceRes.setPrice( entity.getPrice() );
        serviceRes.setRefill( entity.getRefill() );
        serviceRes.setRefillDays( entity.getRefillDays() );
        serviceRes.setSampleLink( entity.getSampleLink() );
        serviceRes.setSort( entity.getSort() );
        serviceRes.setSpecialPrices( specialPriceMapper.toLiteRes( entity.getSpecialPrices() ) );
        serviceRes.setSpeedPerDay( entity.getSpeedPerDay() );
        serviceRes.setType( entity.getType() );

        return serviceRes;
    }

    @Override
    public List<ServiceRes> toRes(List<GService> entities) {
        if ( entities == null ) {
            return null;
        }

        List<ServiceRes> list = new ArrayList<ServiceRes>( entities.size() );
        for ( GService gService : entities ) {
            list.add( toRes( gService ) );
        }

        return list;
    }

    @Override
    public Set<ServiceRes> toRes(Set<GService> entities) {
        if ( entities == null ) {
            return null;
        }

        Set<ServiceRes> set = new HashSet<ServiceRes>( Math.max( (int) ( entities.size() / .75f ) + 1, 16 ) );
        for ( GService gService : entities ) {
            set.add( toRes( gService ) );
        }

        return set;
    }

    @Override
    public SuperServiceRes toSuperRes(GService entity) {
        if ( entity == null ) {
            return null;
        }

        SuperServiceRes superServiceRes = new SuperServiceRes();

        Long id = entityCategoryId( entity );
        if ( id != null ) {
            superServiceRes.setCategoryId( id.intValue() );
        }
        superServiceRes.setAverageTime( entity.getAverageTime() );
        superServiceRes.setCancelButton( entity.getCancelButton() );
        superServiceRes.setDescription( entity.getDescription() );
        if ( entity.getId() != null ) {
            superServiceRes.setId( entity.getId().intValue() );
        }
        superServiceRes.setIsFixedPrice( entity.getIsFixedPrice() );
        superServiceRes.setIsOverflow( entity.getIsOverflow() );
        List<ServiceLabel> list = entity.getLabels();
        if ( list != null ) {
            superServiceRes.setLabels( new ArrayList<ServiceLabel>( list ) );
        }
        superServiceRes.setName( entity.getName() );
        superServiceRes.setOverflow( entity.getOverflow() );
        superServiceRes.setPrice( entity.getPrice() );
        superServiceRes.setRefill( entity.getRefill() );
        superServiceRes.setRefillDays( entity.getRefillDays() );
        superServiceRes.setSampleLink( entity.getSampleLink() );
        superServiceRes.setSort( entity.getSort() );
        superServiceRes.setSpecialPrices( specialPriceMapper.toLiteRes( entity.getSpecialPrices() ) );
        superServiceRes.setSpeedPerDay( entity.getSpeedPerDay() );
        superServiceRes.setAddType( entity.getAddType() );
        superServiceRes.setApiProvider( apiProviderToApiProviderRes( entity.getApiProvider() ) );
        superServiceRes.setApiServiceId( entity.getApiServiceId() );
        superServiceRes.setAutoSync( entity.getAutoSync() );
        superServiceRes.setLimitFrom( entity.getLimitFrom() );
        superServiceRes.setLimitTo( entity.getLimitTo() );
        superServiceRes.setMax( entity.getMax() );
        superServiceRes.setMin( entity.getMin() );
        superServiceRes.setOriginalPrice( entity.getOriginalPrice() );
        if ( entity.getPercent() != null ) {
            superServiceRes.setPercent( entity.getPercent().doubleValue() );
        }
        if ( entity.getPercent1() != null ) {
            superServiceRes.setPercent1( entity.getPercent1().doubleValue() );
        }
        if ( entity.getPercent2() != null ) {
            superServiceRes.setPercent2( entity.getPercent2().doubleValue() );
        }
        superServiceRes.setPrice1( entity.getPrice1() );
        superServiceRes.setPrice2( entity.getPrice2() );
        superServiceRes.setStatus( entity.getStatus() );
        superServiceRes.setSyncCancel( entity.getSyncCancel() );
        superServiceRes.setSyncMinMax( entity.getSyncMinMax() );
        superServiceRes.setSyncRefill( entity.getSyncRefill() );
        superServiceRes.setSyncStatus( entity.getSyncStatus() );
        superServiceRes.setType( entity.getType() );

        return superServiceRes;
    }

    @Override
    public List<SuperServiceRes> toSuperRes(List<GService> entities) {
        if ( entities == null ) {
            return null;
        }

        List<SuperServiceRes> list = new ArrayList<SuperServiceRes>( entities.size() );
        for ( GService gService : entities ) {
            list.add( toSuperRes( gService ) );
        }

        return list;
    }

    @Override
    public Set<SuperServiceRes> toSuperRes(Set<GService> entities) {
        if ( entities == null ) {
            return null;
        }

        Set<SuperServiceRes> set = new HashSet<SuperServiceRes>( Math.max( (int) ( entities.size() / .75f ) + 1, 16 ) );
        for ( GService gService : entities ) {
            set.add( toSuperRes( gService ) );
        }

        return set;
    }

    private String entityCategoryName(GService gService) {
        if ( gService == null ) {
            return null;
        }
        Category category = gService.getCategory();
        if ( category == null ) {
            return null;
        }
        String name = category.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String entityCategoryPlatformIcon(GService gService) {
        if ( gService == null ) {
            return null;
        }
        Category category = gService.getCategory();
        if ( category == null ) {
            return null;
        }
        Platform platform = category.getPlatform();
        if ( platform == null ) {
            return null;
        }
        String icon = platform.getIcon();
        if ( icon == null ) {
            return null;
        }
        return icon;
    }

    private Long entityCategoryId(GService gService) {
        if ( gService == null ) {
            return null;
        }
        Category category = gService.getCategory();
        if ( category == null ) {
            return null;
        }
        Long id = category.getId();
        if ( id == null ) {
            return null;
        }
        return id;
    }

    protected ApiProviderRes apiProviderToApiProviderRes(ApiProvider apiProvider) {
        if ( apiProvider == null ) {
            return null;
        }

        ApiProviderRes apiProviderRes = new ApiProviderRes();

        apiProviderRes.setBalance( apiProvider.getBalance() );
        apiProviderRes.setBalanceAlert( apiProvider.getBalanceAlert() );
        if ( apiProvider.getCurrency() != null ) {
            apiProviderRes.setCurrency( Enum.valueOf( Currency.class, apiProvider.getCurrency() ) );
        }
        apiProviderRes.setId( apiProvider.getId() );
        apiProviderRes.setName( apiProvider.getName() );
        apiProviderRes.setStatus( apiProvider.getStatus() );
        apiProviderRes.setUrl( apiProvider.getUrl() );

        return apiProviderRes;
    }
}
