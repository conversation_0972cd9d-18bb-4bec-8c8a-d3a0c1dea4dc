package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tndung.vnfb.smm.config.TenantContext;
import tndung.vnfb.smm.dto.request.TenantLanguageSettingsReq;
import tndung.vnfb.smm.dto.request.CustomLanguageReq;
import tndung.vnfb.smm.dto.response.TenantLanguageSettingsRes;
import tndung.vnfb.smm.dto.response.TenantDefaultLanguageRes;
import tndung.vnfb.smm.dto.response.CustomLanguageRes;
import tndung.vnfb.smm.dto.response.LanguageOptionRes;
import tndung.vnfb.smm.entity.Tenant;
import tndung.vnfb.smm.entity.TenantCustomLanguage;
import tndung.vnfb.smm.repository.tenant.TenantCustomLanguageRepository;
import tndung.vnfb.smm.service.TenantService;
import tndung.vnfb.smm.service.TenantSettingsService;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class TenantSettingsServiceImpl implements TenantSettingsService {

    private final TenantService tenantService;
    private final TenantCustomLanguageRepository customLanguageRepository;

    // Predefined languages with their flags
    private static final List<LanguageOptionRes> PREDEFINED_LANGUAGES = Arrays.asList(
        new LanguageOptionRes("vi", "Tiếng Việt", "fi fi-vn"),
        new LanguageOptionRes("en", "English", "fi fi-us"),
        new LanguageOptionRes("cn", "中文", "fi fi-cn"),
        new LanguageOptionRes("fr", "Français", "fi fi-fr"),
        new LanguageOptionRes("es", "Español", "fi fi-es"),
        new LanguageOptionRes("de", "Deutsch", "fi fi-de"),
        new LanguageOptionRes("ja", "日本語", "fi fi-jp"),
        new LanguageOptionRes("ko", "한국어", "fi fi-kr"),
        new LanguageOptionRes("ru", "Русский", "fi fi-ru"),
        new LanguageOptionRes("pt", "Português", "fi fi-pt"),
        new LanguageOptionRes("it", "Italiano", "fi fi-it"),
        new LanguageOptionRes("ar", "العربية", "fi fi-sa"),
        new LanguageOptionRes("hi", "हिन्दी", "fi fi-in"),
        new LanguageOptionRes("th", "ไทย", "fi fi-th"),
        new LanguageOptionRes("id", "Bahasa Indonesia", "fi fi-id"),
        new LanguageOptionRes("ms", "Bahasa Melayu", "fi fi-my"),
        new LanguageOptionRes("tl", "Filipino", "fi fi-ph"),
        new LanguageOptionRes("tr", "Türkçe", "fi fi-tr"),
        new LanguageOptionRes("nl", "Nederlands", "fi fi-nl"),
        new LanguageOptionRes("sv", "Svenska", "fi fi-se"),
        new LanguageOptionRes("no", "Norsk", "fi fi-no"),
        new LanguageOptionRes("da", "Dansk", "fi fi-dk"),
        new LanguageOptionRes("fi", "Suomi", "fi fi-fi"),
        new LanguageOptionRes("pl", "Polski", "fi fi-pl"),
        new LanguageOptionRes("cs", "Čeština", "fi fi-cz"),
        new LanguageOptionRes("hu", "Magyar", "fi fi-hu"),
        new LanguageOptionRes("ro", "Română", "fi fi-ro"),
        new LanguageOptionRes("bg", "Български", "fi fi-bg"),
        new LanguageOptionRes("hr", "Hrvatski", "fi fi-hr"),
        new LanguageOptionRes("sk", "Slovenčina", "fi fi-sk"),
        new LanguageOptionRes("sl", "Slovenščina", "fi fi-si"),
        new LanguageOptionRes("et", "Eesti", "fi fi-ee"),
        new LanguageOptionRes("lv", "Latviešu", "fi fi-lv"),
        new LanguageOptionRes("lt", "Lietuvių", "fi fi-lt"),
        new LanguageOptionRes("uk", "Українська", "fi fi-ua"),
        new LanguageOptionRes("be", "Беларуская", "fi fi-by"),
        new LanguageOptionRes("mk", "Македонски", "fi fi-mk"),
        new LanguageOptionRes("sq", "Shqip", "fi fi-al"),
        new LanguageOptionRes("sr", "Српски", "fi fi-rs"),
        new LanguageOptionRes("bs", "Bosanski", "fi fi-ba"),
        new LanguageOptionRes("me", "Crnogorski", "fi fi-me"),
        new LanguageOptionRes("is", "Íslenska", "fi fi-is"),
        new LanguageOptionRes("mt", "Malti", "fi fi-mt"),
        new LanguageOptionRes("cy", "Cymraeg", "fi fi-gb-wls"),
        new LanguageOptionRes("ga", "Gaeilge", "fi fi-ie"),
        new LanguageOptionRes("gd", "Gàidhlig", "fi fi-gb-sct"),
        new LanguageOptionRes("eu", "Euskera", "fi fi-es"),
        new LanguageOptionRes("ca", "Català", "fi fi-es"),
        new LanguageOptionRes("gl", "Galego", "fi fi-es"),
        new LanguageOptionRes("he", "עברית", "fi fi-il"),
        new LanguageOptionRes("fa", "فارسی", "fi fi-ir"),
        new LanguageOptionRes("ur", "اردو", "fi fi-pk"),
        new LanguageOptionRes("bn", "বাংলা", "fi fi-bd"),
        new LanguageOptionRes("ta", "தமிழ்", "fi fi-in"),
        new LanguageOptionRes("te", "తెలుగు", "fi fi-in"),
        new LanguageOptionRes("ml", "മലയാളം", "fi fi-in"),
        new LanguageOptionRes("kn", "ಕನ್ನಡ", "fi fi-in"),
        new LanguageOptionRes("gu", "ગુજરાતી", "fi fi-in"),
        new LanguageOptionRes("pa", "ਪੰਜਾਬੀ", "fi fi-in"),
        new LanguageOptionRes("mr", "मराठी", "fi fi-in"),
        new LanguageOptionRes("ne", "नेपाली", "fi fi-np"),
        new LanguageOptionRes("si", "සිංහල", "fi fi-lk"),
        new LanguageOptionRes("my", "မြန်မာ", "fi fi-mm"),
        new LanguageOptionRes("km", "ខ្មែរ", "fi fi-kh"),
        new LanguageOptionRes("lo", "ລາວ", "fi fi-la"),
        new LanguageOptionRes("ka", "ქართული", "fi fi-ge"),
        new LanguageOptionRes("hy", "Հայերեն", "fi fi-am"),
        new LanguageOptionRes("az", "Azərbaycan", "fi fi-az"),
        new LanguageOptionRes("kk", "Қазақша", "fi fi-kz"),
        new LanguageOptionRes("ky", "Кыргызча", "fi fi-kg"),
        new LanguageOptionRes("uz", "O'zbek", "fi fi-uz"),
        new LanguageOptionRes("tk", "Türkmen", "fi fi-tm"),
        new LanguageOptionRes("tg", "Тоҷикӣ", "fi fi-tj"),
        new LanguageOptionRes("mn", "Монгол", "fi fi-mn"),
        new LanguageOptionRes("sw", "Kiswahili", "fi fi-ke"),
        new LanguageOptionRes("am", "አማርኛ", "fi fi-et"),
        new LanguageOptionRes("zu", "isiZulu", "fi fi-za"),
        new LanguageOptionRes("af", "Afrikaans", "fi fi-za"),
        new LanguageOptionRes("xh", "isiXhosa", "fi fi-za")
    );

    @Override
    public TenantLanguageSettingsRes getLanguageSettings() {
        String tenantId = TenantContext.getCurrentTenant();
        log.debug("Getting language settings for tenant: {}", tenantId);

        Tenant tenant = tenantService.findByTenantId(tenantId)
                .orElseThrow(() -> new RuntimeException("Tenant not found: " + tenantId));

        // Parse available languages from comma-separated string
        List<String> availableLanguages = Arrays.asList(
                tenant.getAvailableLanguages() != null ?
                tenant.getAvailableLanguages().split(",") :
                new String[]{"vi", "en"}
        );

        return new TenantLanguageSettingsRes(
                tenant.getDefaultLanguage() != null ? tenant.getDefaultLanguage() : "vi",
                tenant.getId(),
                tenant.getDomain(),
                availableLanguages
        );
    }

    @Override
    @Transactional
    public TenantLanguageSettingsRes updateLanguageSettings(TenantLanguageSettingsReq request) {
        String tenantId = TenantContext.getCurrentTenant();
        log.debug("Updating language settings for tenant: {} with language: {}", tenantId, request.getDefaultLanguage());

        Tenant tenant = tenantService.findByTenantId(tenantId)
                .orElseThrow(() -> new RuntimeException("Tenant not found: " + tenantId));

        // Validate that default language is in available languages list
        if (!request.getAvailableLanguages().contains(request.getDefaultLanguage())) {
            throw new RuntimeException("Default language must be in available languages list");
        }

        tenant.setDefaultLanguage(request.getDefaultLanguage());
        tenant.setAvailableLanguages(String.join(",", request.getAvailableLanguages()));
        Tenant savedTenant = tenantService.save(tenant);

        log.info("Updated language settings for tenant: {} - default: '{}', available: '{}'",
                tenantId, request.getDefaultLanguage(), request.getAvailableLanguages());

        // Parse available languages for response
        List<String> availableLanguages = Arrays.asList(
                savedTenant.getAvailableLanguages() != null ?
                savedTenant.getAvailableLanguages().split(",") :
                new String[]{"vi", "en", "cn"}
        );

        return new TenantLanguageSettingsRes(
                savedTenant.getDefaultLanguage(),
                savedTenant.getId(),
                savedTenant.getDomain(),
                availableLanguages
        );
    }

    @Override
    public TenantDefaultLanguageRes getTenantDefaultLanguage() {
        String tenantId = TenantContext.getSiteTenant();
        log.debug("Getting default language for tenant: {}", tenantId);

        Tenant tenant = tenantService.findByTenantId(tenantId)
                .orElseThrow(() -> new RuntimeException("Tenant not found: " + tenantId));

        String defaultLanguage = tenant.getDefaultLanguage() != null ? tenant.getDefaultLanguage() : "vi";
        return new TenantDefaultLanguageRes(defaultLanguage);
    }

    @Override
    public List<String> getTenantAvailableLanguages() {
        String tenantId = TenantContext.getSiteTenant();
        log.debug("Getting available languages for tenant: {}", tenantId);

        Tenant tenant = tenantService.findByTenantId(tenantId)
                .orElseThrow(() -> new RuntimeException("Tenant not found: " + tenantId));

        // Parse available languages from comma-separated string
        return Arrays.asList(
                tenant.getAvailableLanguages() != null ?
                tenant.getAvailableLanguages().split(",") :
                new String[]{"vi", "en"}
        );
    }

    @Override
    public List<LanguageOptionRes> getPredefinedLanguages() {
        log.debug("Getting predefined languages");
        return new ArrayList<>(PREDEFINED_LANGUAGES);
    }

    @Override
    public List<CustomLanguageRes> getCustomLanguages() {
        String tenantId = TenantContext.getCurrentTenant();
        log.debug("Getting custom languages for tenant: {}", tenantId);

        List<TenantCustomLanguage> customLanguages = customLanguageRepository.findByTenantIdOrderByLanguageNameAsc(tenantId);

        return customLanguages.stream()
                .map(this::mapToCustomLanguageRes)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public CustomLanguageRes createCustomLanguage(CustomLanguageReq request) {
        String tenantId = TenantContext.getCurrentTenant();
        log.debug("Creating custom language for tenant: {} with code: {}", tenantId, request.getLanguageCode());

        // Check if language code already exists (predefined or custom)
        if (isLanguageCodeExists(request.getLanguageCode())) {
            throw new RuntimeException("Language code already exists: " + request.getLanguageCode());
        }

        TenantCustomLanguage customLanguage = new TenantCustomLanguage(
                request.getLanguageCode(),
                request.getLanguageName(),
                request.getFlagClass(),
                request.getDescription()
        );
        customLanguage.setActive(request.isActive());

        TenantCustomLanguage savedLanguage = customLanguageRepository.save(customLanguage);

        log.info("Created custom language for tenant: {} - code: '{}', name: '{}'",
                tenantId, request.getLanguageCode(), request.getLanguageName());

        return mapToCustomLanguageRes(savedLanguage);
    }

    @Override
    @Transactional
    public CustomLanguageRes updateCustomLanguage(String languageCode, CustomLanguageReq request) {
        String tenantId = TenantContext.getCurrentTenant();
        log.debug("Updating custom language for tenant: {} with code: {}", tenantId, languageCode);

        TenantCustomLanguage customLanguage = customLanguageRepository.findByTenantIdAndLanguageCode(tenantId, languageCode)
                .orElseThrow(() -> new RuntimeException("Custom language not found: " + languageCode));

        // If language code is being changed, check if new code already exists
        if (!languageCode.equals(request.getLanguageCode()) && isLanguageCodeExists(request.getLanguageCode())) {
            throw new RuntimeException("Language code already exists: " + request.getLanguageCode());
        }

        customLanguage.setLanguageCode(request.getLanguageCode());
        customLanguage.setLanguageName(request.getLanguageName());
        customLanguage.setFlagClass(request.getFlagClass());
        customLanguage.setDescription(request.getDescription());
        customLanguage.setActive(request.isActive());

        TenantCustomLanguage savedLanguage = customLanguageRepository.save(customLanguage);

        log.info("Updated custom language for tenant: {} - code: '{}', name: '{}'",
                tenantId, request.getLanguageCode(), request.getLanguageName());

        return mapToCustomLanguageRes(savedLanguage);
    }

    @Override
    @Transactional
    public void deleteCustomLanguage(String languageCode) {
        String tenantId = TenantContext.getCurrentTenant();
        log.debug("Deleting custom language for tenant: {} with code: {}", tenantId, languageCode);

        if (!customLanguageRepository.existsByTenantIdAndLanguageCode(tenantId, languageCode)) {
            throw new RuntimeException("Custom language not found: " + languageCode);
        }

        customLanguageRepository.deleteByTenantIdAndLanguageCode(tenantId, languageCode);

        log.info("Deleted custom language for tenant: {} - code: '{}'", tenantId, languageCode);
    }

    @Override
    public List<LanguageOptionRes> getAllAvailableLanguages() {
        String tenantId = TenantContext.getCurrentTenant();
        log.debug("Getting all available languages for tenant: {}", tenantId);

        List<LanguageOptionRes> allLanguages = new ArrayList<>();

        // Add predefined languages
        allLanguages.addAll(PREDEFINED_LANGUAGES);

        // Add custom languages
        List<TenantCustomLanguage> customLanguages = customLanguageRepository.findByTenantIdAndIsActiveTrueOrderByLanguageNameAsc(tenantId);
        List<LanguageOptionRes> customLanguageOptions = customLanguages.stream()
                .map(custom -> new LanguageOptionRes(
                        custom.getLanguageCode(),
                        custom.getLanguageName(),
                        custom.getFlagClass() != null ? custom.getFlagClass() : "fi fi-xx",
                        custom.isActive(),
                        custom.getDescription()
                ))
                .collect(Collectors.toList());

        allLanguages.addAll(customLanguageOptions);

        return allLanguages;
    }

    // Helper methods
    private boolean isLanguageCodeExists(String languageCode) {
        String tenantId = TenantContext.getCurrentTenant();

        // Check predefined languages
        boolean existsInPredefined = PREDEFINED_LANGUAGES.stream()
                .anyMatch(lang -> lang.getCode().equals(languageCode));

        // Check custom languages
        boolean existsInCustom = customLanguageRepository.existsByTenantIdAndLanguageCode(tenantId, languageCode);

        return existsInPredefined || existsInCustom;
    }

    private CustomLanguageRes mapToCustomLanguageRes(TenantCustomLanguage customLanguage) {
        return new CustomLanguageRes(
                customLanguage.getId(),
                customLanguage.getLanguageCode(),
                customLanguage.getLanguageName(),
                customLanguage.getFlagClass(),
                customLanguage.getDescription(),
                customLanguage.isActive(),
                customLanguage.getTenantId(),
                customLanguage.getCreatedAt(),
                customLanguage.getUpdatedAt()
        );
    }
}
