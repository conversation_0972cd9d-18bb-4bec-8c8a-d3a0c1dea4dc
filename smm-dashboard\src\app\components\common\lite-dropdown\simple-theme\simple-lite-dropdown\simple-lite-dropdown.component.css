/* Simple Lite Dropdown Component Styles */

.simple-lite-dropdown {
  position: relative;
  display: inline-block;
  width: 100%;
}

/* Dropdown Button */
.simple-dropdown-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0.75rem 1rem;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  color: #334155;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
}

.simple-dropdown-button:hover {
  border-color: #cbd5e1;
  background: #f8fafc;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.simple-dropdown-button:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.simple-dropdown-button:active {
  transform: translateY(0);
}

/* Dropdown Text */
.simple-dropdown-text {
  flex: 1;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #334155;
}

/* Dropdown Icon */
.simple-dropdown-icon {
  color: #64748b;
  transition: all 0.2s ease;
  margin-left: 0.5rem;
  flex-shrink: 0;
}

.simple-dropdown-icon-open {
  transform: rotate(180deg);
  color: #3b82f6;
}

/* Dropdown Menu */
.simple-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  margin-top: 0.25rem;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  animation: simple-dropdown-appear 0.2s ease-out;
}

@keyframes simple-dropdown-appear {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Dropdown Content */
.simple-dropdown-content {
  max-height: 300px;
  overflow-y: auto;
  padding: 0.5rem 0;
}

/* Custom Scrollbar */
.simple-dropdown-content::-webkit-scrollbar {
  width: 6px;
}

.simple-dropdown-content::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.simple-dropdown-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.simple-dropdown-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Dropdown Options */
.simple-dropdown-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0.75rem 1rem;
  background: transparent;
  border: none;
  color: #334155;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  text-align: left;
}

.simple-dropdown-option:hover {
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  color: #1e293b;
  transform: translateX(4px);
}

.simple-dropdown-option:active {
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
}

.simple-dropdown-option-selected {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  color: #1e40af;
  font-weight: 600;
}

.simple-dropdown-option-selected:hover {
  background: linear-gradient(135deg, #bfdbfe, #93c5fd);
  color: #1d4ed8;
}

/* Option Text */
.simple-option-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Option Check Icon */
.simple-option-check {
  color: #3b82f6;
  margin-left: 0.5rem;
  flex-shrink: 0;
  animation: simple-check-appear 0.2s ease-out;
}

@keyframes simple-check-appear {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .simple-dropdown-button {
    padding: 0.875rem 1rem;
    font-size: 1rem;
  }
  
  .simple-dropdown-option {
    padding: 0.875rem 1rem;
    font-size: 1rem;
  }
  
  .simple-dropdown-content {
    max-height: 250px;
  }
}

@media (max-width: 480px) {
  .simple-dropdown-button {
    padding: 1rem;
    font-size: 0.95rem;
  }
  
  .simple-dropdown-option {
    padding: 1rem;
    font-size: 0.95rem;
  }
  
  .simple-dropdown-content {
    max-height: 200px;
  }
}

/* Focus States for Accessibility */
.simple-dropdown-option:focus {
  outline: 2px solid #3b82f6;
  outline-offset: -2px;
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .simple-dropdown-button {
    border-width: 3px;
  }
  
  .simple-dropdown-menu {
    border-width: 3px;
  }
  
  .simple-dropdown-option-selected {
    background: #000;
    color: #fff;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .simple-dropdown-button,
  .simple-dropdown-icon,
  .simple-dropdown-option {
    transition: none;
  }
  
  .simple-dropdown-menu {
    animation: none;
  }
  
  .simple-option-check {
    animation: none;
  }
}
