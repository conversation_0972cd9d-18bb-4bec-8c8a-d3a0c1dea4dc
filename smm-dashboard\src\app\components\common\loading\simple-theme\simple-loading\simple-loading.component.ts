import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LoadingState } from '../../services/loading-logic.service';

@Component({
  selector: 'app-simple-loading',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './simple-loading.component.html',
  styleUrl: './simple-loading.component.css'
})
export class SimpleLoadingComponent {
  @Input() loadingState: LoadingState | null = null;
  @Input() size: 'sm' | 'md' | 'lg' = 'md';
  @Input() overlay: boolean = false;
  @Input() fullScreen: boolean = false;
  @Input() message: string = '';
  @Input() transparent: boolean = false;
}
