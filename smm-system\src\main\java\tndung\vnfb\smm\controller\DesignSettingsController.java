package tndung.vnfb.smm.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import tndung.vnfb.smm.anotation.TenantCheck;
import tndung.vnfb.smm.config.TenantContext;
import tndung.vnfb.smm.dto.ApiResponseEntity;
import tndung.vnfb.smm.dto.design.*;
import tndung.vnfb.smm.service.DesignSettingsService;

import javax.validation.Valid;
import java.util.List;


@RestController
@RequestMapping("/v1/design-settings")
@RequiredArgsConstructor
public class DesignSettingsController {
    private final DesignSettingsService designSettingsService;

    @PostMapping
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<DesignSettingsDto> createSettings(@Valid @RequestBody DesignSettingsDto settingsDto) {
        // Set the tenant ID from the current context
        String tenantId = TenantContext.getWildcardTenant();
        settingsDto.setId(tenantId);
        return ApiResponseEntity.success(designSettingsService.saveSettings(settingsDto));
    }

    @GetMapping("/current")
    public ApiResponseEntity<DesignSettingsDto> getCurrentSettings() {
        String tenantId = TenantContext.getWildcardTenant();
        return ApiResponseEntity.success(designSettingsService.getSettingsById(tenantId));
    }

//    @GetMapping("/{id}")
//    public ApiResponseEntity<DesignSettingsDto> getSettingsById(@PathVariable String id) throws InterruptedException {
//        return ApiResponseEntity.success(designSettingsService.getSettingsById(id));
//    }

    @GetMapping
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<List<DesignSettingsDto>> getAllSettings() {
        return ApiResponseEntity.success(designSettingsService.getAllSettings());
    }

//    @DeleteMapping("/{id}")
//    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL', 'ROLE_ADMIN_PANEL')")
//    public ApiResponseEntity<String> deleteSettings(@PathVariable String id) {
//        designSettingsService.deleteSettings(id);
//        return ApiResponseEntity.success();
//    }

    // Update specific components
    @PutMapping("/{id}/logo")
    @PreAuthorize("hasAnyRole('ROLE_PANEL',  'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<DesignSettingsDto> updateLogo(@PathVariable String id, @Valid @RequestBody LogoDto logoDto) {
        return ApiResponseEntity.success(designSettingsService.updateLogo(id, logoDto));
    }

    @PutMapping("/current/logo")
    @PreAuthorize("hasAnyRole('ROLE_PANEL',  'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<DesignSettingsDto> updateCurrentLogo(@Valid @RequestBody LogoDto logoDto) {
        String tenantId = TenantContext.getWildcardTenant();
        return ApiResponseEntity.success(designSettingsService.updateLogo(tenantId, logoDto));
    }

    @PutMapping("/{id}/favicon")
    @PreAuthorize("hasAnyRole('ROLE_PANEL',  'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<DesignSettingsDto> updateFavicon(@PathVariable String id, @Valid @RequestBody FaviconDto faviconDto) {
        return ApiResponseEntity.success(designSettingsService.updateFavicon(id, faviconDto));
    }

    @PutMapping("/current/favicon")
    @PreAuthorize("hasAnyRole('ROLE_PANEL',  'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<DesignSettingsDto> updateCurrentFavicon(@Valid @RequestBody FaviconDto faviconDto) {
        String tenantId = TenantContext.getWildcardTenant();
        return ApiResponseEntity.success(designSettingsService.updateFavicon(tenantId, faviconDto));
    }

    @PutMapping("/{id}/color-scheme")
    @PreAuthorize("hasAnyRole('ROLE_PANEL',  'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<DesignSettingsDto> updateColorScheme(@PathVariable String id, @Valid @RequestBody ColorSchemeDto colorSchemeDto) {
        return ApiResponseEntity.success(designSettingsService.updateColorScheme(id, colorSchemeDto));
    }

    @PutMapping("/current/color-scheme")
    @PreAuthorize("hasAnyRole('ROLE_PANEL',  'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<DesignSettingsDto> updateCurrentColorScheme(@Valid @RequestBody ColorSchemeDto colorSchemeDto) {
        String tenantId = TenantContext.getWildcardTenant();
        return ApiResponseEntity.success(designSettingsService.updateColorScheme(tenantId, colorSchemeDto));
    }



    @PutMapping("/{id}/landing-settings")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<DesignSettingsDto> updateLandingSettings(@PathVariable String id,
                                                                      @Valid @RequestBody LandingSettingsDto landingSettingsDto) {
        return ApiResponseEntity.success(designSettingsService.updateLandingSettings(id, landingSettingsDto));
    }

    @PutMapping("/current/landing-settings")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<DesignSettingsDto> updateCurrentLandingSettings(@Valid @RequestBody LandingSettingsDto landingSettingsDto) {
        String tenantId = TenantContext.getWildcardTenant();
        return ApiResponseEntity.success(designSettingsService.updateLandingSettings(tenantId, landingSettingsDto));
    }

    @PutMapping("/{id}/header-settings")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<DesignSettingsDto> updateHeaderSettings(@PathVariable String id,
                                                                      @Valid @RequestBody HeaderDto headerDto) {
        return ApiResponseEntity.success(designSettingsService.updateHeaderSettings(id, headerDto));
    }

    @PutMapping("/current/header-settings")
    @PreAuthorize("hasAnyRole('ROLE_PANEL',  'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<DesignSettingsDto> updateCurrentHeaderSettings(@Valid @RequestBody HeaderDto headerDto) {
        String tenantId = TenantContext.getWildcardTenant();
        return ApiResponseEntity.success(designSettingsService.updateHeaderSettings(tenantId, headerDto));
    }

    @PutMapping("/{id}/sidebar-settings")
    @PreAuthorize("hasAnyRole('ROLE_PANEL',  'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<DesignSettingsDto> updateSidebarSettings(@PathVariable String id,
                                                                     @Valid @RequestBody SidebarDto sidebarDto) {
        return ApiResponseEntity.success(designSettingsService.updateSidebarSettings(id, sidebarDto));
    }

    @PutMapping("/current/sidebar-settings")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<DesignSettingsDto> updateCurrentSidebarSettings(@Valid @RequestBody SidebarDto sidebarDto) {
        String tenantId = TenantContext.getWildcardTenant();
        return ApiResponseEntity.success(designSettingsService.updateSidebarSettings(tenantId, sidebarDto));
    }
}
