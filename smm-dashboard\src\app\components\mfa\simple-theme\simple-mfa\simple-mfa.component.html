<!-- Simple Theme MFA Component -->
<div class="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8" *ngIf="mfaState$ | async as mfaState">
  <div class="max-w-md w-full space-y-8">
    <!-- Header -->
    <div class="text-center">
      <h2 class="text-3xl font-bold text-gray-900">{{ 'auth.mfa_verification' | translate }}</h2>
      <p class="mt-2 text-sm text-gray-600">{{ 'auth.mfa_instruction' | translate }}</p>
    </div>

    <!-- MFA Form -->
    <form [formGroup]="mfaState.mfaForm" (ngSubmit)="onSubmit()" class="mt-8 space-y-6">
      <!-- MFA Code Input -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-4 text-center">
          {{ 'auth.mfa_code' | translate }}
        </label>
        
        <div class="flex justify-center space-x-3">
          <input
            type="text"
            maxlength="1"
            (input)="onDigitInput($event, 0)"
            (keydown)="onDigitKeyDown($event, 0)"
            (focus)="onFocus($event)"
            (paste)="onPaste($event)"
            class="w-12 h-12 text-center text-lg font-semibold border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 mfa-digit-input"
            [ngClass]="{'border-red-500': code.invalid && code.touched}"
          >
          <input
            type="text"
            maxlength="1"
            (input)="onDigitInput($event, 1)"
            (keydown)="onDigitKeyDown($event, 1)"
            (focus)="onFocus($event)"
            class="w-12 h-12 text-center text-lg font-semibold border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 mfa-digit-input"
            [ngClass]="{'border-red-500': code.invalid && code.touched}"
          >
          <input
            type="text"
            maxlength="1"
            (input)="onDigitInput($event, 2)"
            (keydown)="onDigitKeyDown($event, 2)"
            (focus)="onFocus($event)"
            class="w-12 h-12 text-center text-lg font-semibold border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 mfa-digit-input"
            [ngClass]="{'border-red-500': code.invalid && code.touched}"
          >
          <input
            type="text"
            maxlength="1"
            (input)="onDigitInput($event, 3)"
            (keydown)="onDigitKeyDown($event, 3)"
            (focus)="onFocus($event)"
            class="w-12 h-12 text-center text-lg font-semibold border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 mfa-digit-input"
            [ngClass]="{'border-red-500': code.invalid && code.touched}"
          >
          <input
            type="text"
            maxlength="1"
            (input)="onDigitInput($event, 4)"
            (keydown)="onDigitKeyDown($event, 4)"
            (focus)="onFocus($event)"
            class="w-12 h-12 text-center text-lg font-semibold border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 mfa-digit-input"
            [ngClass]="{'border-red-500': code.invalid && code.touched}"
          >
          <input
            type="text"
            maxlength="1"
            (input)="onDigitInput($event, 5)"
            (keydown)="onDigitKeyDown($event, 5)"
            (focus)="onFocus($event)"
            class="w-12 h-12 text-center text-lg font-semibold border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 mfa-digit-input"
            [ngClass]="{'border-red-500': code.invalid && code.touched}"
          >
        </div>

        <!-- Hidden input for form control -->
        <input type="hidden" formControlName="code">

        <!-- Validation errors -->
        <div *ngIf="code.invalid && (code.dirty || code.touched)" class="mt-2 text-sm text-red-600 text-center">
          <div *ngIf="code.errors?.['required']">{{ 'auth.mfa_code_required' | translate }}</div>
          <div *ngIf="code.errors?.['minlength'] || code.errors?.['maxlength']">
            {{ 'auth.mfa_code_length' | translate }}
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div *ngIf="mfaState.mfaError" class="text-red-600 text-sm text-center bg-red-50 p-3 rounded-md">
        {{ mfaState.mfaError }}
      </div>

      <!-- Submit Button -->
      <div>
        <button
          type="submit"
          [disabled]="mfaState.mfaForm.invalid || mfaState.isLoading"
          class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span *ngIf="!mfaState.isLoading">{{ 'auth.verify' | translate }}</span>
          <span *ngIf="mfaState.isLoading" class="flex items-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ 'auth.verifying' | translate }}
          </span>
        </button>
      </div>

      <!-- Resend Code -->
      <div class="text-center">
        <button
          type="button"
          (click)="resendCode()"
          [disabled]="!mfaState.canResend || mfaState.isResending"
          class="text-blue-600 hover:text-blue-500 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span *ngIf="!mfaState.isResending && mfaState.canResend">{{ 'auth.resend_code' | translate }}</span>
          <span *ngIf="mfaState.isResending">{{ 'auth.resending' | translate }}...</span>
          <span *ngIf="!mfaState.canResend && mfaState.resendCooldown > 0">
            {{ 'auth.resend_in' | translate }} {{ mfaState.resendCooldown }}s
          </span>
        </button>
      </div>

      <!-- Back to Login -->
      <div class="text-center">
        <a (click)="goBack()" class="text-sm text-gray-600 hover:text-gray-500 cursor-pointer">
          ← {{ 'auth.back_to_login' | translate }}
        </a>
      </div>
    </form>
  </div>
</div>
