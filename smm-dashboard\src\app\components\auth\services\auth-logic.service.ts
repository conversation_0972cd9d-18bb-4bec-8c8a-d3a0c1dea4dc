import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { BehaviorSubject, Observable, EMPTY, throwError } from 'rxjs';
import { catchError, finalize } from 'rxjs/operators';

// Services
import { AuthService } from '../../../core/services/auth.service';
import { ToastService } from '../../../core/services/toast.service';
import { MfaStateService } from '../../../core/services/mfa-state.service';
import { NotifyType } from '../../../constant/notify-type';

export interface AuthState {
  loginForm: FormGroup;
  returnUrl: string;
  isLoading: boolean;
  loginError: string;
  activeTab: number;
}

@Injectable({
  providedIn: 'root'
})
export class AuthLogicService {
  private stateSubject = new BehaviorSubject<AuthState>({
    loginForm: new FormGroup({
      email: new FormControl('', [Validators.required]),
      password: new FormControl('', [Validators.required]),
      rememberMe: new FormControl(false)
    }),
    returnUrl: '/',
    isLoading: false,
    loginError: '',
    activeTab: 1
  });

  public state$ = this.stateSubject.asObservable();

  constructor(
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private toastService: ToastService,
    private mfaStateService: MfaStateService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.initializeAuth();
  }

  private initializeAuth(): void {
    this.initFormWithSavedData();
    this.setupRouteParams();
    this.checkExistingAuth();
  }

  private setupRouteParams(): void {
    this.route.queryParams.subscribe(params => {
      const currentState = this.stateSubject.value;
      const returnUrl = params['returnUrl'] || '/dashboard/new';
      console.log('Return URL from query params:', returnUrl);
      
      this.stateSubject.next({
        ...currentState,
        returnUrl
      });
    });
  }

  private checkExistingAuth(): void {
    if (this.authService.isAuthenticated() && !this.authService.isTokenExpired()) {
      console.log('Auth Component - User is already authenticated, redirecting');
      const redirectUrl = localStorage.getItem('redirectAfterLogin') || '/dashboard/new';
      this.router.navigate([redirectUrl]);
    }
  }

  private initFormWithSavedData(): void {
    if (isPlatformBrowser(this.platformId)) {
      const savedEmail = localStorage.getItem('userEmail');
      if (savedEmail) {
        const currentState = this.stateSubject.value;
        currentState.loginForm.patchValue({
          email: savedEmail,
          rememberMe: true
        });
        this.stateSubject.next(currentState);
      }
    }
  }

  public onSubmit(): void {
    const currentState = this.stateSubject.value;
    
    if (currentState.loginForm.status === 'VALID') {
      console.log('Login form submitted');
      this.updateState({ isLoading: true, loginError: '' });

      const email = currentState.loginForm.controls['email'].value;
      const password = currentState.loginForm.controls['password'].value;

      this.authService
        .login(email, password)
        .pipe(
          catchError(err => {
            console.error('Login error:', err);

            // Check if this is an MFA required error
            if (err && err.code === 400 &&
                err.message === 'Please enter the TOTP code to continue logging in' &&
                err.data && err.data.login_first_factor) {

              // Store username and password for MFA verification securely in memory
              this.mfaStateService.storeMfaInfo(
                email || '',
                password || '',
                err.data.login_first_factor,
                currentState.returnUrl || '/dashboard/new'
              );

              // Redirect to MFA page
              this.router.navigate(['/auth/mfa']);
              return EMPTY; // Don't propagate the error
            }

            // Handle other errors
            const loginError = err.message || 'Login failed. Please check your credentials and try again.';
            this.updateState({ loginError });
            // Show toast notification
            this.toastService.showToast(loginError, NotifyType.ERROR);
            return throwError(() => err);
          }),
          finalize(() => {
            this.updateState({ isLoading: false });
          })
        )
        .subscribe({
          next: () => {
            console.log('Login successful');
            this.toastService.showToast('Login successful!', NotifyType.SUCCESS);

            // Only access localStorage in browser environment
            if (isPlatformBrowser(this.platformId)) {
              // Check if there's a stored redirect URL
              const redirectUrl = localStorage.getItem('redirectAfterLogin');
              console.log('Redirect URL from localStorage:', redirectUrl);

              if (redirectUrl) {
                console.log('Navigating to stored URL:', redirectUrl);
                localStorage.removeItem('redirectAfterLogin');
                this.router.navigate([redirectUrl]).then(() => {
                  window.location.reload();
                });
                return;
              }
            }

            // Default navigation if no redirect URL or not in browser
            const state = this.stateSubject.value;
            console.log('Navigating to default return URL:', state.returnUrl);
            this.router.navigate([state.returnUrl || '/dashboard/new']).then(() => {
              window.location.reload();
            });
          },
          error: (error) => {
            console.error('Login subscription error:', error);
            // Error is already handled in the catchError operator
          }
        });
    } else {
      console.log('Login form is invalid');
      // Mark all form controls as touched to show validation errors
      for (const key in currentState.loginForm.controls) {
        const control = currentState.loginForm.controls[key];
        control.markAllAsTouched();
      }
    }
  }

  public loginWithGoogle(): void {
    console.log('Login with Google clicked');
    // TODO: Implement Google authentication
  }

  public setActiveTab(tab: number): void {
    this.updateState({ activeTab: tab });
  }

  private updateState(partialState: Partial<AuthState>): void {
    const currentState = this.stateSubject.value;
    this.stateSubject.next({
      ...currentState,
      ...partialState
    });
  }

  // Getters for form controls
  public get email(): FormControl {
    return this.stateSubject.value.loginForm.get('email') as FormControl;
  }

  public get password(): FormControl {
    return this.stateSubject.value.loginForm.get('password') as FormControl;
  }

  public get rememberMe(): FormControl {
    return this.stateSubject.value.loginForm.get('rememberMe') as FormControl;
  }
}
