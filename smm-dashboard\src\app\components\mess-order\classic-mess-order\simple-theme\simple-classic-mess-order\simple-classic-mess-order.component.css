/* Simple Theme for Classic Mess Order - Clean & Minimal Design */
.simple-classic-mess-order-container {
  padding: 1.5rem;
  background-color: #f8fafc;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Header Section */
.order-header {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-icon {
  width: 48px;
  height: 48px;
  background: #4299e1;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.header-icon svg {
  width: 24px;
  height: 24px;
}

.header-text {
  flex: 1;
}

.header-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.25rem 0;
}

.header-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

/* Order Form Container */
.order-form-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  overflow: hidden;
  margin-bottom: 1.5rem;
}

/* Instructions Section */
.instructions-section {
  padding: 1.5rem;
  background: #f7fafc;
  border-bottom: 1px solid #e2e8f0;
}

.instructions-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.instructions-icon {
  width: 20px;
  height: 20px;
  color: #4299e1;
  flex-shrink: 0;
}

.instructions-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.instructions-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.instruction-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.instruction-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4299e1;
  min-width: 60px;
}

.instruction-code {
  background: #dbeafe;
  border: 1px solid #93c5fd;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
  color: #1e40af;
}

.instruction-text {
  font-size: 0.875rem;
  color: #6b7280;
}

/* Content Section */
.content-section {
  padding: 1.5rem;
}

.content-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.content-icon {
  width: 20px;
  height: 20px;
  color: #4299e1;
  flex-shrink: 0;
}

.content-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.content-subtitle {
  font-size: 0.75rem;
  color: #6b7280;
  margin-left: auto;
}

.content-input {
  position: relative;
}

.content-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.2s ease;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: white;
}

.content-textarea:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.content-textarea::placeholder {
  color: #9ca3af;
  font-family: inherit;
}

.content-stats {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 0.75rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.stat-value {
  font-size: 1rem;
  font-weight: 600;
  color: #4299e1;
}

.stat-label {
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

/* Action Section */
.action-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  padding: 1.5rem;
  background: #f7fafc;
  border-top: 1px solid #e2e8f0;
}

.action-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.info-icon {
  width: 32px;
  height: 32px;
  background: rgba(66, 153, 225, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4299e1;
  flex-shrink: 0;
}

.info-text {
  flex: 1;
}

.info-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 0.25rem;
}

.info-description {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.4;
}

.process-btn {
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  min-width: 120px;
}

.process-btn:hover:not(:disabled) {
  background: #3182ce;
}

.process-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-icon,
.btn-spinner {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.btn-text {
  white-space: nowrap;
}

/* Results Section */
.results-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  overflow: hidden;
  margin-top: 1.5rem;
}

.results-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.results-title-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.results-icon {
  width: 20px;
  height: 20px;
  color: #4299e1;
  flex-shrink: 0;
}

.results-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.results-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.summary-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid transparent;
}

.summary-card.success {
  background: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.2);
}

.summary-card.failed {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
}

.summary-card.total {
  background: rgba(66, 153, 225, 0.1);
  border-color: rgba(66, 153, 225, 0.2);
}

.summary-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.summary-card.success .summary-icon {
  color: #10b981;
}

.summary-card.failed .summary-icon {
  color: #ef4444;
}

.summary-card.total .summary-icon {
  color: #4299e1;
}

.summary-content {
  flex: 1;
}

.summary-count {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.125rem;
}

.summary-card.success .summary-count {
  color: #10b981;
}

.summary-card.failed .summary-count {
  color: #ef4444;
}

.summary-card.total .summary-count {
  color: #4299e1;
}

.summary-label {
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

/* Results List */
.results-list {
  padding: 1.5rem;
}

.results-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.list-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.results-items {
  max-height: 400px;
  overflow-y: auto;
}

.result-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 0.5rem;
  border: 1px solid transparent;
  transition: all 0.2s ease;
}

.result-item:hover {
  background: #f7fafc;
}

.result-item.success {
  background: rgba(16, 185, 129, 0.05);
  border-color: rgba(16, 185, 129, 0.2);
}

.result-item.failed {
  background: rgba(239, 68, 68, 0.05);
  border-color: rgba(239, 68, 68, 0.2);
}

.result-index {
  width: 20px;
  height: 20px;
  background: #e2e8f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  flex-shrink: 0;
}

.result-status {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.result-item.success .result-status {
  color: #10b981;
}

.result-item.failed .result-status {
  color: #ef4444;
}

.result-content {
  flex: 1;
  min-width: 0;
}

.result-link {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1a202c;
  word-break: break-all;
  margin-bottom: 0.25rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.result-message {
  font-size: 0.75rem;
  color: #ef4444;
  line-height: 1.4;
}

.result-badge {
  flex-shrink: 0;
}

.badge {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.badge.success {
  background: #d1fae5;
  color: #065f46;
}

.badge.failed {
  background: #fee2e2;
  color: #991b1b;
}

/* Responsive Design */
@media (max-width: 768px) {
  .simple-classic-mess-order-container {
    padding: 1rem;
  }

  .order-header,
  .order-form-container,
  .results-section {
    margin-bottom: 1rem;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .header-title {
    font-size: 1.5rem;
  }

  .instructions-section,
  .content-section,
  .action-section,
  .results-header,
  .results-list {
    padding: 1rem;
  }

  .instruction-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .content-stats {
    justify-content: center;
  }

  .action-section {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
    gap: 1rem;
  }

  .results-summary {
    grid-template-columns: 1fr;
  }

  .summary-card {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
}
