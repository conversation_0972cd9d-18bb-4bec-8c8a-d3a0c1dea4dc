package tndung.vnfb.smm.service;

import tndung.vnfb.smm.dto.request.TenantLanguageSettingsReq;
import tndung.vnfb.smm.dto.request.CustomLanguageReq;
import tndung.vnfb.smm.dto.response.TenantLanguageSettingsRes;
import tndung.vnfb.smm.dto.response.TenantDefaultLanguageRes;
import tndung.vnfb.smm.dto.response.CustomLanguageRes;
import tndung.vnfb.smm.dto.response.LanguageOptionRes;

import java.util.List;

public interface TenantSettingsService {

    /**
     * Get language settings for current tenant
     * @return TenantLanguageSettingsRes
     */
    TenantLanguageSettingsRes getLanguageSettings();

    /**
     * Update language settings for current tenant
     * @param request TenantLanguageSettingsReq
     * @return TenantLanguageSettingsRes
     */
    TenantLanguageSettingsRes updateLanguageSettings(TenantLanguageSettingsReq request);

    /**
     * Get default language for current tenant (public endpoint)
     * @return TenantDefaultLanguageRes
     */
    TenantDefaultLanguageRes getTenantDefaultLanguage();

    /**
     * Get available languages for current tenant (public endpoint for dashboard)
     * @return List of available language codes
     */
    List<String> getTenantAvailableLanguages();

    /**
     * Get all predefined language options
     * @return List of predefined language options
     */
    List<LanguageOptionRes> getPredefinedLanguages();

    /**
     * Get custom languages for current tenant
     * @return List of custom languages
     */
    List<CustomLanguageRes> getCustomLanguages();

    /**
     * Create custom language for current tenant
     * @param request CustomLanguageReq
     * @return CustomLanguageRes
     */
    CustomLanguageRes createCustomLanguage(CustomLanguageReq request);

    /**
     * Update custom language for current tenant
     * @param languageCode Language code to update
     * @param request CustomLanguageReq
     * @return CustomLanguageRes
     */
    CustomLanguageRes updateCustomLanguage(String languageCode, CustomLanguageReq request);

    /**
     * Delete custom language for current tenant
     * @param languageCode Language code to delete
     */
    void deleteCustomLanguage(String languageCode);

    /**
     * Get all available languages (predefined + custom) for current tenant
     * @return List of all language options
     */
    List<LanguageOptionRes> getAllAvailableLanguages();
}
