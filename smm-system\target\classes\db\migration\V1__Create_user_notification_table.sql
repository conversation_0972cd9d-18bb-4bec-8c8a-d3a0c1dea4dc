-- Create user_notification table
CREATE TABLE user_notification (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(36) NOT NULL,
    user_id BIGINT,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    type <PERSON><PERSON><PERSON><PERSON>(20) NOT NULL DEFAULT 'INFO',
    category VARCHAR(20) NOT NULL DEFAULT 'SYSTEM',
    is_read BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_user_notification_tenant_id ON user_notification(tenant_id);
CREATE INDEX idx_user_notification_user_id ON user_notification(user_id);
CREATE INDEX idx_user_notification_is_read ON user_notification(is_read);
CREATE INDEX idx_user_notification_created_at ON user_notification(created_at);
CREATE INDEX idx_user_notification_tenant_user ON user_notification(tenant_id, user_id);
CREATE INDEX idx_user_notification_tenant_unread ON user_notification(tenant_id, is_read) WHERE is_read = FALSE;

-- Add comments
COMMENT ON TABLE user_notification IS 'User notifications for dashboard users';
COMMENT ON COLUMN user_notification.tenant_id IS 'Tenant identifier for multi-tenancy';
COMMENT ON COLUMN user_notification.user_id IS 'User ID - null means notification for all users in tenant';
COMMENT ON COLUMN user_notification.title IS 'Notification title';
COMMENT ON COLUMN user_notification.content IS 'Notification content/message';
COMMENT ON COLUMN user_notification.type IS 'Notification type: INFO, SUCCESS, WARNING, ERROR';
COMMENT ON COLUMN user_notification.category IS 'Notification category: SYSTEM, RENEWAL, SETUP, ORDER, BALANCE';
COMMENT ON COLUMN user_notification.is_read IS 'Whether the notification has been read';
