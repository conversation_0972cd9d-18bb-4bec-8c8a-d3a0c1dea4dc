import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';
import { SocialIconComponent } from '../../common/social-icon/social-icon.component';
import { AdminServiceService } from '../../../core/services/admin-service.service';
import { ToastService } from '../../../core/services/toast.service';
import { IntegrationPosition } from '../../../model/response/integration-res.model';
import { IntegrationReq } from '../../../model/request/integration-req.model';
import { IntegrationsService } from '../../../core/services/integrations.service';
import { fontAwesomeBrandsIcons } from '../../../icons/icons.font-awesome-brands';
import { IconName } from '@fortawesome/fontawesome-svg-core';
import { brandColors } from '../../../icons/icons.font-awesome-brands';

@Component({
  selector: 'app-custom-integration',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    IconsModule,
    SocialIconComponent
  ],
  templateUrl: './custom-integration.component.html',
  styleUrl: './custom-integration.component.css'
})
export class CustomIntegrationComponent implements OnInit {
  @Output() close = new EventEmitter<void>();
  @Output() integrationAdded = new EventEmitter<any>();

  customIntegrationForm: FormGroup;
  isSubmitting = false;
  errorMessage = '';
  showIconGrid = false;
  isUploading = false;

  // All brand icons from Font Awesome and uploaded icons
  brandIcons: { id: number, name: string, value: IconName | string }[] = [];

  // Default selected icon (no icon)
  selectedIcon: { id: number, name: string, value: IconName | string } = { 
    id: 0, 
    name: 'No icon', 
    value: 'no-icon' as IconName 
  };

  // Position options
  positionOptions = [
    { value: IntegrationPosition.LEFT, label: 'Left' },
    { value: IntegrationPosition.RIGHT, label: 'Right' }
  ];

  constructor(
    private fb: FormBuilder,
    private adminService: AdminServiceService,
    private integrationsService: IntegrationsService,
    private toastService: ToastService
  ) {
    this.customIntegrationForm = this.fb.group({
      key: ['', [Validators.required, Validators.pattern(/^[a-zA-Z0-9_-]+$/)]],
      value: ['', [Validators.required, Validators.pattern(/^https?:\/\/.+/)]],
      position: [IntegrationPosition.LEFT, Validators.required],
      icon: [0]
    });
  }

  ngOnInit(): void {
    this.initializeBrandIcons();
  }

  // Initialize all brand icons from Font Awesome
  private initializeBrandIcons() {
    // Add the "No icon" option as the first item
    this.brandIcons.push({
      id: 0,
      name: 'No icon',
      value: 'no-icon' as IconName
    });

    // Extract icon names from fontAwesomeBrandsIcons object
    let idCounter = 1;
    Object.keys(fontAwesomeBrandsIcons).forEach((key) => {
      // Convert from faIconName to iconName (e.g., faFacebook -> facebook)
      const iconValue = this.toKebabCase(key) as IconName;

      // Convert camelCase to normal text with spaces (e.g., facebookF -> Facebook F)
      const displayName = key.replace(/^fa/, '').charAt(0).toLowerCase() + key.replace(/^fa/, '').slice(1);

      this.brandIcons.push({
        id: idCounter++,
        name: displayName,
        value: iconValue
      });
    });

    // Sort icons alphabetically (except the first "No icon" option)
    const noIcon = this.brandIcons.shift();
    this.brandIcons.sort((a, b) => a.name.localeCompare(b.name));
    this.brandIcons.unshift(noIcon!);
  }

  onClose() {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent) {
    // Close if clicking on the overlay background, not the modal content
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }

  selectIcon(icon: any) {
    // Simply select the icon that was clicked
    this.selectedIcon = icon;
    this.customIntegrationForm.patchValue({ icon: icon.id });
    this.closeIconGrid();
  }

  toggleIconGrid(event?: MouseEvent) {
    this.showIconGrid = !this.showIconGrid;
    
    if (this.showIconGrid && event) {
      // Position the grid relative to the clicked element
      setTimeout(() => {
        const iconGridContainer = document.querySelector('.icon-grid-container') as HTMLElement;
        if (iconGridContainer) {
          const rect = (event.target as HTMLElement).getBoundingClientRect();
          const gridWidth = 350;
          const gridHeight = 280;
          
          // Calculate position
          let top = rect.bottom + window.scrollY + 10;
          let left = rect.left + window.scrollX;
          
          // Ensure grid doesn't go outside viewport
          if (left + gridWidth > window.innerWidth) {
            left = window.innerWidth - gridWidth - 20;
          }
          
          if (top + gridHeight > window.innerHeight + window.scrollY) {
            top = rect.top + window.scrollY - gridHeight - 10;
          }

          // Ensure grid doesn't go outside left edge
          if (left < 20) {
            left = 20;
          }

          iconGridContainer.style.top = `${top}px`;
          iconGridContainer.style.left = `${left}px`;
          iconGridContainer.style.width = `${gridWidth}px`;

          // Ensure the grid is above all other elements
          iconGridContainer.style.zIndex = '10000';

          // Append to body to ensure it's not constrained by parent containers
          document.body.appendChild(iconGridContainer);
        }
      }, 0);
    }
  }

  closeIconGrid() {
    this.showIconGrid = false;
    // Remove the grid from body if it was appended there
    const iconGridContainer = document.querySelector('.icon-grid-container') as HTMLElement;
    if (iconGridContainer && iconGridContainer.parentNode === document.body) {
      document.body.removeChild(iconGridContainer);
    }
  }

  // Check if an icon is valid in Font Awesome
  isValidIcon(iconName: string): boolean {
    return Object.keys(fontAwesomeBrandsIcons)
      .map(key => key.replace(/^fa/, '').toLowerCase())
      .includes(iconName.toLowerCase());
  }

  // Get color for an icon
  getIconColor(iconName: string): string {
    return brandColors[iconName] || '#000000';
  }

  // Convert camelCase to kebab-case for FontAwesome icons
  toKebabCase(str: string): IconName {
    str = str.replace(/^fa/, '');
    return str
      .replace(/([a-z0-9])([A-Z])/g, '$1-$2')
      .replace(/([A-Z])([A-Z])(?=[a-z])/g, '$1-$2')
      .toLowerCase() as IconName;
  }

  // Get FontAwesome icon array format
  getFontAwesomeIcon(iconValue: string): ['fab', IconName] {
    return ['fab', iconValue as IconName];
  }

  // Check if a value is an image URL
  isImageUrl(value: string): boolean {
    if (!value) return false;

    const valueStr = value.toString();

    // Check if it's a URL (contains http/https or starts with /)
    const isUrl = valueStr.startsWith('http') || valueStr.startsWith('https') || valueStr.startsWith('/');

    // Check if it has image extension
    const hasImageExtension = /\.(png|jpg|jpeg|gif|svg|webp)(\?.*)?$/i.test(valueStr);

    // Check if it's a CDN URL (more comprehensive patterns)
    const isCdnUrl = valueStr.includes('/cdn/') ||
                     valueStr.includes('/api/image/') ||
                     valueStr.includes('/uploads/') ||
                     valueStr.includes('/images/') ||
                     valueStr.includes('/assets/') ||
                     !!valueStr.match(/\/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}\.(png|jpg|jpeg|gif|svg|webp)/i);

    return isUrl && (hasImageExtension || isCdnUrl);
  }

  // Handle file selection for icon upload
  onFileSelected(event: any) {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      if (file.type !== 'image/png') {
        this.errorMessage = 'Only PNG files are allowed for icon upload.';
        return;
      }

      // Validate file size (max 2MB)
      if (file.size > 2 * 1024 * 1024) {
        this.errorMessage = 'File size must be less than 2MB.';
        return;
      }

      this.uploadIcon(file);
    }
  }

  // Upload icon file
  uploadIcon(file: File) {
    this.isUploading = true;
    this.errorMessage = '';

    this.adminService.uploadIcon(file).subscribe({
      next: (response) => {
        this.isUploading = false;

        // Create a new icon entry for the uploaded image
        const uploadedIcon = {
          id: this.brandIcons.length,
          name: file.name.replace('.png', ''),
          value: response.url
        };

        // Add to brandIcons array
        this.brandIcons.push(uploadedIcon);

        // Select the uploaded icon
        this.selectedIcon = uploadedIcon;
        this.customIntegrationForm.patchValue({ icon: uploadedIcon.id });

        console.log('Icon uploaded successfully:', response);
      },
      error: (error) => {
        this.isUploading = false;
        this.errorMessage = error.error?.message || 'Failed to upload icon. Please try again.';
        console.error('Error uploading icon:', error);
      }
    });
  }

  onSubmit() {
    if (this.customIntegrationForm.valid) {
      this.isSubmitting = true;
      this.errorMessage = '';

      const formValue = this.customIntegrationForm.value;
      
      // Create the integration request
      const req: IntegrationReq = {
        key: formValue.key,
        value: formValue.value,
        position: formValue.position,
        icon: this.selectedIcon.value !== 'no-icon' ? this.selectedIcon.value : undefined
      };

      // Check if key already exists
      this.integrationsService.getIntegrations().subscribe({
        next: (existingIntegrations) => {
          const keyExists = existingIntegrations.some(integration => 
            integration.type.toLowerCase() === req.key?.toLowerCase()
          );

          if (keyExists) {
            this.errorMessage = 'Integration key already exists. Please choose a different key.';
            this.isSubmitting = false;
            return;
          }

          // Create the integration
          this.integrationsService.createIntegration(req).subscribe({
            next: (result) => {
              this.isSubmitting = false;
              // Emit the created integration with proper data
              const newIntegration = {
                ...result,
                type: req.key || '',
                username: req.value,
                enabled: true,
                token: req.icon || ''
              };
              this.integrationAdded.emit(newIntegration);
              this.onClose();
            },
            error: (error) => {
              this.isSubmitting = false;
              this.errorMessage = error.error?.message || 'Failed to create custom integration. Please try again.';
              console.error('Error creating custom integration:', error);
            }
          });
        },
        error: (error) => {
          this.isSubmitting = false;
          this.errorMessage = 'Failed to validate integration key. Please try again.';
          console.error('Error checking existing integrations:', error);
        }
      });
    }
  }
}
