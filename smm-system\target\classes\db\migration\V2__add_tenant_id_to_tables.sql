-- Add tenant_id column to all relevant tables

-- Add tenant_id to g_user
ALTER TABLE g_user ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(36);

-- Add tenant_id to g_order
ALTER TABLE g_order ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(36);

-- Add tenant_id to category
ALTER TABLE category ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(36);

-- Add tenant_id to g_service
ALTER TABLE g_service ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(36);

-- Add tenant_id to platform
ALTER TABLE platform ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(36);

-- Add tenant_id to ticket
ALTER TABLE ticket ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(36);

-- Add tenant_id to reply
ALTER TABLE reply ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(36);

-- Add tenant_id to transaction
ALTER TABLE g_transaction ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(36);

-- Add tenant_id to affiliate
ALTER TABLE affiliate ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(36);

-- Add tenant_id to voucher
ALTER TABLE voucher ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(36);

-- Add tenant_id to promotion
ALTER TABLE promotion ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(36);

-- Add tenant_id to complain
ALTER TABLE complain ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(36);

-- Add tenant_id to review
ALTER TABLE review ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(36);

-- Add tenant_id to commission
ALTER TABLE commission ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(36);

-- Create Hibernate filter for tenant_id
CREATE INDEX IF NOT EXISTS idx_tenant_id_g_user ON g_user(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_id_g_order ON g_order(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_id_category ON category(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_id_g_service ON g_service(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_id_platform ON platform(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_id_ticket ON ticket(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_id_reply ON reply(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_id_g_transaction ON g_transaction(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_id_affiliate ON affiliate(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_id_voucher ON voucher(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_id_promotion ON promotion(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_id_complain ON complain(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_id_review ON review(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_id_commission ON commission(tenant_id);

-- Set default tenant_id for existing records
UPDATE g_user SET tenant_id = '0e22c37d-bfb5-4276-bd30-355fcdb39c9e' WHERE tenant_id IS NULL;
UPDATE g_order SET tenant_id = '0e22c37d-bfb5-4276-bd30-355fcdb39c9e' WHERE tenant_id IS NULL;
UPDATE category SET tenant_id = '0e22c37d-bfb5-4276-bd30-355fcdb39c9e' WHERE tenant_id IS NULL;
UPDATE g_service SET tenant_id = '0e22c37d-bfb5-4276-bd30-355fcdb39c9e' WHERE tenant_id IS NULL;
UPDATE platform SET tenant_id = '0e22c37d-bfb5-4276-bd30-355fcdb39c9e' WHERE tenant_id IS NULL;
UPDATE ticket SET tenant_id = '0e22c37d-bfb5-4276-bd30-355fcdb39c9e' WHERE tenant_id IS NULL;
UPDATE reply SET tenant_id = '0e22c37d-bfb5-4276-bd30-355fcdb39c9e' WHERE tenant_id IS NULL;
UPDATE g_transaction SET tenant_id = '0e22c37d-bfb5-4276-bd30-355fcdb39c9e' WHERE tenant_id IS NULL;
UPDATE affiliate SET tenant_id = '0e22c37d-bfb5-4276-bd30-355fcdb39c9e' WHERE tenant_id IS NULL;
UPDATE voucher SET tenant_id = '0e22c37d-bfb5-4276-bd30-355fcdb39c9e' WHERE tenant_id IS NULL;
UPDATE promotion SET tenant_id = '0e22c37d-bfb5-4276-bd30-355fcdb39c9e' WHERE tenant_id IS NULL;
UPDATE complain SET tenant_id = '0e22c37d-bfb5-4276-bd30-355fcdb39c9e' WHERE tenant_id IS NULL;
UPDATE review SET tenant_id = '0e22c37d-bfb5-4276-bd30-355fcdb39c9e' WHERE tenant_id IS NULL;
UPDATE commission SET tenant_id = '0e22c37d-bfb5-4276-bd30-355fcdb39c9e' WHERE tenant_id IS NULL;
