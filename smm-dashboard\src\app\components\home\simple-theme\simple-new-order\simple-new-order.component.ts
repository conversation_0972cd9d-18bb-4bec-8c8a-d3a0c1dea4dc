import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { SimpleServiceDropdownComponent } from '../../../common/service-dropdown/simple/simple-service-dropdown.component';
import { IconsModule } from '../../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { OrderSuccessComponent } from '../../../common/order-success/order-success.component';
import { CurrencyConvertPipe } from '../../../../core/pipes/currency-convert.pipe';

import { ServiceAutocompleteComponent } from "../../../common/service-autocomplete/service-autocomplete.component";
import { BaseNewOrderComponent } from '../../new-order/base-new-order.component';
import { NewOrderLogicService } from '../../services/new-order-logic.service';
import { PlatformSelectionService } from '../../../../core/services/platform-selection.service';
import { ServiceSelectionService } from '../../../../core/services/service-selection.service';
import { FavoritesService } from '../../../../core/services/favorites.service';
import { OrderService } from '../../../../core/services/order.service';
import { VoucherService } from '../../../../core/services/voucher.service';
import { CategoriesService } from '../../../../core/services/categories.service';
import { SimpleIconDropdownComponent } from '../../../common/icon-dropdown/simple/simple-icon-dropdown.component';

@Component({
  selector: 'app-simple-new-order',
  standalone: true,
  imports: [
    FormsModule,
    CommonModule,
    SimpleServiceDropdownComponent,
    IconsModule,
    TranslateModule,
    OrderSuccessComponent,
    ServiceAutocompleteComponent,
    CurrencyConvertPipe,
    SimpleIconDropdownComponent
  ],
  templateUrl: './simple-new-order.component.html',
  styleUrls: ['./simple-new-order.component.css']
})
export class SimpleNewOrderComponent extends BaseNewOrderComponent {

  constructor(
    newOrderLogicService: NewOrderLogicService,
    platformSelectionService: PlatformSelectionService,
    serviceSelectionService: ServiceSelectionService,
    favoritesService: FavoritesService,
    orderService: OrderService,
    voucherService: VoucherService,
    categoriesService: CategoriesService
  ) {
    super(
      newOrderLogicService,
      platformSelectionService,
      serviceSelectionService,
      favoritesService,
      orderService,
      voucherService,
      categoriesService
    );
  }

  // Helper methods for template
  shouldShowFormFields(state: any): boolean {
    return (state.activeButton !== 'favorite' && state.services.length > 0) || 
           (state.activeButton === 'favorite' && state.favoriteServices.length > 0);
  }

  updateFormData(field: string, value: any): void {
    this.newOrderLogicService.updateFormData({ [field]: value });
  }
}
