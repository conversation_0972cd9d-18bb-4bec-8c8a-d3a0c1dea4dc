import { Injectable } from '@angular/core';
import { AdminServiceService } from './admin-service.service';
import { SuperGeneralSvRes } from '../../model/response/super-general-sv.model';
import { ExtendedCategoryRes } from '../../model/extended/extended-category.model';
import { CategoryService } from './category.service';

@Injectable({
  providedIn: 'root'
})
export class DragDropService {
  constructor(
    private adminService: AdminServiceService,
    private categoryService: CategoryService
  ) {}

  /**
   * Handles the drop of a service
   * @param sourceCategoryId The source category ID
   * @param targetCategoryId The target category ID
   * @param sourceIndex The source index
   * @param targetIndex The target index
   * @param callback Callback function to execute after completion
   */
  handleServiceDrop(
    sourceCategoryId: string,
    targetCategoryId: string,
    sourceIndex: number,
    targetIndex: number,
    callback?: () => void
  ): void {
    console.log(`Moving service from category ${sourceCategoryId}, index ${sourceIndex} to category ${targetCategoryId}, index ${targetIndex}`);

    try {
      // If source and target categories are the same, swap items instead of reordering
      if (sourceCategoryId === targetCategoryId) {
        // Get the current services by category
        const currentServicesByCategory = {...this.adminService.getServicesByCategoryValue()};

        // Get the services for the specified category
        const categoryServices = [...(currentServicesByCategory[sourceCategoryId] || [])];

        // Get the services to swap
        const service1 = categoryServices[sourceIndex];
        const service2 = categoryServices[targetIndex];

        if (!service1 || !service2) {
          console.error('Failed to find services to swap');
          return;
        }

        console.log(`Swapping services: ${service1.id} and ${service2.id}`);

        // Swap the items in the local array
        [categoryServices[sourceIndex], categoryServices[targetIndex]] = [categoryServices[targetIndex], categoryServices[sourceIndex]];

        // Update the services by category
        currentServicesByCategory[sourceCategoryId] = categoryServices;
        this.adminService.updateServicesByCategory(currentServicesByCategory);

        // Call the service to update the backend - using the swap API
        this.adminService.swapServiceSort(service1.id, service2.id).subscribe({
          next: () => {
            console.log('Service sort order updated successfully');
            if (callback) callback();
          },
          error: (error) => {
            console.error('Error updating service sort order:', error);
          }
        });
      } else {
        // If source and target categories are different, move the service to the new category
        console.log(`Moving service from category ${sourceCategoryId}, index ${sourceIndex} to category ${targetCategoryId}, index ${targetIndex}`);

        this.adminService.moveServiceToCategory(sourceCategoryId, targetCategoryId, sourceIndex, targetIndex);

        // Force update of the UI
        setTimeout(() => {
          if (callback) callback();
        }, 100);
      }

      console.log('Service moved successfully');
    } catch (error) {
      console.error('Error handling service drop:', error);
    }
  }



  /**
   * Handles the drop of a category
   * @param sourceIndex The source index
   * @param targetIndex The target index
   * @param categories The categories array
   * @param callback Callback function to execute after completion
   */
  handleCategoryDrop(
    sourceIndex: number,
    targetIndex: number,
    categories: ExtendedCategoryRes[],
    callback?: (updatedCategories: ExtendedCategoryRes[]) => void
  ): void {
    console.log(`Swapping categories from index ${sourceIndex} to index ${targetIndex}`);

    try {
      // If source and target indices are the same, no need to reorder
      if (sourceIndex === targetIndex) {
        console.log('Source and target indices are the same, no need to reorder');
        return;
      }

      // Get a copy of the current categories
      const categoriesCopy = [...categories];

      // Validate indices
      if (sourceIndex < 0 || sourceIndex >= categoriesCopy.length) {
        console.error(`Invalid source index: ${sourceIndex}, max: ${categoriesCopy.length - 1}`);
        return;
      }

      if (targetIndex < 0 || targetIndex >= categoriesCopy.length) {
        console.error(`Invalid target index: ${targetIndex}, max: ${categoriesCopy.length - 1}`);
        return;
      }

      // Get the categories being swapped
      const sourceCategory = categoriesCopy[sourceIndex];
      const targetCategory = categoriesCopy[targetIndex];

      console.log(`Swapping categories: ${sourceCategory.id} (${sourceCategory.name}) and ${targetCategory.id} (${targetCategory.name})`);

      // Swap the items in the local array
      [categoriesCopy[sourceIndex], categoriesCopy[targetIndex]] = [categoriesCopy[targetIndex], categoriesCopy[sourceIndex]];

      // Update the servicesByCategory in AdminServiceService
      this.categoryService.updateServicesByCategoryOrder(categoriesCopy);

      // Call the API to swap category sort order
      this.adminService.swapCategorySort(sourceCategory.id, targetCategory.id).subscribe({
        next: () => {
          // Log the new category order for debugging
          console.log('Categories swapped successfully');
          console.log('New category order:', categoriesCopy.map(c => `${c.name} (${c.id})`));

          if (callback) callback(categoriesCopy);
        },
        error: (error) => {
          console.error('Error swapping categories:', error);
        }
      });
    } catch (error) {
      console.error('Error handling category drop:', error);
    }
  }

  /**
   * Finds a service's index in a category
   * @param categoryId The category ID
   * @param serviceId The service ID
   * @param displayCategories The display categories array
   * @returns The index of the service in the category, or -1 if not found
   */
  findServiceIndex(categoryId: string, serviceId: number, displayCategories: ExtendedCategoryRes[]): number {
    const category = this.findCategoryById(categoryId, displayCategories);
    if (!category) return -1;

    return category.services.findIndex(service => service.id === serviceId);
  }

  /**
   * Finds a category by ID
   * @param categoryId The category ID
   * @param displayCategories The display categories array
   * @returns The category, or undefined if not found
   */
  findCategoryById(categoryId: string, displayCategories: ExtendedCategoryRes[]): ExtendedCategoryRes | undefined {
    return displayCategories.find(category => category.id.toString() === categoryId);
  }

  /**
   * Moves selected services to a new category
   * @param selectedServices The selected service IDs
   * @param targetCategory The target category
   * @param displayCategories The display categories array
   * @param callback Callback function to execute after completion
   */
  moveServicesToCategory(
    selectedServices: Set<number>,
    targetCategory: any,
    displayCategories: ExtendedCategoryRes[],
    callback?: () => void
  ): void {
    const selectedServiceIds = Array.from(selectedServices);

    // Find the services to move
    const servicesToMove: { service: SuperGeneralSvRes, categoryId: string }[] = [];

    displayCategories.forEach(category => {
      category.services.forEach(service => {
        if (selectedServiceIds.includes(service.id)) {
          servicesToMove.push({
            service,
            categoryId: category.id.toString()
          });
        }
      });
    });

    // Move each service to the new category
    servicesToMove.forEach(item => {
      const sourceCategory = item.categoryId;
      const targetCategoryId = targetCategory.id.toString();
      const sourceIndex = this.findServiceIndex(sourceCategory, item.service.id, displayCategories);

      if (sourceIndex !== -1) {
        // Move the service to the end of the target category
        const targetIndex = this.findCategoryById(targetCategoryId, displayCategories)?.services.length || 0;
        this.adminService.moveServiceToCategory(sourceCategory, targetCategoryId, sourceIndex, targetIndex);
      }
    });

    // Refresh the display
    setTimeout(() => {
      if (callback) callback();
    }, 100);
  }
}
