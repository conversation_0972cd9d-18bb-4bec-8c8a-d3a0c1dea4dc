import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { Observable } from 'rxjs';

// Services
import { MessOrderLogicService, MessOrderState } from '../../services/mess-order-logic.service';

// Components
import { SimpleMessOrderComponent as OriginalSimpleMessOrderComponent } from '../../simple-mess-order/simple-mess-order.component';
import { ClassicMessOrderComponent } from '../../classic-mess-order/classic-mess-order.component';

@Component({
  selector: 'app-simple-theme-mess-order',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    OriginalSimpleMessOrderComponent,
    ClassicMessOrderComponent
  ],
  templateUrl: './simple-mess-order.component.html',
  styleUrl: './simple-mess-order.component.css'
})
export class SimpleMessOrderComponent implements OnInit, OnD<PERSON>roy {
  // Mess order logic state
  messOrderState$: Observable<MessOrderState>;

  constructor(private messOrderLogicService: MessOrderLogicService) {
    this.messOrderState$ = this.messOrderLogicService.state$;
  }

  ngOnInit(): void {
    // MessOrderLogicService handles all initialization
  }

  ngOnDestroy(): void {
    // MessOrderLogicService is singleton, no cleanup needed
  }

  // Delegate methods to MessOrderLogicService for template compatibility
  swapComponent(type: 'simple' | 'classic'): void {
    this.messOrderLogicService.swapComponent(type);
  }
}
