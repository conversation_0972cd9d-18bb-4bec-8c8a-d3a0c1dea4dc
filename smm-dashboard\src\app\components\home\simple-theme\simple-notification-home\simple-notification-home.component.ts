import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconsModule } from '../../../../icons/icons.module';
import { BaseNotificationHomeComponent } from '../../notification-home/base-notification-home.component';

@Component({
  selector: 'app-simple-notification-home',
  standalone: true,
  imports: [CommonModule, IconsModule],
  templateUrl: './simple-notification-home.component.html',
  styleUrls: ['./simple-notification-home.component.css']
})
export class SimpleNotificationHomeComponent extends BaseNotificationHomeComponent {

  constructor() {
    super();
  }
}
