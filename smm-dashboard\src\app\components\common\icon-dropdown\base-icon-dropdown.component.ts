import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, OnChanges, SimpleChanges, AfterViewInit, HostListener, ElementRef, Renderer2 } from '@angular/core';
import { Subscription } from 'rxjs';
import { IconDropdownLogicService, IconDropdownState } from './icon-dropdown-logic.service';
import { IconBaseModel } from '../../../model/base-model';

@Component({
  template: '', // Will be overridden by child components
})
export abstract class BaseIconDropdownComponent implements OnInit, OnDestroy, OnChanges, AfterViewInit {
  @Input() options: IconBaseModel[] = [];
  @Input() placeholder: string = 'Chọn một tùy chọn';
  @Input() iconSize: number = 20;
  @Input() selectedOption: IconBaseModel | undefined;
  @Input() customClassButton: string = '';
  @Input() customClassDropdown: string = '';

  @Output() selected = new EventEmitter<IconBaseModel>();

  // State from service
  state$ = this.iconDropdownLogicService.state$;
  
  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(
    protected iconDropdownLogicService: IconDropdownLogicService,
    protected elementRef: ElementRef,
    protected renderer: Renderer2
  ) {}

  ngOnInit(): void {
    // Initialize dropdown with current inputs
    this.iconDropdownLogicService.initializeDropdown(
      this.options,
      this.selectedOption,
      this.placeholder,
      this.iconSize
    );

    // Subscribe to state changes - removed automatic emission to prevent circular updates
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Update options if they change
    if (changes['options'] && changes['options'].currentValue) {
      this.iconDropdownLogicService.updateOptions(changes['options'].currentValue);
    }

    // Update selected option if it changes from parent
    if (changes['selectedOption'] && changes['selectedOption'].currentValue) {
      this.iconDropdownLogicService.updateSelectedOption(changes['selectedOption'].currentValue);
    }
  }

  ngAfterViewInit(): void {
    // Add a small delay to ensure the component is fully rendered
    setTimeout(() => {
      this.iconDropdownLogicService.updateDropdownPosition(this.elementRef, this.renderer);
    }, 0);
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.subscriptions = [];
    
    // Clean up service
    this.iconDropdownLogicService.destroy();
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    this.iconDropdownLogicService.handleDocumentClick(this.elementRef, event);
  }

  @HostListener('window:resize')
  onWindowResize() {
    this.iconDropdownLogicService.updateDropdownPosition(this.elementRef, this.renderer);
  }

  @HostListener('window:scroll', ['$event'])
  onWindowScroll() {
    requestAnimationFrame(() => {
      this.iconDropdownLogicService.updateDropdownPosition(this.elementRef, this.renderer);
    });
  }

  // Helper methods that can be used by child components
  toggleDropdown(event?: MouseEvent): void {
    this.iconDropdownLogicService.toggleDropdown(this.elementRef, this.renderer, event);
  }

  selectOption(option: IconBaseModel, event?: MouseEvent): void {
    const selectedOption = this.iconDropdownLogicService.selectOption(option, event);
    this.selected.emit(selectedOption);
  }

  // Public method to programmatically select an option
  updateSelectedOption(option: IconBaseModel): void {
    if (option && option.id) {
      console.log('Icon dropdown programmatically selecting option:', option.label);
      this.iconDropdownLogicService.updateSelectedOption(option);
      // Don't emit the selected event to avoid circular updates
    }
  }

  // Helper getters for templates
  get currentState(): IconDropdownState {
    return this.iconDropdownLogicService.currentState;
  }

  get isOpen(): boolean {
    return this.currentState.isOpen;
  }

  get currentSelectedOption(): IconBaseModel | undefined {
    return this.currentState.selectedOption;
  }

  get currentOptions(): IconBaseModel[] {
    return this.currentState.options;
  }

  get currentPlaceholder(): string {
    return this.currentState.placeholder;
  }
}
