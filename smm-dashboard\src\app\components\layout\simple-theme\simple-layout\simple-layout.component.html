<!-- Simple Layout Theme - Modern Design -->
<div class="simple-layout-container" *ngIf="layoutState$ | async as layoutState" [ngClass]="getLayoutClasses()">
  
  <!-- Background Elements -->
  <div class="layout-background">
    <div class="bg-pattern"></div>
    <div class="bg-gradient"></div>
  </div>

  <!-- Sidebar -->
  <aside class="layout-sidebar" [class.open]="layoutState.isOpen">
    <app-simple-sidebar [isOpen]="layoutState.isOpen"></app-simple-sidebar>
  </aside>

  <!-- Mobile Overlay -->
  <div *ngIf="layoutState.isOpen" class="mobile-overlay active md:hidden" (click)="closeSidebar()"></div>

  <!-- Main Content Area -->
  <div class="layout-main">
    
    <!-- Header -->
    <header class="layout-header">
      <app-simple-header></app-simple-header>
    </header>

    <!-- Content Wrapper -->
    <main class="layout-content">
      <div class="content-container">
        
        <!-- Page Content -->
       
          <router-outlet></router-outlet>
 

        <!-- Content Decorations -->
        <div class="content-decorations">
          <div class="decoration-circle decoration-1"></div>
          <div class="decoration-circle decoration-2"></div>
          <div class="decoration-line decoration-3"></div>
        </div>

      </div>
    </main>

    <!-- Footer -->
    
  </div>

  <!-- Floating Elements -->
  <div class="floating-elements">
    <app-floating-contact-buttons></app-floating-contact-buttons>
  </div>

  <!-- Mobile Overlay -->
  <div class="mobile-overlay" 
       [class.active]="layoutState.isOpen && layoutState.isMobile"
       (click)="closeSidebar()">
  </div>

</div>
