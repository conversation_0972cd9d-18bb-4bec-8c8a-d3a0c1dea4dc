-- Create table for tenant custom languages (PostgreSQL)
CREATE TABLE IF NOT EXISTS tenant_custom_languages (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL,
    language_code VARCHAR(20) NOT NULL,
    language_name VARCHAR(100) NOT NULL,
    flag_class VARCHAR(20),
    description VARCHAR(500),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Unique constraint to prevent duplicate language codes per tenant
    CONSTRAINT unique_tenant_language UNIQUE (tenant_id, language_code)
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_tenant_custom_languages_tenant_id ON tenant_custom_languages (tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_custom_languages_language_code ON tenant_custom_languages (language_code);
CREATE INDEX IF NOT EXISTS idx_tenant_custom_languages_is_active ON tenant_custom_languages (is_active);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at on row updates
CREATE TRIGGER update_tenant_custom_languages_updated_at
    BEFORE UPDATE ON tenant_custom_languages
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add some sample data for testing (optional)
-- INSERT INTO tenant_custom_languages (tenant_id, language_code, language_name, flag_class, description, is_active) VALUES
-- ('default', 'vi2', 'Việt Nam 2', 'fi fi-vn', 'Custom Vietnamese variant', TRUE),
-- ('default', 'en-us', 'English (US)', 'fi fi-us', 'American English variant', TRUE),
-- ('default', 'custom1', 'Custom Language 1', 'fi fi-xx', 'A custom language for testing', TRUE);
