import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SuperGeneralSvRes } from '../../../../../model/response/super-general-sv.model';
import { ServiceAutocompleteState } from '../../services/service-autocomplete-logic.service';
import { ServiceLabelComponent } from '../../../service-label/service-label.component';

@Component({
  selector: 'app-simple-service-autocomplete',
  standalone: true,
  imports: [CommonModule, ServiceLabelComponent],
  templateUrl: './simple-service-autocomplete.component.html',
  styleUrl: './simple-service-autocomplete.component.css'
})
export class SimpleServiceAutocompleteComponent {
  @Input() serviceAutocompleteState: ServiceAutocompleteState | null = null;
  @Input() services: SuperGeneralSvRes[] = [];
  @Input() isVisible: boolean = false;
  @Input() highlightedIndex: number = -1;
  @Output() serviceSelected = new EventEmitter<SuperGeneralSvRes>();

  onSelectService(service: SuperGeneralSvRes): void {
    this.serviceSelected.emit(service);
  }
}
