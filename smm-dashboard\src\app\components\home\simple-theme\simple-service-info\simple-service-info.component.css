.simple-service-info-container {
  @apply flex flex-col gap-4 p-0;
}

/* Service Selected State */
.service-selected {
  @apply flex flex-col gap-4;
}

.service-header {
  @apply flex items-center justify-between pb-3 border-b;
  border-color: rgba(30, 41, 59, 0.1);
}

.service-name {
  @apply text-lg font-semibold m-0;
  color: #1e293b;
}

.service-id {
  @apply text-sm px-2 py-1 rounded;
  color: #64748b;
  background: rgba(30, 41, 59, 0.05);
}

/* Service Details Grid */
.service-details-grid {
  @apply grid gap-3;
  grid-template-columns: 1fr;
}

.detail-item {
  @apply flex flex-col gap-1;
}

.detail-label {
  @apply flex items-center gap-2 text-sm font-medium;
  color: #64748b;
}

.detail-icon {
  @apply w-4 h-4;
  color: #1e293b;
}

.detail-value {
  @apply text-sm pl-6;
  color: #1e293b;
}

.example-link {
  @apply underline break-all;
  color: #1e293b;
}

.example-link:hover {
  color: #0f172a;
}

/* Description */
.service-description {
  @apply flex flex-col gap-2 pt-3 border-t;
  border-color: rgba(30, 41, 59, 0.1);
}

.description-label {
  @apply flex items-center gap-2 text-sm font-medium;
  color: #64748b;
}

.description-text {
  @apply text-sm leading-relaxed m-0 pl-6;
  color: #1e293b;
}

/* No Service Selected State */
.no-service-selected {
  @apply flex items-center justify-center min-h-[200px];
}

.empty-state {
  @apply text-center;
}

.empty-icon {
  @apply text-4xl mb-3;
  color: #94a3b8;
}

.empty-title {
  @apply text-lg font-medium mb-2;
  color: #64748b;
}

.empty-message {
  @apply text-sm;
  color: #94a3b8;
}

/* Responsive Design */
@media (min-width: 640px) {
  .service-details-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (min-width: 768px) {
  .simple-service-info-container {
    @apply p-6;
  }
  
  .service-details-grid {
    @apply gap-4;
  }
  
  .detail-item {
    @apply gap-2;
  }
}
