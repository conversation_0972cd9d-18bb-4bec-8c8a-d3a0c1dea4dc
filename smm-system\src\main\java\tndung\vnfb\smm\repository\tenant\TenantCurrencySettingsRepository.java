package tndung.vnfb.smm.repository.tenant;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import tndung.vnfb.smm.entity.TenantCurrencySettings;

import java.util.List;
import java.util.Optional;

@Repository
public interface TenantCurrencySettingsRepository extends TenantAwareRepository<TenantCurrencySettings, Long> {

    // Use explicit tenant filtering in queries since custom methods don't go through TenantAwareRepositoryImpl

    @Query("SELECT tcs FROM TenantCurrencySettings tcs WHERE tcs.currencyCode = :currencyCode AND (tcs.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} OR tcs.tenantId = '*')")
    Optional<TenantCurrencySettings> findByCurrencyCode(@Param("currencyCode") String currencyCode);

    @Query("SELECT tcs FROM TenantCurrencySettings tcs WHERE tcs.currencyCode IN :currencyCodes AND (tcs.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} OR tcs.tenantId = '*')")
    List<TenantCurrencySettings> findByCurrencyCodeIn(@Param("currencyCodes") List<String> currencyCodes);

    @Modifying
    @Query("DELETE FROM TenantCurrencySettings tcs WHERE tcs.currencyCode = :currencyCode AND (tcs.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} OR tcs.tenantId = '*')")
    void deleteByCurrencyCode(@Param("currencyCode") String currencyCode);
}
