import { Injectable } from '@angular/core';
import { BehaviorSubject, Subscription } from 'rxjs';
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';
import { SuperPlatformRes } from '../../../model/response/super-platform.model';
import { OrderRes } from '../../../model/response/order-res.model';
import { IconBaseModel } from '../../../model/base-model';
import { IconName } from '@fortawesome/fontawesome-svg-core';
import { UserRes } from '../../../model/response/user-res.model';
import { VoucherLiteRes } from '../../../model/response/voucher-lite-res.model';
import { CreateOrderReq, OrderService } from '../../../core/services/order.service';
import { CategoriesService } from '../../../core/services/categories.service';
import { VoucherService } from '../../../core/services/voucher.service';
import { UserService } from '../../../core/services/user.service';
import { TranslateService } from '@ngx-translate/core';

export interface NewOrderState {
  categories: IconBaseModel[];
  services: SuperGeneralSvRes[];
  platforms: SuperPlatformRes[];
  selectedCategory: IconBaseModel | undefined;
  selectedService: SuperGeneralSvRes | undefined;
  selectedPlatformId: number | null;

  // User data
  user: UserRes | undefined;
  favoriteServiceIds: number[];
  
  // Search related properties
  searchTerm: string;
  searchResults: SuperGeneralSvRes[];
  showAutocomplete: boolean;
  allServices: SuperGeneralSvRes[];
  
  // Active button state
  activeButton: 'new_order' | 'favorite' | 'auto_subscription';
  
  // Form data
  formData: {
    link: string;
    quantity: number | null;
    fee: number;
    comments: string;
    voucher_code: string;
  };
  
  // Order creation related properties
  createdOrder: OrderRes | null;
  isOrderSuccess: boolean;
  isLoading: boolean;
  errorMessage: string;
  quantityError: string;
  
  // Price and discount related properties
  originalPrice: number;
  discountPercent: number;
  discountType: string;
  
  // Voucher related properties
  voucherDiscount: number;
  voucherApplied: boolean;
  voucherError: string;
  isValidatingVoucher: boolean;
  
  // Favorite services
  favoriteServices: SuperGeneralSvRes[];
}

@Injectable({
  providedIn: 'root'
})
export class NewOrderLogicService {
  private subscriptions: Subscription[] = [];

  // State management
  private _state$ = new BehaviorSubject<NewOrderState>({
    categories: [],
    services: [],
    platforms: [],
    selectedCategory: undefined,
    selectedService: undefined,
    selectedPlatformId: null,
    user: undefined,
    favoriteServiceIds: [],
    searchTerm: '',
    searchResults: [],
    showAutocomplete: false,
    allServices: [],
    activeButton: 'new_order',
    formData: {
      link: '',
      quantity: null,
      fee: 0,
      comments: '',
      voucher_code: ''
    },
    createdOrder: null,
    isOrderSuccess: false,
    isLoading: false,
    errorMessage: '',
    quantityError: '',
    originalPrice: 0,
    discountPercent: 0,
    discountType: '',
    voucherDiscount: 0,
    voucherApplied: false,
    voucherError: '',
    isValidatingVoucher: false,
    favoriteServices: []
  });

  public readonly state$ = this._state$.asObservable();

  constructor(
    private categoriesService: CategoriesService,
    private orderService: OrderService,
    private voucherService: VoucherService,
    private userService: UserService,
    private translate: TranslateService
  ) {
    this.initialize();
  }

  // State getters
  get currentState(): NewOrderState {
    return this._state$.value;
  }

  // State update methods
  updateState(partialState: Partial<NewOrderState>): void {
    this._state$.next({ ...this.currentState, ...partialState });
  }

  updateFormData(partialFormData: Partial<NewOrderState['formData']>): void {
    const currentState = this.currentState;
    this.updateState({
      formData: { ...currentState.formData, ...partialFormData }
    });
  }

  // Reset methods
  resetOrderState(): void {
    this.updateState({
      createdOrder: null,
      isOrderSuccess: false,
      errorMessage: '',
      quantityError: ''
    });
  }

  resetForm(): void {
    this.updateState({
      formData: {
        link: '',
        quantity: null,
        fee: 0,
        comments: '',
        voucher_code: ''
      },
      selectedService: undefined,
      errorMessage: '',
      quantityError: '',
      voucherApplied: false,
      voucherError: '',
      voucherDiscount: 0
    });
  }

  // Initialize service
  private initialize(): void {
    this.loadPlatforms();
    this.subscribeToUserData();
  }

  // Subscribe to user data changes
  private subscribeToUserData(): void {
    const userSubscription = this.userService.user$.subscribe(user => {
      this.updateState({ user });

      // Recalculate fee if user data changes and we have a selected service
      if (this.currentState.selectedService) {
        this.calculateFee();
      }

      // If user is not loaded yet, trigger a fetch
      if (!user) {
        this.userService.get$.next();
      }
    });

    this.subscriptions.push(userSubscription);
  }

  // Load platforms and initialize categories
  loadPlatforms(): void {
    this.categoriesService.getPlatforms().subscribe(platforms => {
      const allServices: SuperGeneralSvRes[] = [];
      const categories: IconBaseModel[] = [];

      // Store all categories and services for search functionality
      platforms.forEach(platform => {
        platform.categories.forEach(category => {
          if (!category.hide) {
            // Add category to the categories list
            categories.push({
              id: category.id.toString(),
              label: category.name,
              icon: platform.icon as IconName,
              sort: category.sort
            });

            // Add all services to allServices array for search functionality
            category.services.forEach(service => {
              allServices.push(service);
            });
          }
        });
      });

      // Sort categories by sort field for consistent display
      categories.sort((a, b) => a.sort - b.sort);

      this.updateState({
        platforms,
        categories,
        allServices
      });

      // Always set the first category as the selected category and load its services
      if (categories.length > 0) {
        const firstCategory = categories[0];
        this.updateState({ selectedCategory: firstCategory });
        this.onCategorySelected(firstCategory);
      } else {
        // Initialize with empty services if no categories are available
        this.updateState({
          services: [],
          selectedService: undefined
        });
      }

      console.log('Platforms loaded, all categories displayed');
      console.log('Total categories:', categories.length);
      console.log('Total services available for search:', allServices.length);
    });
  }

  // Handle platform selection
  handlePlatformSelection(platform: SuperPlatformRes): void {
    console.log('NewOrderLogicService received platform selection:', platform.name);

    // Store the selected platform ID
    this.updateState({ selectedPlatformId: platform.id });

    // Check if this is the "All networks" platform (id = 0)
    if (platform.id === 0) {
      // For "All networks", reload all categories from all platforms
      this.loadAllCategories();
      return;
    }

    // Filter categories to only show those from the selected platform
    const platformCategories = platform.categories
      .filter(category => !category.hide)
      .map(category => ({
        id: category.id.toString(),
        label: category.name,
        icon: platform.icon as IconName,
        sort: category.sort
      }))
      // Sort categories by sort field
      .sort((a, b) => a.sort - b.sort);

    // Update the categories list to only show categories from this platform
    this.updateState({ categories: platformCategories });
    console.log('Filtered categories for platform:', platform.name, 'Categories count:', platformCategories.length);

    // If there are categories for this platform, select the first one
    if (platformCategories.length > 0) {
      const categoryToSelect = platformCategories[0];
      console.log('Selecting category:', categoryToSelect.label);
      this.onCategorySelected(categoryToSelect);
    } else {
      // If no categories are available for this platform, clear the services
      this.updateState({
        services: [],
        selectedService: undefined
      });
      this.calculateFee();
    }
  }

  // Helper method to load all categories from all platforms
  private loadAllCategories(): void {
    const categories: IconBaseModel[] = [];

    // Add all categories from all platforms
    this.currentState.platforms.forEach(platform => {
      if (!platform.hide) {
        platform.categories.forEach(category => {
          if (!category.hide) {
            categories.push({
              id: category.id.toString(),
              label: category.name,
              icon: platform.icon as IconName,
              sort: category.sort
            });
          }
        });
      }
    });

    // Sort categories by sort field
    categories.sort((a, b) => a.sort - b.sort);

    this.updateState({ categories });

    console.log('Loaded all categories, count:', categories.length);

    // Select the first category if available
    if (categories.length > 0) {
      const firstCategory = categories[0];
      this.updateState({ selectedCategory: firstCategory });
      this.onCategorySelected(firstCategory);
    } else {
      // If no categories are available, clear the services
      this.updateState({
        services: [],
        selectedService: undefined
      });
      this.calculateFee();
    }
  }

  // Handle category selection
  onCategorySelected(category: IconBaseModel): void {
    // Always set the selected category to the provided category
    this.updateState({ selectedCategory: category });

    // Find the selected category in platforms data and get its services
    for (const platform of this.currentState.platforms) {
      const foundCategory = platform.categories.find(c => c.id.toString() === category.id);
      if (foundCategory) {
        // Map SuperGeneralSvRes to services
        const services = foundCategory.services;

        this.updateState({ services });

        // Set the first service as the selected service
        if (services.length > 0) {
          const selectedService = services[0];
          this.updateState({ selectedService });
          this.calculateFee();
        }

        console.log('Services loaded for category:', category.label, 'Services count:', services.length);
        break;
      }
    }
  }

  // Calculate fee based on selected service and quantity, applying discounts
  calculateFee(): void {
    const state = this.currentState;

    if (state.selectedService && state.formData.quantity !== null && state.formData.quantity > 0) {
      // Get the base price
      let basePrice = state.selectedService.price;
      let discountedPrice = basePrice;
      let discountPercent = 0;
      let discountType = '';

      // Check if service has special prices (priority)
      if (state.selectedService.special_prices && state.selectedService.special_prices.length > 0) {
        const specialPrice = state.selectedService.special_prices[0]; // Use the first special price

        if (specialPrice.discount_type === 'FIXED') {
          // Fixed price discount
          discountedPrice = specialPrice.discount_value;
          discountType = 'FIXED';
        } else if (specialPrice.discount_type === 'PERCENT') {
          // Percentage discount
          discountPercent = specialPrice.discount_value;
          discountedPrice = basePrice * (1 - discountPercent / 100);
          discountType = 'PERCENT';
        }
      } else {
        // Check for user's custom discount (only if no special prices)
        const userCustomDiscount = state.user && (state.user as any).custom_discount ? (state.user as any).custom_discount : 0;

        if (userCustomDiscount > 0) {
          discountPercent = userCustomDiscount;
          discountedPrice = basePrice * (1 - discountPercent / 100);
          discountType = 'PERCENT';
        }
      }

      // Store original and discounted prices for display
      const originalPrice = basePrice / 1000 * state.formData.quantity;

      // Calculate final fee based on discounted price
      let fee = discountedPrice / 1000 * state.formData.quantity;

      // Apply voucher discount if a voucher is applied
      if (state.voucherApplied && state.voucherDiscount > 0) {
        // Apply voucher discount (percentage)
        const voucherDiscountAmount = fee * (state.voucherDiscount / 100);
        fee -= voucherDiscountAmount;
      }

      this.updateState({
        originalPrice,
        discountPercent,
        discountType
      });

      this.updateFormData({ fee });

      console.log('Fee calculated:', fee,
                 'Original Price:', basePrice,
                 'Discounted Price:', discountedPrice,
                 'Discount:', discountPercent + '%',
                 'Voucher Discount:', state.voucherDiscount + '%',
                 'Quantity:', state.formData.quantity);
    } else {
      this.updateFormData({ fee: 0 });
      this.updateState({
        originalPrice: 0,
        discountPercent: 0,
        discountType: ''
      });
    }
  }

  // Validate quantity and calculate fee
  validateQuantityAndCalculateFee(): void {
    const state = this.currentState;
    this.updateState({ quantityError: '' });

    if (!state.selectedService) {
      return;
    }

    // If service type is 'Comment', calculate quantity based on number of comment lines
    if (state.selectedService.type === 'Custom Comments' && state.formData.comments) {
      // Split comments by newline and filter out empty lines
      const commentLines = state.formData.comments
        .split('\n')
        .filter(line => line.trim().length > 0);

      // Update quantity based on number of comment lines
      this.updateFormData({ quantity: commentLines.length });
    }

    const min = state.selectedService.min || 10;
    const max = state.selectedService.max || 5000000;

    if (state.formData.quantity !== null) {
      if (state.formData.quantity < min) {
        this.updateState({ quantityError: `Quantity must be at least ${min}` });
      } else if (state.formData.quantity > max) {
        this.updateState({ quantityError: `Quantity cannot exceed ${max}` });
      }
    }

    // Calculate fee regardless of validation errors
    this.calculateFee();
  }

  // Get comments array for display
  getCommentsArray(): string[] {
    const state = this.currentState;
    if (!state.formData.comments) {
      return [];
    }
    return state.formData.comments
      .split('\n')
      .filter(line => line.trim().length > 0);
  }

  // Validate voucher code and apply discount if valid
  validateVoucherCode(): void {
    const state = this.currentState;

    // Reset voucher state
    this.updateState({
      voucherError: '',
      voucherApplied: false,
      voucherDiscount: 0
    });

    // Check if voucher code is provided
    if (!state.formData.voucher_code || state.formData.voucher_code.trim() === '') {
      // No voucher code provided, recalculate fee without voucher
      this.calculateFee();
      return;
    }

    // Set loading state
    this.updateState({ isValidatingVoucher: true });

    // Call API to validate voucher code
    this.voucherService.verifyVoucherCode(state.formData.voucher_code.trim()).subscribe({
      next: (response: VoucherLiteRes) => {
        // Voucher is valid, apply discount
        this.updateState({
          voucherApplied: true,
          voucherDiscount: response.discount_value,
          isValidatingVoucher: false
        });

        // Recalculate fee with voucher discount
        this.calculateFee();

        console.log('Voucher applied successfully:', state.formData.voucher_code, 'Discount:', response.discount_value + '%');
      },
      error: (error) => {
        // Voucher is invalid
        let voucherError = '';
        if (error.status === 404) {
          voucherError = this.translate.instant('invalid_voucher');
        } else if (error.status === 410) {
          voucherError = this.translate.instant('voucher_expired');
        } else if (error.status === 429) {
          voucherError = this.translate.instant('voucher_limit_reached');
        } else {
          voucherError = error.message || this.translate.instant('invalid_voucher');
        }

        this.updateState({
          voucherError,
          voucherApplied: false,
          voucherDiscount: 0,
          isValidatingVoucher: false
        });

        // Recalculate fee without voucher discount
        this.calculateFee();

        console.error('Error validating voucher:', error);
      }
    });
  }

  // Clear voucher code and reset voucher state
  clearVoucherCode(): void {
    // Reset voucher code and state
    this.updateFormData({ voucher_code: '' });
    this.updateState({
      voucherError: '',
      voucherApplied: false,
      voucherDiscount: 0
    });

    // Recalculate fee without voucher discount
    this.calculateFee();

    console.log('Voucher code cleared');
  }

  // Set active button
  setActiveButton(button: 'new_order' | 'favorite' | 'auto_subscription'): void {
    this.updateState({ activeButton: button });
  }

  // Handle service selection from dropdown
  onServiceDropdownSelected(service: SuperGeneralSvRes): void {
    this.updateState({ selectedService: service });
    this.calculateFee();
  }

  // Create order
  onSubmit(): void {
    const state = this.currentState;

    if (!state.selectedService || state.quantityError !== '' || state.isLoading) {
      return;
    }

    this.updateState({
      isLoading: true,
      errorMessage: ''
    });

    const orderData: CreateOrderReq = {
      service_id: state.selectedService.id,
      link: state.formData.link,
      quantity: state.formData.quantity || 0,
      comments: state.selectedService.type === 'Custom Comments' ? this.getCommentsArray() : undefined,
      voucher_code: state.formData.voucher_code || undefined
    };

    this.orderService.createOrder(orderData).subscribe({
      next: (response: OrderRes) => {
        console.log('Order created successfully:', response);

        this.updateState({
          createdOrder: response,
          isOrderSuccess: true,
          isLoading: false
        });

        // Reset form after successful order
        this.resetForm();
      },
      error: (error) => {
        console.error('Error creating order:', error);

        let errorMessage = 'An error occurred while creating the order';
        if (error.error && error.error.message) {
          errorMessage = error.error.message;
        } else if (error.message) {
          errorMessage = error.message;
        }

        this.updateState({
          errorMessage,
          isLoading: false
        });
      }
    });
  }

  // Search functionality
  onSearchInput(event: any): void {
    const searchTerm = event.target.value;
    this.updateState({ searchTerm });

    if (searchTerm.length >= 2) {
      this.performSearch(searchTerm);
    } else {
      this.updateState({
        searchResults: [],
        showAutocomplete: false
      });
    }
  }

  private performSearch(searchTerm: string): void {
    const state = this.currentState;
    const results = state.allServices.filter(service =>
      service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.id.toString().includes(searchTerm)
    ).slice(0, 10); // Limit to 10 results

    this.updateState({
      searchResults: results,
      showAutocomplete: results.length > 0
    });
  }

  onSearchClick(): void {
    const state = this.currentState;
    if (state.searchTerm.length >= 2) {
      this.performSearch(state.searchTerm);
    }
  }

  onServiceSelected(service: SuperGeneralSvRes): void {
    this.updateState({
      selectedService: service,
      showAutocomplete: false,
      searchTerm: ''
    });
    this.calculateFee();
  }

  onAutocompleteVisibilityChange(visible: boolean): void {
    this.updateState({ showAutocomplete: visible });
  }

  handleSearchKeydown(event: KeyboardEvent): void {
    if (event.key === 'Escape') {
      this.updateState({ showAutocomplete: false });
    }
  }

  // Cleanup
  destroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.subscriptions = [];
  }
}
