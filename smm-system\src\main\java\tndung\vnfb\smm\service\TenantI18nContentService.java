package tndung.vnfb.smm.service;

import tndung.vnfb.smm.dto.request.TenantI18nContentReq;
import tndung.vnfb.smm.dto.response.TenantI18nContentRes;

import java.util.List;
import java.util.Map;

public interface TenantI18nContentService {

    /**
     * Get all translations for a specific language and tenant
     */
    TenantI18nContentRes getI18nContent(String languageCode);

    /**
     * Update translations for a specific language and tenant
     */
    TenantI18nContentRes updateI18nContent(String languageCode, TenantI18nContentReq request);

    /**
     * Get all available language codes for current tenant
     */
    List<String> getAvailableLanguageCodes();

    /**
     * Get translations for dashboard (public endpoint)
     */
    Map<String, Object> getDashboardTranslations(String languageCode);

    /**
     * Delete all translations for a specific language
     */
    void deleteI18nContent(String languageCode);

    /**
     * Get default template from en.json for a new language
     */
    Map<String, Object> getDefaultTemplate();

    /**
     * Get template for specific language, fallback to English if not found
     */
    Map<String, Object> getTemplateForLanguage(String languageCode);

    /**
     * Bulk import translations from JSON
     */
    TenantI18nContentRes importTranslations(String languageCode, Map<String, Object> translations);
}
