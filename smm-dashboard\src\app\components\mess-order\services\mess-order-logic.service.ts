import { Injectable } from '@angular/core';
import { BehaviorSubject, Subscription } from 'rxjs';

// Services
import { ThemeService, LayoutTheme } from '../../../core/services/theme.service';

export interface MessOrderState {
  // UI state
  activeComponent: 'simple' | 'classic';
  
  // Theme management
  currentTheme: LayoutTheme;
}

@Injectable({
  providedIn: 'root'
})
export class MessOrderLogicService {
  private subscriptions: Subscription[] = [];

  // State management
  private _state$ = new BehaviorSubject<MessOrderState>({
    activeComponent: 'simple',
    currentTheme: LayoutTheme.DEFAULT
  });

  // Public state observable
  public readonly state$ = this._state$.asObservable();

  // Current state getter
  private get currentState(): MessOrderState {
    return this._state$.value;
  }

  constructor(
    private themeService: ThemeService
  ) {
    this.initialize();
  }

  // Public getters for template access
  get activeComponent(): 'simple' | 'classic' {
    return this.currentState.activeComponent;
  }

  get currentTheme(): LayoutTheme {
    return this.currentState.currentTheme;
  }

  // Initialize service
  private initialize(): void {
    this.subscribeToThemeChanges();
  }

  // Update state helper
  private updateState(updates: Partial<MessOrderState>): void {
    const currentState = this._state$.value;
    this._state$.next({ ...currentState, ...updates });
  }

  // Subscribe to theme changes
  private subscribeToThemeChanges(): void {
    const themeSubscription = this.themeService.currentLayoutTheme$.subscribe((theme: LayoutTheme) => {
      this.updateState({ currentTheme: theme });
    });
    this.subscriptions.push(themeSubscription);
  }

  // Public methods for component interaction
  swapComponent(type: 'simple' | 'classic'): void {
    this.updateState({ activeComponent: type });
  }

  // Cleanup
  destroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.subscriptions = [];
  }
}
