import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { Observable } from 'rxjs';

// Services
import { AvatarLogicService, AvatarState } from '../../services/avatar-logic.service';

// Components
import { SvgIconComponent } from '../../../svg-icon/svg-icon.component';

@Component({
  selector: 'app-simple-avatar',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    SvgIconComponent
  ],
  templateUrl: './simple-avatar.component.html',
  styleUrl: './simple-avatar.component.css'
})
export class SimpleAvatarComponent implements OnInit, OnDestroy {
  // Avatar logic state
  avatarState$: Observable<AvatarState>;

  constructor(private avatarLogicService: AvatarLogicService) {
    this.avatarState$ = this.avatarLogicService.state$;
  }

  ngOnInit(): void {
    // AvatarLogicService handles all initialization
  }

  ngOnDestroy(): void {
    // AvatarLogicService is singleton, no cleanup needed
  }

  // Delegate methods to AvatarLogicService for template compatibility
  toggleMenu(): void {
    this.avatarLogicService.toggleMenu();
  }

  closeMenu(): void {
    this.avatarLogicService.closeMenu();
  }

  goToSettings(): void {
    this.avatarLogicService.goToSettings();
  }

  goToSecuritySettings(): void {
    this.avatarLogicService.goToSecuritySettings();
  }

  logout(): void {
    this.avatarLogicService.logout();
  }

  getAvatarPath(): string {
    return this.avatarLogicService.getAvatarPath();
  }
}
