import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IconsModule } from '../../../../../icons/icons.module';
import { BaseMfaSettingComponent } from '../../base-mfa-setting.component';
import { MfaSettingLogicService } from '../../mfa-setting-logic.service';
import { ToastService } from '../../../../../core/services/toast.service';
import { NotifyType } from '../../../../../constant/notify-type';

@Component({
  selector: 'app-simple-mfa-setting',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    FormsModule,
    ReactiveFormsModule,
    IconsModule
  ],
  templateUrl: './simple-mfa-setting.component.html',
  styleUrl: './simple-mfa-setting.component.css',
  providers: [MfaSettingLogicService]
})
export class SimpleMfaSettingComponent extends BaseMfaSettingComponent {

  constructor(private toastService: ToastService) {
    super();
  }

  /**
   * Copy secret key to clipboard
   */
  copySecretKey(): void {
    if (this.secretKey) {
      navigator.clipboard.writeText(this.secretKey).then(() => {
        this.toastService.showToast('Secret key copied to clipboard!', NotifyType.SUCCESS);
      }).catch(() => {
        this.toastService.showToast('Failed to copy secret key', NotifyType.ERROR);
      });
    }
  }
}
