import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';

// Components
import { LoadingComponent } from '../../../common/loading/loading.component';
import { IconsModule } from '../../../../icons/icons.module';

// Base component and service
import { BaseTicketDetailComponent } from '../../base-ticket-detail.component';
import { TicketDetailLogicService } from '../../services/ticket-detail-logic.service';

@Component({
  selector: 'app-simple-ticket-detail',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    FormsModule,
    LoadingComponent,
    IconsModule
  ],
  templateUrl: './simple-ticket-detail.component.html',
  styleUrl: './simple-ticket-detail.component.css'
})
export class SimpleTicketDetailComponent extends BaseTicketDetailComponent implements OnInit, OnDestroy {

  constructor(ticketDetailLogicService: TicketDetailLogicService) {
    super(ticketDetailLogicService);
  }

  override ngOnInit(): void {
    // Call parent initialization
    super.ngOnInit();
  }

  override ngOnDestroy(): void {
    // Call parent cleanup
    super.ngOnDestroy();
  }

  // Helper method for template
  getStatusIcon(status: string | undefined): string {
    if (!status) return 'question-circle';

    switch (status) {
      case 'Pending':
        return 'clock';
      case 'Accept':
        return 'check-circle';
      case 'Closed':
        return 'times-circle';
      case 'Solved':
        return 'check-double';
      default:
        return 'question-circle';
    }
  }

  // Helper method for template
  getStatusColor(status: string | undefined): string {
    if (!status) return '#6b7280';

    switch (status) {
      case 'Pending':
        return '#f59e0b';
      case 'Accept':
        return '#3b82f6';
      case 'Closed':
        return '#ef4444';
      case 'Solved':
        return '#10b981';
      default:
        return '#6b7280';
    }
  }
}
