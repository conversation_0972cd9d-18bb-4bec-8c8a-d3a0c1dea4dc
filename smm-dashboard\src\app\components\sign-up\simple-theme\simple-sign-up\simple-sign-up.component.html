<!-- Simple Theme Sign Up Component -->
<div class="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8" *ngIf="signUpState$ | async as signUpState">
  <div class="max-w-md w-full space-y-8">
    <!-- Header -->
    <div class="text-center">
      <h2 class="text-3xl font-bold text-gray-900">{{ 'signup.create_account' | translate }}</h2>
      <p class="mt-2 text-sm text-gray-600">{{ 'signup.join_community' | translate }}</p>
    </div>

    <!-- Registration Form -->
    <form [formGroup]="signUpState.registrationForm" (ngSubmit)="onSubmit()" class="mt-8 space-y-6">
      <div class="space-y-4">
        <!-- Email Field -->
        <div>
          <label for="email" class="block text-sm font-medium text-gray-700">
            {{ 'signup.email' | translate }}
          </label>
          <input
            id="email"
            type="email"
            formControlName="email"
            placeholder="{{ 'signup.enter_email' | translate }}"
            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            [ngClass]="{'border-red-500': email?.invalid && email?.touched}"
          >
          <div *ngIf="email?.invalid && email?.touched" class="mt-1 text-sm text-red-600">
            <div *ngIf="email?.errors?.['required']">{{ 'signup.email_required' | translate }}</div>
            <div *ngIf="email?.errors?.['email']">{{ 'signup.email_invalid' | translate }}</div>
          </div>
        </div>

        <!-- Username Field -->
        <div>
          <label for="username" class="block text-sm font-medium text-gray-700">
            {{ 'signup.username' | translate }}
          </label>
          <input
            id="username"
            type="text"
            formControlName="username"
            placeholder="{{ 'signup.enter_username' | translate }}"
            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            [ngClass]="{'border-red-500': username?.invalid && username?.touched}"
          >
          <div *ngIf="username?.invalid && username?.touched" class="mt-1 text-sm text-red-600">
            {{ 'signup.username_required' | translate }}
          </div>
        </div>

        <!-- Password Field -->
        <div>
          <label for="password" class="block text-sm font-medium text-gray-700">
            {{ 'signup.password' | translate }}
          </label>
          <input
            id="password"
            type="password"
            formControlName="password"
            placeholder="{{ 'signup.enter_password' | translate }}"
            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            [ngClass]="{'border-red-500': password?.invalid && password?.touched}"
          >
          <div *ngIf="password?.invalid && password?.touched" class="mt-1 text-sm text-red-600">
            <div *ngIf="password?.errors?.['required']">{{ 'signup.password_required' | translate }}</div>
            <div *ngIf="password?.errors?.['minlength']">{{ 'signup.password_min_length' | translate }}</div>
          </div>
        </div>

        <!-- Confirm Password Field -->
        <div>
          <label for="confirmPassword" class="block text-sm font-medium text-gray-700">
            {{ 'signup.confirm_password' | translate }}
          </label>
          <input
            id="confirmPassword"
            type="password"
            formControlName="confirmPassword"
            placeholder="{{ 'signup.enter_confirm_password' | translate }}"
            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            [ngClass]="{'border-red-500': confirmPassword?.invalid && confirmPassword?.touched}"
          >
          <div *ngIf="confirmPassword?.invalid && confirmPassword?.touched" class="mt-1 text-sm text-red-600">
            <div *ngIf="confirmPassword?.errors?.['required']">{{ 'signup.confirm_password_required' | translate }}</div>
            <div *ngIf="confirmPassword?.errors?.['passwordMismatch']">{{ 'signup.passwords_not_match' | translate }}</div>
          </div>
        </div>
      </div>

      <!-- Terms and Conditions -->
      <div class="flex items-center">
        <input
          id="terms"
          type="checkbox"
          formControlName="termsAgreed"
          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        >
        <label for="terms" class="ml-2 block text-sm text-gray-900">
          {{ 'signup.terms_agree' | translate }}
        </label>
      </div>
      <div *ngIf="termsAgreed?.invalid && termsAgreed?.touched" class="text-sm text-red-600">
        {{ 'signup.terms_required' | translate }}
      </div>

      <!-- Error Message -->
      <div *ngIf="signUpState.registrationError" class="text-red-600 text-sm text-center bg-red-50 p-3 rounded-md">
        {{ signUpState.registrationError }}
      </div>

      <!-- Submit Button -->
      <div>
        <button
          type="submit"
          [disabled]="signUpState.registrationForm.invalid || signUpState.isLoading"
          class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span *ngIf="!signUpState.isLoading">{{ 'signup.register' | translate }}</span>
          <span *ngIf="signUpState.isLoading" class="flex items-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ 'signup.registering' | translate }}
          </span>
        </button>
      </div>

      <!-- Login Link -->
      <div class="text-center">
        <p class="text-sm text-gray-600">
          {{ 'signup.already_have_account' | translate }}
          <a (click)="login()" class="font-medium text-blue-600 hover:text-blue-500 cursor-pointer">
            {{ 'signup.login' | translate }}
          </a>
        </p>
      </div>
    </form>
  </div>
</div>
