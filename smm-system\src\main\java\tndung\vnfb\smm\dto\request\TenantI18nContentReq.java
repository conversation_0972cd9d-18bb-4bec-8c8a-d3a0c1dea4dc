package tndung.vnfb.smm.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Map;

@Data
public class TenantI18nContentReq {

    @NotBlank(message = "Language code is required")
    @Size(max = 10, message = "Language code must not exceed 10 characters")
    private String languageCode;

    @NotNull(message = "Translations cannot be null")
    private Map<String, Object> translations;

    private String description;
}
