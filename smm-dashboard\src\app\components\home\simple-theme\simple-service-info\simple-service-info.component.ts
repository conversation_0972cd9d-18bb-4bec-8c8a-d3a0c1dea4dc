import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconsModule } from '../../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { SecondsToDurationPipe } from '../../../../core/pipes/seconds-to-duration.pipe';
import { BaseServiceInfoComponent } from '../../service-info/base-service-info.component';
import { ServiceSelectionService } from '../../../../core/services/service-selection.service';
import { SimpleFavoriteServicesComponent } from '../simple-favorite-services/simple-favorite-services.component';

@Component({
  selector: 'app-simple-service-info',
  standalone: true,
  imports: [CommonModule, IconsModule, TranslateModule, SimpleFavoriteServicesComponent, SecondsToDurationPipe],
  templateUrl: './simple-service-info.component.html',
  styleUrls: ['./simple-service-info.component.css']
})
export class SimpleServiceInfoComponent extends BaseServiceInfoComponent {

  constructor(serviceSelectionService: ServiceSelectionService) {
    super(serviceSelectionService);
  }
}
