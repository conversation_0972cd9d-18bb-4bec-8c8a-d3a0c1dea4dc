/* Simple Mess Order Theme Styles */
.simple-mess-order-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

/* Header Section */
.order-header {
  margin-bottom: 30px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-title {
  flex: 1;
}

.title-text {
  font-size: 28px;
  font-weight: 700;
  color: white;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title-description {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  line-height: 1.5;
}

/* Theme Switcher */
.theme-switcher {
  flex-shrink: 0;
}

.switcher-container {
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  padding: 4px;
  gap: 4px;
}

.switcher-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 50px;
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.switcher-btn:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.switcher-btn.active {
  background: white;
  color: #667eea;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.switcher-icon {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

/* Order Content */
.order-content {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.content-wrapper {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 0;
  min-height: 600px;
}

.order-component-container {
  padding: 40px;
  border-right: 1px solid #f0f0f0;
}

/* Help Section */
.help-section {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  padding: 40px 30px;
}

.help-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.help-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.help-icon {
  width: 24px;
  height: 24px;
  color: #667eea;
  flex-shrink: 0;
}

.help-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.help-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.help-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.help-label {
  font-size: 13px;
  font-weight: 600;
  color: #667eea;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.help-code {
  background: #f8f9ff;
  border: 1px solid #e0e7ff;
  border-radius: 8px;
  padding: 8px 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #4338ca;
  word-break: break-all;
}

.help-text {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .content-wrapper {
    grid-template-columns: 1fr;
  }
  
  .order-component-container {
    border-right: none;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .help-section {
    padding: 30px;
  }
}

@media (max-width: 768px) {
  .simple-mess-order-container {
    padding: 15px;
  }
  
  .header-content {
    flex-direction: column;
    align-items: stretch;
    padding: 20px;
  }
  
  .title-text {
    font-size: 24px;
  }
  
  .switcher-container {
    align-self: stretch;
  }
  
  .switcher-btn {
    flex: 1;
    justify-content: center;
  }
  
  .order-component-container {
    padding: 20px;
  }
  
  .help-section {
    padding: 20px;
  }
  
  .help-card {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .switcher-btn span {
    display: none;
  }
  
  .switcher-btn {
    padding: 12px;
  }
}
