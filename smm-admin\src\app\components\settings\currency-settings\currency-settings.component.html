<!-- Currency Settings Page -->
<div class="currency-settings-container">
  <!-- Header Section -->
  <div class="header-section bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
    <div class="flex items-center justify-between mb-4">
      <div>
        <h2 class="text-2xl font-bold text-gray-900">{{ 'Currency Management' | translate }}</h2>
        <p class="text-sm text-gray-600">{{ 'Manage currencies for your tenant and configure sync settings' | translate }}</p>
      </div>
      <div class="flex gap-3">
        <button
          class="btn-secondary flex items-center gap-2"
          (click)="toggleCurrencySelector()"
        >
          <fa-icon [icon]="['fas', 'plus']"></fa-icon>
          {{ 'Add Currency' | translate }}
        </button>
        <button
          class="btn-primary flex items-center gap-2"
          (click)="triggerSync()"
          [disabled]="isSyncing"
        >
          <fa-icon [icon]="['fas', 'sync-alt']" [class.animate-spin]="isSyncing"></fa-icon>
          {{ isSyncing ? ('Updating...' | translate) : ('Update Rates' | translate) }}
        </button>
      </div>
    </div>

    <!-- Last Sync Info -->
    <div class="text-sm text-gray-600" *ngIf="tenantCurrencies?.last_currency_sync">
      <fa-icon [icon]="['fas', 'clock']" class="mr-1"></fa-icon>
      {{ 'Last synced' | translate }}: {{ formatLastSync(tenantCurrencies?.last_currency_sync) }}
    </div>

    <!-- Base Currency Notice -->
    <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
      <div class="flex items-start">
        <fa-icon [icon]="['fas', 'info-circle']" class="text-blue-600 mt-0.5 mr-2"></fa-icon>
        <div class="text-sm text-blue-800">
          <p class="font-medium">{{ 'Currency Sync Information' | translate }}</p>
          <p>{{ 'Base currency (USD) cannot be modified or removed. When sync is disabled, you can set custom rates. "Update Rates" copies latest rates from background sync job to avoid API rate limits.' | translate }}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Search Section -->
  <div class="search-section mb-4">
    <div class="relative">
      <fa-icon [icon]="['fas', 'search']" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></fa-icon>
      <input
        type="text"
        class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        [(ngModel)]="searchText"
        placeholder="{{ 'Search currencies...' | translate }}"
      >
    </div>
  </div>

  <!-- Currency Table -->
  <div class="currency-table-section bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-semibold text-gray-900">{{ 'Tenant Currencies' | translate }}</h3>
      <p class="text-sm text-gray-600">{{ 'Manage currency rates and sync settings for your tenant' | translate }}</p>
    </div>

    <div class="overflow-x-auto">
      <table class="w-full">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {{ 'Currency' | translate }}
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {{ 'Exchange Rate' | translate }}
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {{ 'Currency Sync' | translate }}
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {{ 'Payment Sync' | translate }}
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {{ 'Actions' | translate }}
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr *ngFor="let currency of filteredAvailableCurrencies" class="hover:bg-gray-50">
            <!-- Currency Info -->
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span class="text-xs font-bold text-blue-600">{{ currency.code.substring(0, 2) }}</span>
                </div>
                <div class="ml-3">
                  <div class="text-sm font-medium text-gray-900">{{ currency.code }}</div>
                  <div class="text-sm text-gray-500">{{ currency.name }}</div>
                </div>
                <span class="ml-2 text-sm text-gray-400" *ngIf="currency.symbol">{{ currency.symbol }}</span>
                <span class="ml-2 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full" *ngIf="currency.base_currency">
                  {{ 'Base' | translate }}
                </span>
              </div>
            </td>

            <!-- Exchange Rate -->
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <input
                  type="number"
                  step="0.0001"
                  class="w-24 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  [value]="editingRates[currency.code]"
                  (input)="editingRates[currency.code] = +$any($event.target).value"
                  (blur)="updateExchangeRate(currency)"
                  [disabled]="!canEditRate(currency) || currency.base_currency"
                  [class.bg-gray-100]="!canEditRate(currency) || currency.base_currency"
                  [class.border-orange-300]="getCurrencySettings(currency.code)?.custom_rate && !getCurrencySettings(currency.code)?.sync_enabled"
                >
                <fa-icon
                  [icon]="['fas', 'lock']"
                  class="ml-2 text-gray-400 text-xs"
                  *ngIf="!canEditRate(currency) || currency.base_currency"
                  title="{{ currency.base_currency ? ('Base currency rate is system managed' | translate) : ('Rate is automatically synced' | translate) }}"
                ></fa-icon>
                <fa-icon
                  [icon]="['fas', 'edit']"
                  class="ml-2 text-orange-500 text-xs"
                  *ngIf="getCurrencySettings(currency.code)?.custom_rate && !getCurrencySettings(currency.code)?.sync_enabled"
                  title="{{ 'Custom rate set' | translate }}"
                ></fa-icon>
              </div>
            </td>

            <!-- Currency Sync Toggle -->
            <td class="px-6 py-4 whitespace-nowrap">
              <div *ngIf="!currency.base_currency">
                <label class="toggle-switch">
                  <input
                    type="checkbox"
                    [checked]="getCurrencySettings(currency.code)?.sync_enabled ?? true"
                    (change)="toggleCurrencySync(currency)"
                  >
                  <span class="toggle-slider"></span>
                </label>
              </div>
              <div *ngIf="currency.base_currency" class="flex items-center text-sm text-gray-500">
                <fa-icon [icon]="['fas', 'check-circle']" class="text-green-500 mr-1"></fa-icon>
                {{ 'System managed' | translate }}
              </div>
            </td>

            <!-- Payment Sync Toggle -->
            <td class="px-6 py-4 whitespace-nowrap">
              <div *ngIf="!currency.base_currency">
                <label class="toggle-switch">
                  <input
                    type="checkbox"
                    [checked]="getCurrencySettings(currency.code)?.payment_sync_enabled ?? false"
                    (change)="togglePaymentSync(currency)"
                  >
                  <span class="toggle-slider"></span>
                </label>
              </div>
              <div *ngIf="currency.base_currency" class="flex items-center text-sm text-gray-500">
                <fa-icon [icon]="['fas', 'check-circle']" class="text-green-500 mr-1"></fa-icon>
                {{ 'System managed' | translate }}
              </div>
            </td>

            <!-- Actions -->
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="flex items-center gap-2">
                <!-- <button
                  class="text-blue-600 hover:text-blue-900"
                  (click)="updateExchangeRate(currency)"
                  *ngIf="canEditRate(currency) && !currency.base_currency"
                >
                  {{ 'Update Rate' | translate }}
                </button> -->
                <button
                  class="text-red-600 hover:text-red-900"
                  (click)="removeCurrency(currency)"
                  *ngIf="!currency.base_currency"
                >
                  {{ 'Remove' | translate }}
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Loading State -->
    <div class="flex items-center justify-center py-8" *ngIf="isLoading">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <span class="ml-3 text-gray-600">{{ 'Loading currencies...' | translate }}</span>
    </div>

    <!-- Empty State -->
    <div class="text-center py-8" *ngIf="!isLoading && filteredAvailableCurrencies.length === 0">
      <fa-icon [icon]="['fas', 'coins']" class="text-gray-400 text-4xl mb-4"></fa-icon>
      <p class="text-gray-600">{{ 'No currencies found' | translate }}</p>
    </div>
  </div>

  <!-- Currency Selector Modal -->
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" *ngIf="showCurrencySelector" (click)="toggleCurrencySelector()">
    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden" (click)="$event.stopPropagation()">
      <!-- Modal Header -->
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">{{ 'Add Currency' | translate }}</h3>
            <p class="text-sm text-gray-600">{{ 'Select currencies to add to your tenant' | translate }}</p>
          </div>
          <button
            class="text-gray-400 hover:text-gray-600"
            (click)="toggleCurrencySelector()"
          >
            <fa-icon [icon]="['fas', 'times']"></fa-icon>
          </button>
        </div>
      </div>

      <!-- Search -->
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="relative">
          <fa-icon [icon]="['fas', 'search']" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></fa-icon>
          <input
            type="text"
            class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            [(ngModel)]="searchText"
            placeholder="{{ 'Search currencies...' | translate }}"
          >
        </div>
      </div>

      <!-- Currency List -->
      <div class="px-6 py-4 max-h-96 overflow-y-auto">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div
            *ngFor="let currency of filteredCurrenciesForSelection"
            class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
            (click)="addCurrency(currency)"
          >
            <div class="flex items-center">
              <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span class="text-xs font-bold text-blue-600">{{ currency.code.substring(0, 2) }}</span>
              </div>
              <div class="ml-3">
                <div class="text-sm font-medium text-gray-900">{{ currency.code }}</div>
                <div class="text-xs text-gray-500">{{ currency.name }}</div>
              </div>
            </div>
            <fa-icon [icon]="['fas', 'plus']" class="text-blue-600"></fa-icon>
          </div>
        </div>

        <!-- No currencies available -->
        <div class="text-center py-8" *ngIf="filteredCurrenciesForSelection.length === 0">
          <fa-icon [icon]="['fas', 'search']" class="text-gray-400 text-3xl mb-3"></fa-icon>
          <p class="text-gray-600">{{ 'No currencies available to add' | translate }}</p>
          <p class="text-sm text-gray-500">{{ 'All available currencies have been added to your tenant' | translate }}</p>
        </div>
      </div>
    </div>
  </div>
</div>
