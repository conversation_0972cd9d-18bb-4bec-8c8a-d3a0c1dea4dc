import { CommonModule } from '@angular/common';
import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { AdminServiceService } from '../../../core/services/admin-service.service';
import { ProviderRes } from '../../../model/response/provider-res.model';
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';
import { SMMServiceRes } from '../../../model/response/smm-service-res.model';
import { ImportServiceStep2Component } from '../import-service-step2/import-service-step2.component';
import { LiteDropdownComponent } from '../../../components/common/lite-dropdown/lite-dropdown.component';


@Component({
  selector: 'app-import-services',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconsModule,
    TranslateModule,
    ImportServiceStep2Component,
    LiteDropdownComponent
  ],
  templateUrl: './import-services.component.html',
  styleUrl: './import-services.component.css'
})
export class ImportServicesComponent implements OnInit {
  @Output() close = new EventEmitter<void>();
  @Output() servicesImported = new EventEmitter<SuperGeneralSvRes[]>();

  providers: ProviderRes[] = [];
  providerNames: string[] = [];
  selectedProviderName: string = '';
  selectedProvider: ProviderRes | null = null;
  services: SMMServiceRes[] = [];
  filteredServices: SMMServiceRes[] = [];
  searchTerm: string = '';
  selectedServices: Set<number> = new Set<number>();
  isLoading: boolean = false;

  // Step management
  currentStep: number = 1;
  selectedServicesData: SuperGeneralSvRes[] = [];

  constructor(
    private adminService: AdminServiceService
  ) {}

  ngOnInit(): void {
    this.loadProviders();
  }

  loadProviders(): void {
    this.isLoading = true;
    this.adminService.getProviders().subscribe({
      next: (providers) => {
        this.providers = providers;
        // Extract provider names for the dropdown
        this.providerNames = providers.map(provider => provider.name);

        if (providers.length > 0) {
          this.selectedProvider = providers[0];
          this.selectedProviderName = providers[0].name;
          this.loadServices(this.selectedProvider);
        }
      },
      error: (error) => {
        console.error('Error loading providers:', error);
      },
      complete: () => {
        //this.isLoading = false;
      }
    });
  }

  loadServices(provider: ProviderRes): void {
    this.isLoading = true;
    this.adminService.getProviderServices(provider.id).subscribe({
      next: (services) => {
        this.services = services;
        this.filteredServices = [...this.services];
      },
      error: (error) => {
        console.error('Error loading services:', error);
      },
      complete: () => {
        this.isLoading = false;
      }
    });
  }

  onProviderChange(providerName: string): void {
    this.selectedProviderName = providerName;
    // Find the provider object that matches the selected name
    const provider = this.providers.find(p => p.name === providerName);
    if (provider) {
      this.selectedProvider = provider;
      this.loadServices(provider);
      this.selectedServices.clear();
    }
  }

  onSearch(): void {
    if (!this.searchTerm.trim()) {
      this.filteredServices = [...this.services];
      return;
    }

    const term = this.searchTerm.toLowerCase();
    this.filteredServices = this.services.filter(service =>
      service.name.toLowerCase().includes(term) ||
      service.service.toString().includes(term)
    );
  }

  toggleServiceSelection(serviceId: number): void {
    if (this.selectedServices.has(serviceId)) {
      this.selectedServices.delete(serviceId);
    } else {
      this.selectedServices.add(serviceId);
    }
  }

  isServiceSelected(serviceId: number): boolean {
    return this.selectedServices.has(serviceId);
  }

  isAllSelected(): boolean {
    return this.filteredServices.length > 0 && this.selectedServices.size === this.filteredServices.length;
  }

  toggleSelectAll(): void {
    if (this.isAllSelected()) {
      // Deselect all
      this.selectedServices.clear();
    } else {
      // Select all
      this.filteredServices.forEach(service => {
        this.selectedServices.add(service.service);
      });
    }
  }

  selectAtLeastOneService(): boolean {
    return this.selectedServices.size > 0;
  }

  importSelectedServices(): void {
    if (!this.selectedProvider || this.selectedServices.size === 0) {
      return;
    }

    this.isLoading = true;

    // Get the selected services data to pass to step 2
    const selectedSMMServices = this.filteredServices.filter(service =>
      this.selectedServices.has(service.service)
    );

    // Convert SMMServiceRes to SuperGeneralSvRes for step 2 using the factory method
    this.selectedServicesData = selectedSMMServices.map(smmService => {
      return SuperGeneralSvRes.create({
        id: smmService.service,
        name: smmService.name,
        description: smmService.type,
        price: smmService.rate,
        min: smmService.min,
        max: smmService.max,
        refill: smmService.refill,
        original_price: smmService.rate,
        api_service_id: smmService.service.toString(),
        api_provider: this.selectedProvider!,
        status: 'ACTIVATED',
        auto_sync: true,
        sync_min_max: true,
        sync_refill: true,
        sync_cancel: true,
        sync_status: true,
        type: smmService.type,
        add_type: 'API',
        category_id: 0 // Will be set in step 2
      });
    });

    // Move to step 2 instead of immediately importing
    this.currentStep = 2;
    this.isLoading = false;
  }

  // Handle going back to step 1
  goBackToStep1(): void {
    this.currentStep = 1;
  }

  // Handle final import with configured services
  finalizeImport(configuredServices: SuperGeneralSvRes[]): void {
    this.servicesImported.emit(configuredServices);
    this.closePopup();
  }

  closePopup(): void {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent): void {
    // Prevent closing when clicking inside the modal content
    if ((event.target as HTMLElement).classList.contains('overlay-black')) {
      this.closePopup();
    }
  }
}
