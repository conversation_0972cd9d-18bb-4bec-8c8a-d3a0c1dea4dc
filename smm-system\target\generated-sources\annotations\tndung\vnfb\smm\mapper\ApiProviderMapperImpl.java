package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.constant.enums.Currency;
import tndung.vnfb.smm.dto.request.ApiProviderReq;
import tndung.vnfb.smm.dto.response.ApiProviderRes;
import tndung.vnfb.smm.entity.ApiProvider;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class ApiProviderMapperImpl implements ApiProviderMapper {

    @Override
    public ApiProvider toEntity(ApiProviderReq req) {
        if ( req == null ) {
            return null;
        }

        ApiProvider apiProvider = new ApiProvider();

        apiProvider.setSecretKey( req.getSecretKey() );
        apiProvider.setUrl( req.getUrl() );

        return apiProvider;
    }

    @Override
    public ApiProviderRes toRes(ApiProvider entity) {
        if ( entity == null ) {
            return null;
        }

        ApiProviderRes apiProviderRes = new ApiProviderRes();

        apiProviderRes.setBalance( entity.getBalance() );
        apiProviderRes.setBalanceAlert( entity.getBalanceAlert() );
        if ( entity.getCurrency() != null ) {
            apiProviderRes.setCurrency( Enum.valueOf( Currency.class, entity.getCurrency() ) );
        }
        apiProviderRes.setId( entity.getId() );
        apiProviderRes.setName( entity.getName() );
        apiProviderRes.setStatus( entity.getStatus() );
        apiProviderRes.setUrl( entity.getUrl() );

        return apiProviderRes;
    }

    @Override
    public List<ApiProviderRes> toRes(List<ApiProvider> entity) {
        if ( entity == null ) {
            return null;
        }

        List<ApiProviderRes> list = new ArrayList<ApiProviderRes>( entity.size() );
        for ( ApiProvider apiProvider : entity ) {
            list.add( toRes( apiProvider ) );
        }

        return list;
    }
}
