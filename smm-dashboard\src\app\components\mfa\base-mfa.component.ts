import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON>, AfterViewInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Observable } from 'rxjs';

// Services
import { MfaLogicService, MfaComponentState } from './services/mfa-logic.service';

@Component({
  template: '', // Base component has no template
  standalone: true
})
export class BaseMfaComponent implements OnInit, OnDestroy, AfterViewInit {
  // Expose state as observable for template
  mfaState$: Observable<MfaComponentState>;

  constructor(protected mfaLogicService: MfaLogicService) {
    this.mfaState$ = this.mfaLogicService.state$;
  }

  ngOnInit(): void {
    // MfaLogicService handles all initialization
  }

  ngAfterViewInit(): void {
    this.mfaLogicService.initializeDigitInputs();
  }

  ngOnDestroy(): void {
    this.mfaLogicService.cleanup();
  }

  // Delegate methods to MfaLogicService for template compatibility
  onSubmit(): void {
    this.mfaLogicService.onSubmit();
  }

  resendCode(): void {
    this.mfaLogicService.resendCode();
  }

  goBack(): void {
    this.mfaLogicService.goBack();
  }

  onDigitInput(event: Event, index: number): void {
    this.mfaLogicService.onDigitInput(event, index);
  }

  onDigitKeyDown(event: KeyboardEvent, index: number): void {
    this.mfaLogicService.onDigitKeyDown(event, index);
  }

  onFocus(event: FocusEvent): void {
    this.mfaLogicService.onFocus(event);
  }

  onPaste(event: ClipboardEvent): void {
    this.mfaLogicService.onPaste(event);
  }

  // Getter for form control (used in template)
  get code(): FormControl {
    return this.mfaLogicService.code;
  }
}
